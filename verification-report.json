{"timestamp": "2025-07-15T09:01:22.792Z", "summary": {"passed": 17, "failed": 1, "warnings": 2, "passRate": 85}, "details": [{"type": "pass", "message": "Vue配置包含Gzip压缩", "details": ""}, {"type": "pass", "message": "Vue配置包含代码分割", "details": ""}, {"type": "pass", "message": "Vue配置包含支付SDK分离", "details": ""}, {"type": "pass", "message": "Axios已升级到安全版本", "details": ""}, {"type": "pass", "message": "Vue已升级到2.7版本", "details": ""}, {"type": "pass", "message": "已安装Gzip压缩插件", "details": ""}, {"type": "pass", "message": "已安装图片优化插件", "details": ""}, {"type": "warn", "message": "无法运行安全审计", "details": "Command failed: npm audit --audit-level=high --json"}, {"type": "info", "message": "node_modules 大小: 306M", "details": ""}, {"type": "pass", "message": "src/utils/performance-monitor.js 存在", "details": ""}, {"type": "pass", "message": "src/utils/api-cache.js 存在", "details": ""}, {"type": "pass", "message": "src/server/http.optimized.js 存在", "details": ""}, {"type": "pass", "message": "config/service-worker.optimized.js 存在", "details": ""}, {"type": "pass", "message": "scripts/performance-test.js 存在", "details": ""}, {"type": "pass", "message": "性能监控已集成到main.js", "details": ""}, {"type": "pass", "message": "生产环境已禁用source map", "details": ""}, {"type": "pass", "message": "已配置性能预算", "details": ""}, {"type": "pass", "message": "已配置缓存组", "details": ""}, {"type": "fail", "message": "构建测试失败", "details": "Command failed: npm run build:release\n-  Building for release...\n ERROR  Error: Build failed with errors.\nError: Build failed with errors.\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/@vue/cli-service/lib/commands/build/index.js:207:23\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/webpack.js:167:8\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/HookWebpackError.js:68:2\n    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:6:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/Hook.js:20:14)\n    at Cache.shutdown (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Cache.js:156:23)\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compiler.js:1406:15\n    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:6:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/Hook.js:20:14)\n    at Compiler.close (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compiler.js:1399:23)\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/webpack.js:166:16\n    at finalCallback (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compiler.js:521:32)\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compiler.js:545:13\n    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:33:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/Hook.js:20:14)\n    at onCompiled (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compiler.js:543:21)\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compiler.js:1377:17\n    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:6:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/Hook.js:20:14)\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compiler.js:1373:33\n    at finalCallback (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compilation.js:3070:11)\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compilation.js:3413:11\n    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:6:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/Hook.js:20:14)\n    at /Users/<USER>/work/pay-web/web-pay-hub/node_modules/webpack/lib/Compilation.js:3406:38\n    at eval (eval at create (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:17:1)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n"}, {"type": "pass", "message": "JavaScript总大小: 0.00MB (目标: <0.5MB)", "details": ""}, {"type": "info", "message": "CSS总大小: 0.00KB", "details": ""}, {"type": "warn", "message": "没有发现Gzip压缩文件", "details": ""}]}