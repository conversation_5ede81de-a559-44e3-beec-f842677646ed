"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[5455],{1571:function(t,e,i){i.r(e),i.d(e,{default:function(){return d}});var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"payment-callback-page"},["completed"===t.status||"fail"===t.status?[e("div",{staticClass:"info-wrapper"},[e("div",{class:["image","image-status__"+t.status]}),e("section",[e("div",{staticClass:"title-desc"},[t._v(t._s(t.$t(t.title[t.status])))]),e("div",{staticClass:"command"},[t._v(t._s(t.$t(t.tips[t.status])))])])])]:"pending"===t.status?[e("div",{staticClass:"info-wrapper",staticStyle:{"flex-direction":"column"}},[e("div",{class:["image","image-status__"+t.status]}),e("section",[e("div",{staticClass:"title-desc"},[t._v(t._s(t.$t("cb_page_pending_desc")))]),e("div",{staticClass:"command"},[t._v(t._s(t.$t("cb_page_pending_tips")))])])])]:[t._v("Configuration Error!")]],2)},a=[],n=i(87367),o={name:"PaymentCallback",data(){return{status:this.$route.path.replace("/common/",""),title:{completed:"cb_pay_succeed",fail:"cb_page_title_err"},tips:{completed:"cb_view_tips",fail:"cb_view_err_tips"},interval:"",timeStop:!1}},methods:{clearInterval(){this.interval&&(clearInterval(this.interval),this.interval="")},getPaymentStatus(){const t=this.$route.query,e=t.foreignInvoice||t.orderId||t.OrderId,i={transaction_id:e,hideErrToast:!0};(0,n.i9)(i).then(t=>{0===t.code&&(this.$router.replace("/completed"),this.clearInterval())})},adapterStatus(){const t={fail:2,completed:1,pending:0};return t[this.status]},backGame(){const t=location.href.includes("adyen")||location.href.includes("airwallext");this.$router.replace("/"),t&&setTimeout(()=>window.location.reload(),300)},navTo(t){this.$router.replace(t).then(t=>{t.fullPath.includes("/gallery")&&window.location.reload()})},judgeStatus(){this.interval&&(clearInterval(this.interval),this.interval=""),this.$root.$emit("showPop","CallbackPendingTips")}},created(){window.fetchOrderStatus=this.adapterStatus.bind(this),"pending"===this.status&&(this.getPaymentStatus(),this.interval=setInterval(()=>{this.getPaymentStatus()},2e3),setTimeout(()=>{this.clearInterval(),this.timeStop=!0},6e4))},beforeDestroy(){this.clearInterval()}},r=o,c=i(81656),l=(0,c.A)(r,s,a,!1,null,"785cc194",null),d=l.exports},33237:function(t,e,i){i.d(e,{IN:function(){return I},fK:function(){return K},lt:function(){return U}});var s,a,n,o,r=-1,c=function(t){addEventListener("pageshow",function(e){e.persisted&&(r=e.timeStamp,t(e))},!0)},l=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},d=function(){var t=l();return t&&t.activationStart||0},u=function(t,e){var i=l(),s="navigate";return r>=0?s="back-forward-cache":i&&(document.prerendering||d()>0?s="prerender":document.wasDiscarded?s="restore":i.type&&(s=i.type.replace(/_/g,"-"))),{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:s}},g=function(t,e,i){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var s=new PerformanceObserver(function(t){Promise.resolve().then(function(){e(t.getEntries())})});return s.observe(Object.assign({type:t,buffered:!0},i||{})),s}}catch(t){}},p=function(t,e,i,s){var a,n;return function(o){e.value>=0&&(o||s)&&((n=e.value-(a||0))||void 0===a)&&(a=e.value,e.delta=n,e.rating=function(t,e){return t>e[1]?"poor":t>e[0]?"needs-improvement":"good"}(e.value,i),t(e))}},v=function(t){requestAnimationFrame(function(){return requestAnimationFrame(function(){return t()})})},h=function(t){var e=function(e){"pagehide"!==e.type&&"hidden"!==document.visibilityState||t(e)};addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0)},f=function(t){var e=!1;return function(i){e||(t(i),e=!0)}},_=-1,m=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},y=function(t){"hidden"===document.visibilityState&&_>-1&&(_="visibilitychange"===t.type?t.timeStamp:0,C())},b=function(){addEventListener("visibilitychange",y,!0),addEventListener("prerenderingchange",y,!0)},C=function(){removeEventListener("visibilitychange",y,!0),removeEventListener("prerenderingchange",y,!0)},w=function(){return _<0&&(_=m(),b(),c(function(){setTimeout(function(){_=m(),b()},0)})),{get firstHiddenTime(){return _}}},$=function(t){document.prerendering?addEventListener("prerenderingchange",function(){return t()},!0):t()},k=[1800,3e3],S=function(t,e){e=e||{},$(function(){var i,s=w(),a=u("FCP"),n=g("paint",function(t){t.forEach(function(t){"first-contentful-paint"===t.name&&(n.disconnect(),t.startTime<s.firstHiddenTime&&(a.value=Math.max(t.startTime-d(),0),a.entries.push(t),i(!0)))})});n&&(i=p(t,a,k,e.reportAllChanges),c(function(s){a=u("FCP"),i=p(t,a,k,e.reportAllChanges),v(function(){a.value=performance.now()-s.timeStamp,i(!0)})}))})},T=[.1,.25],I=function(t,e){e=e||{},S(f(function(){var i,s=u("CLS",0),a=0,n=[],o=function(t){t.forEach(function(t){if(!t.hadRecentInput){var e=n[0],i=n[n.length-1];a&&t.startTime-i.startTime<1e3&&t.startTime-e.startTime<5e3?(a+=t.value,n.push(t)):(a=t.value,n=[t])}}),a>s.value&&(s.value=a,s.entries=n,i())},r=g("layout-shift",o);r&&(i=p(t,s,T,e.reportAllChanges),h(function(){o(r.takeRecords()),i(!0)}),c(function(){a=0,s=u("CLS",0),i=p(t,s,T,e.reportAllChanges),v(function(){return i()})}),setTimeout(i,0))}))},P={passive:!0,capture:!0},L=new Date,x=function(t,e){s||(s=e,a=t,n=new Date,A(removeEventListener),E())},E=function(){if(a>=0&&a<n-L){var t={entryType:"first-input",name:s.type,target:s.target,cancelable:s.cancelable,startTime:s.timeStamp,processingStart:s.timeStamp+a};o.forEach(function(e){e(t)}),o=[]}},O=function(t){if(t.cancelable){var e=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;"pointerdown"==t.type?function(t,e){var i=function(){x(t,e),a()},s=function(){a()},a=function(){removeEventListener("pointerup",i,P),removeEventListener("pointercancel",s,P)};addEventListener("pointerup",i,P),addEventListener("pointercancel",s,P)}(e,t):x(e,t)}},A=function(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(e){return t(e,O,P)})},j=[100,300],U=function(t,e){e=e||{},$(function(){var i,n=w(),r=u("FID"),l=function(t){t.startTime<n.firstHiddenTime&&(r.value=t.processingStart-t.startTime,r.entries.push(t),i(!0))},d=function(t){t.forEach(l)},v=g("first-input",d);i=p(t,r,j,e.reportAllChanges),v&&h(f(function(){d(v.takeRecords()),v.disconnect()})),v&&c(function(){var n;r=u("FID"),i=p(t,r,j,e.reportAllChanges),o=[],a=-1,s=null,A(addEventListener),n=l,o.push(n),E()})})},F=[2500,4e3],D={},K=function(t,e){e=e||{},$(function(){var i,s=w(),a=u("LCP"),n=function(t){var e=t[t.length-1];e&&e.startTime<s.firstHiddenTime&&(a.value=Math.max(e.startTime-d(),0),a.entries=[e],i())},o=g("largest-contentful-paint",n);if(o){i=p(t,a,F,e.reportAllChanges);var r=f(function(){D[a.id]||(n(o.takeRecords()),o.disconnect(),D[a.id]=!0,i(!0))});["keydown","click"].forEach(function(t){addEventListener(t,function(){return setTimeout(r,0)},!0)}),h(r),c(function(s){a=u("LCP"),i=p(t,a,F,e.reportAllChanges),v(function(){a.value=performance.now()-s.timeStamp,D[a.id]=!0,i(!0)})})}})}},76030:function(t,e,i){i.r(e),i.d(e,{default:function(){return T}});var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"order-page-wrapper",on:{click:t.closeAllSlide}},[e("header",[e("div",{staticClass:"logo"}),e("div",{staticClass:"right"},[e("div",{staticClass:"toggle"},[e("div",{staticClass:"now-lang",on:{click:function(e){return e.stopPropagation(),t.menuToggle("showToggleLang")}}},[t._v(" "+t._s(t.langObj[t.$i18n.locale])+" "),e("i",{class:{"caret-reverse":t.showToggleLang}})]),t.showToggleLang?e("div",{staticClass:"options"},t._l(Object.entries(t.langObj),function([i,s]){return e("span",{key:i,on:{click:function(e){return t.toggleLang(i)}}},[t._v(" "+t._s(s)+" ")])}),0):t._e()]),e("div",{staticClass:"divider"}),e("div",{staticClass:"user-info"},[e("div",{staticClass:"info-container",on:{click:function(e){return e.stopPropagation(),t.menuToggle("showToggleLogin")}}},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:backgroundImage",value:t.userinfo.icon,expression:"userinfo.icon",arg:"backgroundImage"}],staticClass:"avatar"}),e("div",{class:[{"no-name":!t.userinfo.name},"name"]},[t._v(t._s(t.userinfo.name))]),e("i",{class:{"caret-reverse":t.showToggleLogin}})]),t.showToggleLogin?e("div",{staticClass:"options"},[t.userinfo.isLogin?[e("span",{on:{click:t.logOut}},[t._v(t._s(t.$t("logout")))]),t.loginToken&&!t.onlyOneRole?e("span",{class:[t.$i18n.locale],on:{click:function(e){return t.openUidListPop(!0)}}},[t._v(t._s(t.$t("switch_character")))]):t._e()]:e("span",{on:{click:function(e){return t.navToLogin(t.$i18n.locale,2031)}}},[t._v(t._s(t.$t("login")))])],2):t._e()])])]),e("div",{staticClass:"content-wrap"},[e("div",{staticClass:"content-title"},[t._v(t._s(t.$t("txt_clear_card_title")))]),e("div",{staticClass:"content-body clear-cache-wrapper"},[e("div",{staticClass:"icon"}),e("div",{staticClass:"txt"},[t._v(t._s(t.$t("txt_clear_card_desc")))]),e("div",{staticClass:"btn",on:{click:t.clearCardCache}},[t._v(t._s(t.$t("txt_clear_card_btn")))])])]),e("div",{staticClass:"content-wrap list-wrap"},[e("div",{staticClass:"content-title"},[t._v(t._s(t.$t("order-page-title")))]),e("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:()=>!t.isPc&&t.togglePage(),expression:"() => !isPc && togglePage()"}],staticClass:"content-body pc-scroll",attrs:{"infinite-scroll-distance":"100"}},[e("section",{staticClass:"order-list-wrapper"},[t._l(t.orderList,function(i,s){return[e("div",{key:s,class:["order-item",{"order-item__open":t.activeIndex===s||t.isPc}]},[e("div",{staticClass:"row-1"},[e("div",{staticClass:"order-id"},[t._v(t._s(t.$t("order-page-pay-order"))+"："+t._s(i.order_id))]),e("div",{staticClass:"order-status"},[t._v(t._s(t.$t(t.orderResultMapKey[i.order_status])))])]),e("div",{staticClass:"field"},[t._v(t._s(t.$t("order-page-pay-amount"))+"："+t._s(i.price)+" "+t._s(i.currency))]),e("div",{staticClass:"field"},[t._v(t._s(t.$t("order-page-pay-date"))+"："+t._s(i.created_at))]),e("div",{staticClass:"field"},[t._v(t._s(t.$t("order-page-pay-platform"))+"："+t._s(i.source))]),e("div",{staticClass:"field"},[t._v(t._s(t.$t("order-page-pay-method"))+"："+t._s(i.channel_name))]),e("div",{staticClass:"field"},[t._v(" "+t._s(t.$t("order-page-pay-discount"))+"： "),""===i.act_type?void 0:t._e(),"deduct"===i.act_type?[t._v(t._s(i.discount)+" "+t._s(i.currency))]:t._e(),["fixed_discount","first_pay","coupon"].includes(i.act_type)?[t._v(" "+t._s(t._f("rate")(i.discount))+" OFF")]:t._e()],2),t.isPc?t._e():e("div",{staticClass:"toggle-btn",on:{click:function(e){t.activeIndex=t.activeIndex===s?-1:s}}})])]}),t.orderList.length?t._e():e("div",{staticClass:"no-order-wrapper"},[e("div",{staticClass:"no-order-image"}),e("div",{staticClass:"no-order-txt"},[t._v(t._s(t.$t("nothingHere")))])])],2)])]),t.isPc&&t.totalPages>1?e("footer",[e("paginate",{attrs:{value:t.pageIndex,"page-count":t.totalPages,"click-handler":t.togglePage,"prev-text":"<","next-text":">","container-class":"paginate-wrapper"}})],1):t._e(),t.showToggleUidPop?e("toggle-info",{attrs:{uidList:t.uidList},on:{close:function(e){t.showToggleUidPop=!1},choose:e=>t.loadUserInfo(e)}}):t._e()],1)},a=[],n=(i(44114),i(18111),i(20116),i(85471)),o=i(96375),r=i.n(o),c=i(81721),l=i(95353),d=i(52112),u=i(87367),g=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cover-bg",on:{click:function(e){return t.$emit("close")}}},[e("section",{staticClass:"toggle-user-info",style:{transform:`translate(-50%, -50%) scale(${1.5*t.$store.state.scaleSize})`},on:{click:function(t){t.stopPropagation()}}},[e("div",{staticClass:"now-info"},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:background-image",value:t.userinfo.icon,expression:"userinfo.icon",arg:"background-image"}],staticClass:"avatar"}),t.userinfo.name?e("div",{staticClass:"id"},[e("i"),e("span",[t._v(t._s(t.userinfo.name))])]):t._e()]),e("div",{staticClass:"info-panel"},[e("div",{staticClass:"info-list"},t._l(t.uidList,function(i){return e("div",{key:i.uid,class:["info-item",{"info-item__active":t.chosenObj.uid===i.uid}],on:{click:function(e){return t.choose(i)}}},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:background-image",value:i.avatar,expression:"roleItem.avatar",arg:"background-image"}],staticClass:"avatar"}),e("div",{staticClass:"other-info"},[e("div",{staticClass:"row row-1"},[e("div",{staticClass:"id"},[t._v(t._s(i.name))]),e("div",{staticClass:"last-time"},[t._v(t._s(i.last_login))])]),e("div",{staticClass:"row row-2"},[t._v(t._s(t.$t("userinfo_level",{0:i.level}))),e("span"),t._v(t._s(t.$t("userinfo_server",{0:i.server})))])]),t.chosenObj.uid===i.uid||t.userinfo.uid===i.uid?e("div",{staticClass:"active-mark"}):t._e()])}),0),e("div",{class:["toggle-confirm",{"toggle-confirm__disable":!t.chosenObj.uid}],on:{click:t.confirmToggle}},[t._v(t._s(t.$t("switch_character")))])])])])},p=[],v=i(3003),h={name:"toggleInfo",props:["uidList"],data(){return{chosenObj:{}}},computed:{...(0,l.aH)("orderPage",["userinfo"])},methods:{choose(t){return t.uid===this.userinfo.uid?null:t.uid===this.chosenObj.uid?(this.chosenObj={},null):void(this.chosenObj=t)},confirmToggle(){this.chosenObj.uid&&(this.$emit("choose",this.chosenObj.uid),this.$emit("close"),localStorage.removeItem(v.yW))}}},f=h,_=i(81656),m=(0,_.A)(f,g,p,!1,null,"db10a1aa",null),y=m.exports,b=i(27567),C=i.n(b);n["default"].component("paginate",r());const w={1:"order-page-status-ok","-1":"order-page-status-pending"};var $={name:"OrderPage",components:{ToggleInfo:y},data(){return{activeIndex:-1,isPc:!0,orderList:[],pageSize:10,pageIndex:1,totalPages:1,showToggleLang:!1,showToggleLogin:!1,showToggleUidPop:!1,langObj:c.k,uidList:[],onlyOneRole:!0,loginToken:"",orderResultMapKey:w,busy:!1}},filters:{rate(t){return(100*(1-t)).toFixed(0)+"%"}},directives:{infiniteScroll:C()},methods:{menuToggle(t){if(this[t])return this[t]=!1,null;this.closeAllSlide(),this[t]=!0},toggleLang(t){this.$i18n.locale=t,localStorage.setItem(v.Jp,t)},closeAllSlide(){this.showToggleLogin=this.showToggleLang=!1},navToLogin:d.Ax,logOut(){localStorage.removeItem(v.yW),sessionStorage.removeItem(v.wF),localStorage.removeItem(v.wF),window.location.href=location.origin+location.pathname},openUidListPop(t){const e=()=>{this.$loading.show();const t={token:this.loginToken};(0,u.D8)(t).then(t=>{const{code:e,data:i}=t;if(sessionStorage.removeItem(v.wF),!(0===e&&i.uid_list&&i.uid_list.length>=1))throw new Error(`get error data.uid_list: ${i.uid_list}`);this.uidList=i.uid_list,1===i.uid_list.length?this.loadUserInfo(i.uid_list[0].uid):(this.onlyOneRole=!1,this.showToggleUidPop=!0)}).catch(t=>console.error(t)).finally(()=>this.$loading.hide())};return t?e():location.href.includes("token")?void e():(this.loadUserInfo(),null)},loadUserInfo(t){this.closeAllSlide();const e=this.$t("login_fail_2"),i={};t&&(i.uid=t,i.token=this.loginToken);const s=localStorage.getItem(v.yW);if(!t&&s&&(i.openid=s),"{}"===JSON.stringify(i))return null;this.$loading.show(),(0,u.uy)(i).then(i=>{let{data:a,code:n}=i;if(0===n){try{const t=this.$gcbk("ids.secretKey");"string"===typeof a&&(a=JSON.parse((0,d.VK)(a,t)))}catch(o){console.error(`解密失败！${s||this.uid}`)}this.$store.commit("orderPage/setUserInfo",a),t&&this.resetPage(),this.initList()}else this.$toast.err(e)}).catch(t=>{this.$toast.err(e),console.error(t),setTimeout(()=>this.logOut(),1500)}).finally(()=>this.$loading.hide())},resetPage(){this.orderList=[],this.orderList.length=0,this.totalPages=1,this.pageIndex=1,this.busy=!0},togglePage(t){if(this.isPc)this.pageIndex=t;else{const t=this.pageIndex+1;if(t>this.totalPages)return null;this.pageIndex=t}this.initList()},initList(){this.busy=!0;const t={p0:"web",p1:7,p2:1122,p3:"api",game:"koa",page_size:this.pageSize,page:this.pageIndex,token:localStorage.getItem(v.wF)};(0,u.AO)(t).then(e=>{const{code:i,data:s}=e;if(0===i){const{total:e,result:i=[]}=s;this.isPc?this.orderList=i:this.orderList.push(...i),this.totalPages=Math.ceil(+e/t.page_size)}}).finally(()=>{this.busy=!1})},initPage(){const{openid:t,l:e,token:i}=this.$route.query;if(e&&localStorage.setItem(v.Jp,e),i&&sessionStorage.setItem(v.wF,i),i&&localStorage.setItem(v.wF,i),i||t||e)return this.$router.replace("/order");const s=localStorage.getItem(v.yW),a=localStorage.getItem(v.Jp),n=this.loginToken=sessionStorage.getItem(v.wF),o=a||this.$i18n.locale,r=Object.keys(c.k).find(t=>t===o)||"en";if(this.toggleLang(r),n)return this.openUidListPop(!0);s&&this.loadUserInfo()},clearCardCache(){if(!this.userinfo.isLogin)return void(0,d.Ax)(this.$i18n.locale,2031);if(this.busy)return;this.busy=!0;const t={p0:"web",p1:7,p2:1145,p3:"api",game:"koa"};(0,u.V1)(t).then(t=>{0===t.code?this.$toast.err(this.$t("txt_clear_card_tips_suc")):this.$toast.err(this.$t("txt_clear_card_tips_fail"))}).finally(()=>{this.busy=!1})}},computed:{...(0,l.aH)("orderPage",["userinfo"])},created(){this.initPage();const t=()=>{this.isPc=window.innerWidth>940};t(),window.addEventListener("resize",()=>t()),this.$root.$on("bodyClick",()=>this.closeAllSlide())},mounted(){const t=document.querySelector(".pc-scroll");t&&t.addEventListener("scroll",()=>this.closeAllSlide())}},k=$,S=(0,_.A)(k,s,a,!1,null,"6b962055",null),T=S.exports},83450:function(t,e,i){i.r(e),i.d(e,{default:function(){return g}});var s=function(){var t=this,e=t._self._c;return e("div",{class:["payment-callback-page",t.$gameName],attrs:{id:"payment-callback-page"}},["completed"===t.status?[e("div",{staticClass:"info-wrapper"},[e("div",{class:["image","image-status__"+t.status]}),e("section",[e("div",{staticClass:"title-desc"},[t._v(t._s(t.$t(t.title[t.status])))]),e("div",{staticClass:"command"},[t._v(t._s(t.$t(t.tips[t.status])))])])]),e("div",{staticClass:"btn btn-back",on:{click:t.backGame}},[t._v(t._s(t.$t(t.backGameTxt)))])]:"fail"===t.status?[e("div",{staticClass:"info-wrapper"},[e("div",{class:["image","image-status__"+t.status]}),e("section",[e("div",{staticClass:"title-desc"},[t._v(t._s(t.$t(t.title[t.status])))]),e("div",{staticClass:"command"},[t._v(t._s(t.$t(t.tips[t.status])))])])]),t.$route.query.tip_msg?e("p",{staticClass:"mycard-error-tips"},[t._v(t._s(t.$route.query.tip_msg))]):t._e(),e("div",{staticClass:"btn btn-back",on:{click:t.backGame}},[t._v(t._s(t.$t(t.backGameTxt)))])]:"pending"===t.status?[e("div",{staticClass:"info-wrapper",staticStyle:{"flex-direction":"column"}},[e("div",{class:["image","image-status__"+t.status]}),e("section",[e("div",{class:["title-desc","title-desc__"+t.status]},[t._v(t._s(t.$t("cb_page_pending_desc")))]),e("div",{staticClass:"command"},[t._v(t._s(t.$t("cb_page_pending_tips")))])]),e("div",{staticClass:"btn-wrapper"},[e("div",{staticClass:"btn-status btn-status-not-pay",on:{click:function(e){return t.navTo("/")}}},[t._v(t._s(t.$t("btn_status_not_pay")))]),e("div",{staticClass:"btn-status btn-status-has-pay",on:{click:t.judgeStatus}},[t._v(t._s(t.$t("btn_status_has_pay")))])])])]:[t._v("Configuration Error!")]],2)},a=[],n=i(87367),o=i(95353),r=i(46838),c={name:"PaymentCallback",data(){return{status:this.$route.path.replace("/",""),title:{completed:"cb_pay_succeed",fail:"cb_page_title_err"},tips:{completed:"cb_view_tips",fail:"cb_view_err_tips"},interval:"",timeStop:!1}},computed:{...(0,o.aH)(["IS_CHECKOUT_SDK_V2"]),backGameTxt(){return this.IS_CHECKOUT_SDK_V2?"order-back-btn-txt":"cb_back_home"}},methods:{clearInterval(){this.interval&&(clearInterval(this.interval),this.interval="")},getPaymentStatus(){const t=this.$route.query,e=t.foreignInvoice||t.orderId||t.OrderId,i={transaction_id:e,hideErrToast:!0};(0,n.i9)(i).then(t=>{0===t.code&&(this.$router.replace("/completed"),this.clearInterval())})},adapterStatus(){const t={fail:2,completed:1,pending:0};return t[this.status]},backGame(){if(this.IS_CHECKOUT_SDK_V2)return(0,r.Z)();const t=location.href.includes("ir=ad")||location.href.includes("ir=aw")||location.href.includes("ir=cko")||location.href.includes("rf=1");this.$router.replace("/"),t&&setTimeout(()=>window.location.reload(),300)},navTo(t){this.$router.replace(t).then(t=>{t.fullPath.includes("/gallery")&&window.location.reload()})},judgeStatus(){this.interval&&(clearInterval(this.interval),this.interval=""),this.$root.$emit("showPop","CallbackPendingTips")}},created(){window.fetchOrderStatus=this.adapterStatus.bind(this),"pending"===this.status&&(this.getPaymentStatus(),this.interval=setInterval(()=>{this.getPaymentStatus()},2e3),setTimeout(()=>{this.clearInterval(),this.timeStop=!0},6e4))},mounted(){if(this.$store.state.IS_CHECKOUT_SDK&&!this.IS_CHECKOUT_SDK_V2){const t=document.querySelector(".btn-back");t.style.display="none"}},beforeDestroy(){this.clearInterval()}},l=c,d=i(81656),u=(0,d.A)(l,s,a,!1,null,"b403cf34",null),g=u.exports}}]);