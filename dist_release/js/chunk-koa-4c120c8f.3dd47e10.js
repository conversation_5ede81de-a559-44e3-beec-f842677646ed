"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[7996],{317:function(t,i,e){e.d(i,{A:function(){return _}});var a=function(){var t=this,i=t._self._c;return i("common-part",{class:["diamond-part-wrapper",t.$gameName],attrs:{id:"diamond-part-wrapper"},scopedSlots:t._u([{key:"label",fn:function(){return[i("div",{staticClass:"label-wrap"},[i("span",{staticClass:"label",on:{click:function(i){return t.$root.$emit("showWhatIsDiamondPop")}}},[t._v(" "+t._s(t.$t("charge_gear"))+" "),i("i",{staticClass:"diamond-icon"})]),t.isMobile?i("div",{staticClass:"charge-construction",on:{click:function(i){return t.$root.$emit("showPop","ChargeConstruction")}}},[i("i"),t._v(t._s(t.$t("construction_title"))+" ")]):t._e()])]},proxy:!0}])},[i("div",{staticClass:"diamond-list-wrapper"},[t._l(t.diamondList,function(e,a){return i("div",{key:e.product_id,class:["diamond-item",{"diamond-item__active":e.product_id===t.chosenDiamond.product_id},{"sold-out":e.soldOut}],on:{click:function(i){return t.toggleStatus(a)}}},[2===e.type?[i("div",{staticClass:"top"},[i("div",{staticClass:"coin-num"},[t._v(t._s(t.lastDiamondCalc.totalDiamond)+" "),i("i",{staticClass:"diamond-icon"})]),i("transition",{attrs:{name:"bonus"}},[t.showBonus?i("div",{staticClass:"bonus"},[i("div",{staticClass:"bonus-description"},[t._v("+"+t._s(t.vip.diamondBonus*t.lastDiamondCalc.totalDiamond)+" "),i("br"),t._v(" "),i("i")])]):t._e()]),i("div",{staticClass:"diamond-input-wrapper"},[i("span",{staticClass:"basic-num"},[t._v(t._s(t.lastDiamondCalc.coin))]),i("i"),t._v(" x "),i("input",{directives:[{name:"model",rawName:"v-model",value:t.chosenNum,expression:"chosenNum"}],attrs:{type:"number",disabled:""},domProps:{value:t.chosenNum},on:{input:function(i){i.target.composing||(t.chosenNum=i.target.value)}}}),i("div",{staticClass:"tips",on:{click:function(i){i.stopPropagation(),t.showPopTips=!0}}},[t.showPopTips?i("div",{staticClass:"custom-num-range-tips"},[t._v(t._s(t.$vt("customDiamondLimitTips")))]):t._e()])])],1),i("div",{staticClass:"bottom"},[i("div",{class:["now",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.diamondState(e).priceState.nowPrice))]),t.diamondState(e).priceState.originPrice?i("div",{class:["origin",{"is-ar-zone":t.isArZone}]},[i("del",[t._v(t._s(t.diamondState(e).priceState.originPrice))])]):t._e()])]:[i("div",{staticClass:"top"},[i("div",{staticClass:"coin-num"},[t._v(" "+t._s(e.coin)+" "),i("i",{staticClass:"diamond-icon"})]),i("transition",{attrs:{name:"bonus"}},[t.showBonus?i("div",{staticClass:"bonus"},[i("div",{staticClass:"bonus-description"},[t._v("+"+t._s(t.vip.diamondBonus*e.coin)+" "),i("br"),t._v(" "),i("i")])]):t._e()]),i("div",{class:["image","image_"+a]}),e.soldOut?i("div",{staticClass:"sold-out-mask"}):t._e()],1),i("div",{staticClass:"bottom"},[i("div",{class:["now",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.diamondState(e).priceState.nowPrice))]),t.diamondState(e).priceState.originPrice?i("div",{class:["origin",{"is-ar-zone":t.isArZone}]},[i("del",[t._v(t._s(t.diamondState(e).priceState.originPrice))])]):t._e()])],t.isKOA&&!t.isLogin&&t.firstPayProducts[e.product_id]?i("div",{class:["bonus","orange"]},[i("p",{staticClass:"discount"},[i("over-size-scale",[t._v("20%")])],1),i("p",{staticClass:"off"},[t._v("OFF")])]):t._e(),t.diamondState(e).bonusState.type?i("div",{staticClass:"common-bonus"},["rebate"===t.diamondState(e).bonusState.type?[i("div",{staticClass:"send"},[i("over-size-scale",[t._v(t._s(t.$t("bonus_tips")))])],1),i("div",{staticClass:"num"},[t._v(t._s(t.diamondState(e).bonusState.coin)),i("i",{staticClass:"diamond-icon"})])]:t._e(),"coupon"===t.diamondState(e).bonusState.type?[i("div",{staticClass:"discount"},[t._v(t._s(t.diamondState(e).bonusState.rate))]),i("div",{staticClass:"off"},[t._v("OFF")])]:t._e()],2):t._e(),e.total_purchase_times||e.purchased_times?i("div",{staticClass:"forbidden-by-num"},[t._v(" "+t._s(e.purchased_times)+"/"+t._s(e.total_purchase_times)+" ")]):t._e()],2)}),t.diamondList.length?t._e():i("div",{staticClass:"empty"},[t._v(t._s(t.$t("nothingHere")))])],2),"TW"===t.$store.state.country&&"ssv"===t.$gameName?i("extra-diamond"):t._e()],1)},o=[],s=(e(44114),e(18111),e(61701),e(49933)),n=e(95353),c=e(52112),r=e(87367),l=e(44374),d=e(49811);const p=window.$gcbk("ids.minCustomDiamondNum",11);var m={name:"DiamondChooseKOA",components:{ExtraDiamond:d.A,CommonPart:s.A,OverSizeScale:l.A},data(){return{diamondList:[],canICustom:!1,lastDiamondItem:{},chosenNum:p,showPopTips:!1}},computed:{...(0,n.aH)("formdata",["chosenDiamond","chosenCoupon","vip","isInit","isFirstPayUsed","chosenCoupon","chosenCouponOther","firstPayProducts"]),...(0,n.aH)("gameinfo",["defaultDiscount","isKOA"]),...(0,n.aH)("userinfo",["isLogin"]),...(0,n.aH)(["isPc","isMobile"]),...(0,n.aH)(["isArZone"]),...(0,n.L8)("formdata",["takeEffectDefaultDiscount","takeEffectDefaultRebate","FinalPriceState","isDiamondOwn95Off","getRebateCoin","isDiamondOwnRebate"]),...(0,n.aH)("functionSwitch",["smallDiamondDoubleDiscount"]),...(0,n.aH)("vb",["isDiscountUsed"]),showBonus(){return this.isInit&&this.isFirstPayUsed&&!this.chosenCoupon.FE_INDEX&&this.vip.isInit},lastDiamondCalc(){const{level_currency_price:t,currency:i,coin:e,tax_rate:a}=this.lastDiamondItem,o=Object.assign({},this.lastDiamondItem,{totalDiamond:e*this.chosenNum,chosenNum:+this.chosenNum,defaultPrice:(0,c.YK)(p*t,i),nowPrice:(0,c.YK)(this.chosenNum*t,i),nowPriceWidthTax:(0,c.YK)(t*this.chosenNum*(a||1),i)});return o.taxation=(0,c.YK)(o.nowPriceWidthTax-o.nowPrice,i),o},diamondState(){const t=this.chosenDiamond,i=this.chosenCoupon,e=(e,a,o)=>{["first_pay","discount_coupon","fixed_discount_coupon"].includes(i.feType)?(e.type="coupon",e.rate=i.rate,a.originPrice=o.level_currency_price,a.nowPrice=o.no_tax_price):"cash_coupon"===i.feType?(e.type="coupon",e.rate=`${i.deduct_price} ${t.currency_symbol}`,a.originPrice=o.level_currency_price,a.nowPrice=o.no_tax_price):["rebate_coupon","fixed_rebate","fixed_dynamic_rebate","first_pay_rebate"].includes(i.feType||o.feType)&&(e.type="rebate",e.coin=o.rate||i.rate,a.nowPrice=o.level_currency_price)};return a=>{const o={type:"",rate:"",coin:0},s={nowPrice:0,originPrice:0};if(this.firstPayProducts[a.product_id]){const t=this.firstPayProducts[a.product_id];e(o,s,t)}else if(a.product_id===t.product_id&&i.productId===a.product_id)e(o,s,i);else if(this.chosenCouponOther[a.product_id]){const t=this.chosenCouponOther[a.product_id];e(o,s,t)}else if(this.isDiamondOwn95Off(a))o.type="coupon",o.rate=`${this.$store.state.formdata.defaultDiscountInfo.rateWidthOutPercent}%`,s.originPrice=this.isDiamondOwn95Off(a).level_currency_price,s.nowPrice=this.isDiamondOwn95Off(a).no_tax_price;else if(this.isDiamondOwnRebate(a)){if(o.type="rebate",o.coin=this.getRebateCoin(a),"dc"===this.$gameName){const t=this.$store.state.formdata.defaultRebateDynamicInfoAll[a.product_id].level_coin,i=this.getRebateCoin(a)/t;o.coin=`${Math.floor(100*i)}%`}s.nowPrice=this.isDiamondOwnRebate(a).no_tax_price}else s.nowPrice=a.level_currency_price,2===a.type&&(s.nowPrice=this.lastDiamondCalc.nowPrice);const n=this.$gcbk("ids.minimumDiamondId","");if(this.smallDiamondDoubleDiscount&&a.product_id===n){const t=()=>"first_pay"!==this.chosenCoupon.feType&&(this.chosenDiamond.product_id===n&&!this.chosenCoupon.FE_INDEX||this.chosenDiamond.product_id!==n);!this.isDiscountUsed&&t()&&(o.coin=a.coin,o.type="rebate")}return s.nowPrice&&(s.nowPrice+=` ${a.currency_symbol}`),s.originPrice&&(s.originPrice+=` ${a.currency_symbol}`),this.isKOA&&(o.type=void 0),{bonusState:o,priceState:s}}}},methods:{loadDiamondList(){this.$loading.show(),(0,r.iM)({store_from:"storeFromWeb"}).then(t=>{const{data:i,code:e}=t;if(0===e){this.diamondList=i.sort((t,i)=>t.coin-i.coin),this.diamondList=this.diamondList.map((t,i)=>{const{level_currency_price:e,currency:a,tax_rate:o}=t;return t.nowPriceWidthTax=(0,c.YK)(e*(o||1),a),t.index=i,t.soldOut=(t.total_purchase_times||t.purchased_times)&&t.total_purchase_times===t.purchased_times,t}),this.initCustomParams(this.diamondList);for(const[t,e]of Object.entries(i))if(e.product_id===this.$route.query.pd)return this.$store.commit("formdata/setChosenDiamond",i[t]);if(i&&i.length){const t=this.diamondList.findIndex(t=>!t.soldOut);this.$store.commit("formdata/setChosenDiamond",i[t||0])}"ssv2"===this.$gameName&&this.initFixCouponByDiamond4(this.diamondList[4])}else this.$toast.err(this.$t("fetchChannelError"))}).finally(()=>this.$loading.hide())},toggleStatus(t){if(this.canICustom&&t===this.diamondList.length-1){if(this.lastDiamondCalc.totalDiamond===this.chosenDiamond.totalDiamond)return null;this.$root.$emit("showPop","CustomDiamond",{diamond:this.lastDiamondCalc,cb:this.toggleCustom.bind(this)})}else{const i=this.diamondList[t];if(i.soldOut)return this.$toast.err(this.$t("product-sold-out"));if(this.diamondList[t].product_id===this.chosenDiamond.product_id)return null;this.$store.commit("formdata/setChosenDiamond",this.diamondList[t])}this.$store.commit("formdata/resetChannel")},initCustomParams(t=[]){let i=-1;for(const[e,a]of Object.entries(t))if(2===a.type){i=e;break}if(-1!==i){const e=t.splice(i,1)[0];t.push(e),this.lastDiamondItem=e,this.canICustom=!0}},toggleCustom(t){t&&(this.chosenNum=t),this.$store.commit("formdata/setChosenDiamond",this.lastDiamondCalc)},initSmallDiamond(){const{p1:t,p2:i}=this.$gcbk("apiParams.smallDiamondDiscount",{}),e={p0:"web",p1:t,p2:i};(0,r.Pp)(e).then(t=>{const{code:i,data:e}=t;0===i&&this.$store.commit("vb/setIsDiscountUsed",e.is_received)}),this.judgeFirstDoubleDiscountStatus()},judgeFirstDoubleDiscountStatus(){const t={p0:"web",p1:10,p2:1144};t.p2=1144,(0,r.Pp)(t).then(t=>{const{code:i}=t;0===i&&setTimeout(()=>{this.$root.$emit("reloadActivity")},400)})},smallDiamondEvent(){this.$root.$on("couponChosen",()=>{if(this.isDiscountUsed)return null;100===this.chosenDiamond.coin&&this.$toast.err(this.$t("bonus_coupon_mutually_exclusive"))}),this.$root.$on("activityInitEnd",()=>{if(this.isDiscountUsed)return null;const t=this.chosenDiamond,i=this.chosenCoupon;100===t.coin&&["discount_coupon","cash_coupon"].includes(i.feType)&&(console.log("reset coupon!"),this.$store.commit("formdata/resetCouponInfo"))})},initFixCouponByDiamond4(t){const i=t=>(100*(1-t)).toFixed(0),e={};e.price=t.price,e.product_id=t.product_id,this.$loading.show(),(0,r.QC)(e).then(t=>{const{code:a,data:o}=t;if(0===a){let t=o.fixed_discount||[];t=t.map((t,e)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:`fixed_discount_coupon_${e}`,rateWidthOutPercent:i(t.discount)})),t.length&&this.$store.commit("formdata/setFixedCouponByProduct4",t[0]);let a=o.fixed_rebate||[];a=a.map((t,a)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:i(t.discount),rate:`${i(t.discount)}%`,FE_INDEX:`fixed_rebate_${a}`,productId:e.product_id})),a.length&&this.$store.commit("formdata/setFixedRebateByProduct4",a[0])}}).finally(()=>this.$loading.hide())}},created(){this.loadDiamondList(),this.smallDiamondDoubleDiscount&&this.smallDiamondEvent(),this.$root.$on("loginEnd",t=>{1===t&&(this.loadDiamondList(),this.smallDiamondDoubleDiscount&&this.initSmallDiamond())}),this.$root.$on("BodyClick",()=>{this.showPopTips=!1})}},h=m,u=e(81656),g=(0,u.A)(h,a,o,!1,null,"04fa89ac",null),_=g.exports},2378:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzAuZDg0OTM3NzcucG5nIjs="},4473:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8xLjYwZTVkNDM1LnBuZyI7"},7568:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8wLmM0NzBiZjFmLnBuZyI7"},13515:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2RhaWx5LXJld2FyZC1pbWFnZS4wN2UzNWNiZC5wbmciOw=="},14895:function(t){t.exports="data:image/jpeg;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luLXZhbGlkYXRlLWdhbWUtc2NycmVuY3V0LmZiYmNkYzNhLmpwZWciOw=="},16146:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8xLjYwZTVkNDM1LnBuZyI7"},20222:function(t){t.exports="data:image/jpeg;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nLzAzLjdjNzg5NTMwLmpwZWciOw=="},20380:function(t,i,e){var a=e(19822);const o=Object.create(a.H),s=Object.create(a.l);s.ckoCheckedByDefault=!0;const n={gameinfo:{gameProject:"koa_global",gameCode:"KOA",gameName:"King of Avalon",appId:"koa.global.prod",gameId:"2031",defaultDiscount:!1,whiteChannel:[],blackChannel:["android_bilibili"],greyChannel:[{channel:["ios_cn","android_cn"],to:"http://**************:10301"},{channel:["rom_global_google"],to:"http://**************:10305"}]},apiParams:{boonAme:{projectId:15,loginAction:1097,getLoginReward:1098,pwaOpenAction:1099,getPwaReward:1100}},langKey:o,images:{whatsDiamond:[{imageUrl:e(34699),jumpUrl:""},{imageUrl:e(67004),jumpUrl:""},{imageUrl:e(20222),jumpUrl:""}],uidTips:e(77637),loganFindValidationCode:e(14895)},ids:{gid:"G-LB15MXTLZK",appId:"e3Xnhny1706589599129",secretKey:"108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83"},switch:s};i.A=n},20692:function(t,i,e){e.d(i,{A:function(){return m}});var a=function(){var t=this,i=t._self._c;return i("container-v2",[i("div",{staticClass:"daily-reward-wrapper"},["box"===t.type?[i("div",{staticClass:"title"},[t._v(t._s(t.$t("title-daily-rewards")))]),i("div",{staticClass:"daily-reward-box"}),i("div",{staticClass:"btn",on:{click:t.openBox}},[t._v(t._s(t.$t("btn-open-now")))]),i("div",{staticClass:"tips"},[t._v(t._s(t.$t("subtitle-daily-rewards")))])]:t._e(),"exp"===t.type?[i("div",{staticClass:"title"},[t._v(t._s(t.$t("btn-congratulations")))]),i("div",{staticClass:"name",domProps:{innerHTML:t._s(t.$t("text-obtain-vip-exp",{0:`<span>${t.expNum}</span>`}))}}),i("div",{staticClass:"daily-reward-exp"}),i("div",{staticClass:"btn btn-exp",on:{click:t.close}},[t._v(t._s(t.$t("btn-accept")))]),i("div",{staticClass:"go-vip",on:{click:t.clickGoVip}},[t._v(t._s(t.$t("btn-check-vip")))])]:t._e(),"coupon"===t.type?[i("div",{staticClass:"title"},[t._v(t._s(t.$t("btn-congratulations")))]),i("div",{staticClass:"name"},[t._v(t._s(t.couponName))]),i("div",{class:["daily-reward-"+t.name]}),i("div",{staticClass:"btn btn-coupon",on:{click:t.close}},[t._v(t._s(t.$t("btn-accept")))])]:t._e(),i("div",{staticClass:"close",on:{click:t.close}})],2)])},o=[],s=e(34870),n=e(87367);const c={"9_day_vip_72":"text-10-off-coupons","95_day_vip_72":"text-5-off-coupons"};var r={name:"DailyReward",components:{ContainerV2:s.A},props:["option"],data(){let t="",i=0,e="",a="";if(this.option.type)t=this.option.type;else{const o=this.option.reward;e=o.item_list[0].item_name,e.includes("_exp")&&(t="exp",i=o.item_list[0].item_nums),e.includes("day_vip")&&(t="coupon",a=this.$t(c[e]))}return{type:t,name:e,expNum:i,couponName:a}},computed:{vipIntroducePageUrl(){const t={NODE_ENV:"production",VUE_APP_PROD_ENV:"RELEASE",VUE_APP_PREFIX_TOKEN_KOA:"https://koa-store-release.kingsgroup.cn/api",VUE_APP_PREFIX_API_KOA:"http://**************:8990/api",VUE_APP_PREFIX_TOKEN_KOACN:"http://**************:12001/api",VUE_APP_OlD_STORE_URL_KOA:"http://**************:10085",VUE_APP_CN_ADDRESS_KOA:"http://**************:10301",VUE_APP_ROM_ADDRESS_KOA:"http://**************:10305",VUE_APP_PREFIX_AME_KOA:"https://ame-test.funplus.com",VUE_APP_PREFIX_AME:"https://ame-test.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account-stage.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid-test.funplus.com/login",VUE_APP_VipIntroducePageUrl_koa:"http://vip-test.funplus.com.cn/koa",VUE_APP_VipIntroducePageUrl_aof:"http://vip-test.funplus.com.cn/vip",VUE_APP_VipIntroducePageUrl_rom:"http://vip-test.funplus.com.cn/rom",VUE_APP_VipIntroducePageUrl_koaCn:"http://vip-test.funplus.com.cn/koa-cn",BASE_URL:"/res/"}[`VUE_APP_VipIntroducePageUrl_${this.$gameName}`]+"?l="+this.$i18n.locale;return t}},methods:{openBox(){if(!this.$store.state.userinfo.isLogin)return this.$root.$emit("closePop"),this.$root.$emit("ClickPayButNotLogin"),null;this.$loading.show(),(0,n.Pp)({p0:"web",p1:11,p2:1422}).then(t=>{const{code:i,data:e=[]}=t;0===i&&e.length?(this.$store.commit("formdata/setDailyRewardStatus",!0),this.$root.$emit("closePop"),setTimeout(()=>{this.$root.$emit("showPop","DailyReward",{reward:e[0]})},0)):this.$toast.err(this.$t("network_err"))}).catch(()=>this.$toast.err(this.$t("network_err"))).finally(()=>this.$loading.hide())},close(){this.$root.$emit("closePop")},clickGoVip(){window.open(this.vipIntroducePageUrl,"_target")}}},l=r,d=e(81656),p=(0,d.A)(l,a,o,!1,null,"5f92c5bf",null),m=p.exports},21217:function(t,i,e){var a=e(19822);const o=Object.create(a.H);o.pageTitle="koa-official-website-title",o.customDiamondLimitTips="custom-diamond-limit-koa";const s=Object.create(a.l);s.showMobilePolicy=!1;const n={gameinfo:{gameProject:"koa_global",gameCode:"KOA",gameName:"King of Avalon",appId:"koa.global.prod",gameId:"2031",defaultDiscount:!1,whiteChannel:["ios_cn","android_cn"],blackChannel:["android_bilibili","android_cn_newbirth","android_cn37","android_cn37union"],isCn:!0},apiParams:{boonAme:{projectId:15,loginAction:1097,getLoginReward:1098,pwaOpenAction:1099,getPwaReward:1100}},langKey:o,images:{whatsDiamond:[],uidTips:e(77637),loganFindValidationCode:e(14895)},ids:{secretKey:"108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83"},switch:s};i.A=n},26464:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8zLmQ0NGEzNjg0LnBuZyI7"},30886:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzMuMDVlOWE5NjAucG5nIjs="},32605:function(t,i,e){e.r(i),e.d(i,{default:function(){return d}});var a=function(){var t=this,i=t._self._c;return i("div",{staticClass:"footer-wrapper"},[i("div",{staticClass:"copyright"},[t.isMobile?t._e():i("img",{staticClass:"logo",staticStyle:{"vertical-align":"text-bottom","padding-right":"10px"},attrs:{src:e(35895),alt:"funplus"}}),t._m(0)])])},o=[function(){var t=this,i=t._self._c;return i("div",[i("p",{staticClass:"links-p"},[i("a",{staticClass:"links",attrs:{href:"https://nenglianghe.cn/compliance/privacyAgreement.html",target:"_blank"}},[t._v("隐私政策")]),i("a",{staticClass:"links",attrs:{href:"https://nenglianghe.cn/compliance/userAgreement.html",target:"_blank"}},[t._v("用户协议")]),i("a",{staticClass:"links",attrs:{href:"https://nenglianghe.cn/compliance/children.html",target:"_blank"}},[t._v("儿童个人信息保护政策")]),i("a",{staticClass:"links",attrs:{href:"http://koa.nenglianghe.cn/hook/",target:"_blank"}},[t._v("防沉迷")])]),i("p",[t._v("网站备案/许可证号: "),i("a",{attrs:{href:"https://beian.miit.gov.cn/",target:"_blank"}},[t._v("京ICP备16053236号-11")])]),i("p",[i("img",{staticClass:"gongan",attrs:{src:e(79574),alt:""}}),i("a",{attrs:{href:"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502051888",target:"_blank"}},[t._v("京公网安备 11010502051888号")])])])}],s=e(95353),n={name:"CommonFooter",computed:{...(0,s.aH)(["isMobile"])}},c=n,r=e(81656),l=(0,r.A)(c,a,o,!1,null,"0690a763",null),d=l.exports},34699:function(t){t.exports="data:image/jpeg;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nLzAxLjkxZmZkNjAyLmpwZyI7"},39938:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8yLmExYTBmZGVhLnBuZyI7"},41235:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzEuOTEyNWE3NWEucG5nIjs="},41303:function(t){t.exports="data:image/png;base64,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"},51223:function(t,i,e){e.r(i);var a=e(19822);const o=Object.create(a.H);o.pageTitle="koa-official-website-title";const s=Object.create(a.l);s.enableAnimation=!0;const n={gameinfo:{gameProject:"koa_global",gameCode:"KOA",gameId:"2031",whiteChannel:[],blackChannel:[],greyChannel:[],gameName:"King of Avalon",appGameDeepLinkIos:"https://koa-universal-link.kingsgroupgames.com",appGameDeepLinkAndroid:"com.diandian.kingofavalon://"},apiParams:{},langKey:o,images:{logoPath:"https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1749024030/img/koa-logo.825114b1.png",iconDiamond:e(41303)},ids:{gid:"G-LB15MXTLZK",appId:"e3Xnhny1706589599129",secretKey:"108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83"},switch:s};i["default"]=n},51361:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzMuMDVlOWE5NjAucG5nIjs="},61067:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8zLmQ0NGEzNjg0LnBuZyI7"},64363:function(t){t.exports="data:image/jpeg;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nLzAyLjU0YThkODYxLmpwZyI7"},65867:function(t){t.exports="data:image/jpeg;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nLzAzLjdjNzg5NTMwLmpwZWciOw=="},66744:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzIuZmQ3OWQyZTAucG5nIjs="},67004:function(t){t.exports="data:image/jpeg;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nLzAyLjU0YThkODYxLmpwZyI7"},77172:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzEuOTEyNWE3NWEucG5nIjs="},77637:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL3NhbXBsZV9rb2EuN2Y4MTYyMDcucG5nIjs="},79708:function(t){t.exports="data:image/jpeg;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nLzAxLjkxZmZkNjAyLmpwZyI7"},81289:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8yLmExYTBmZGVhLnBuZyI7"},81499:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2xvZ2luX3Jld2FyZF8wLmM0NzBiZjFmLnBuZyI7"},93298:function(t,i,e){e.r(i),e.d(i,{default:function(){return k}});var a=function(){var t=this,i=t._self._c;return i("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,t.$gameName],attrs:{title:t.$t("boon-page-title")}},[i("div",{staticClass:"divider"}),i("div",{staticClass:"tab-wrapper",class:{"tab-small":["de","sv"].includes(t.$i18n.locale)}},t._l(t.tabList,function(e,a){return i("div",{key:e.dataKey,class:["tab",{"dot-active":!(t.$data[e.dataKey]||t.$store.state.formdata[e.dataKey]),"chosen-active":t.chosenIndex===a}],on:{click:function(i){t.chosenIndex=a}}},[i("span",[t._v(t._s(t.$t(e.langKey)))])])}),0),i("Swiper",{staticClass:"my-swiper-wrapper",attrs:{options:t.swiperOptions}},[i("SwiperSlide",{key:"install"},[i("div",{staticClass:"charge-desc"},[i("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-2-title")))]),i("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),i("div",{staticClass:"gift-image"},["koa"===t.$gameName?t._l([0,1,2,3],function(t){return i("img",{key:t,attrs:{src:e(38374)(`./install_reward_${t}.png`),alt:""}})}):t._l([0,1,2,3],function(a){return i("img",{key:a,attrs:{src:e(5161)(`./${"rom"===t.$gameName?"aof":t.$gameName}/boon/install_reward_${a}.png`),alt:""}})})],2),i("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadInstall?[t.gotInstallReward?i("span",{staticClass:"forbidden"}):i("span",{staticClass:"todo click-btn",on:{click:function(i){return t.getReward(t.getPwaReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:[t.calcShowInstall?i("span",{staticClass:"click-btn",on:{click:t.install}},[t._v(t._s(t.$t("boon-task-2-add")))]):i("p",{staticClass:"browser-forbidden"},[t._v(t._s(t.$t("boon-browser-forbidden")))])]:[i("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])]],2)]),i("SwiperSlide",{key:"login"},[i("div",{staticClass:"charge-desc"},[i("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-1-title")))]),i("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),i("div",{staticClass:"gift-image"},["koa"===t.$gameName?t._l([0,1,2,3],function(t){return i("img",{key:t,attrs:{src:e(712)(`./login_reward_${t}.png`),alt:""}})}):t._l([0,1,2,3],function(a){return i("img",{key:a,attrs:{src:e(50971)(`./${"rom"===t.$gameName?"aof":t.$gameName}/boon/login_reward_${a}.png`),alt:""}})})],2),i("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadLogin?[t.gotLoginReward?i("span",{staticClass:"forbidden"}):i("span",{staticClass:"todo click-btn",on:{click:function(i){return t.getReward(t.getLoginReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:i("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-go-charge-short")))]):i("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])],2)]),i("SwiperSlide",{key:"daily-reward"},[i("div",{staticClass:"charge-desc"},[i("div",{staticClass:"row-1"},[t._v(t._s(t.$t("text-login-topup")))]),i("div",{staticClass:"row-2"},[t._v(t._s(t.$t("text-claim-daily-chest")))])]),i("div",{staticClass:"gift-image gift-image__daily-reward"},[i("img",{attrs:{src:e(13515),alt:""}})]),i("div",{staticClass:"login-reward-btn action-btn login-reward-btn__daily-reward"},[t.isLogin?[t.gotDailyReward?i("span",{class:["forbidden","forbidden__daily-reward"]}):i("span",{staticClass:"todo click-btn",on:{click:t.getDailyReward}},[t._v(t._s(t.$t("btn-open-now")))])]:i("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])],2),t.gotDailyReward?t._e():i("div",{staticClass:"tips"},[t._v("*"+t._s(t.$t("subtitle-daily-rewards-boon")))])])],1)],1)},o=[],s=(e(18111),e(22489),e(81135)),n=e(87367),c=e(24276),r=(e(13561),e(77232)),l=e.n(r),d=e(95353),p=e(52112);const{projectId:m,loginAction:h,getLoginReward:u,pwaOpenAction:g,getPwaReward:_}=window.$gcbk("apiParams.boonAme",{}),b={p0:"web",p1:m},w={500:50,2e3:200,5e3:400,1e4:750,2e4:1200,5e4:2e3};function R(){const t=window.matchMedia("(display-mode: standalone)").matches;return document.referrer.startsWith("android-app://")?"twa":navigator.standalone||t?"standalone":"browser"}const y=[{dataKey:"gotInstallReward",langKey:"boon-ss-install-title"},{dataKey:"gotLoginReward",langKey:"boon-ss-login-title"},{dataKey:"gotDailyReward",langKey:"btn-daily-rewards"}];var v={name:"BoonPop",components:{Container:s.A,Swiper:c.Swiper,SwiperSlide:c.SwiperSlide},data(){const t=this;return{chargeStatus:{},recharge:0,hadLogin:!1,gotLoginReward:!1,hadInstall:!1,gotInstallReward:!1,goTurntable:!1,deferredPrompt:window.__deferredPrompt||void 0,showMobileSafariGuide:!1,progressPercent:0,numsMap:w,chosenIndex:0,swiperInstance:void 0,swiperOptions:{autoplay:!1,on:{slideChangeTransitionStart:function(){t.chosenIndex=this.activeIndex},init:function(){t.swiperInstance=this}}},tabList:y,getLoginReward:u,getPwaReward:_,missionList:[],serverId:0,gotToggleCoupon:!0}},methods:{getImgUrl(t){},numberFormat(t){return(0,p.jq)(t)},onRecive(t){(0,n.Pp)({p0:"web",p1:71,p2:1626,id:t}).then(i=>{const{code:e}=i;if(0===e){const i=this.missionList.filter(i=>i.id===t),e=this.missionList.findIndex(i=>i.id===t);i[0].status=2,this.$set(this.missionList,e,i[0])}})},showInstallPart(){window.addEventListener("beforeinstallprompt",t=>{t.preventDefault(),this.deferredPrompt=t})},resetStatus(){const t={p0:"web",p1:m,p2:`${h},${u},${g},${_}`};this.$loading.show(),(0,n.OL)(t).then(t=>{const{data:i,code:e}=t;if(0===e){const t={};for(const e of Object.values(i))t[e.task_id]=e;this.hadLogin=h in t,this.gotLoginReward=u in t,this.hadInstall=g in t,this.gotInstallReward=_ in t}}).finally(()=>this.$loading.hide())},resetTurntable(){const t={p0:"web",p1:79,p2:1711};this.$loading.show(),(0,n.AO)(t).then(t=>{const{data:i,code:e}=t;if(0===e){const t=i.score.filter(t=>"10085"==t.score_id)[0];this.goTurntable=!(t.ticket>0||0==t.total)}}).finally(()=>this.$loading.hide())},getReward(t){const i={p2:t};this.$loading.show(),(0,n.Pp)({...i,...b}).then(i=>{const{code:e,data:a=[]}=i;0===e&&a.length&&(t===_&&(this.gotInstallReward=!0),t===u&&(this.gotLoginReward=!0))}).finally(()=>this.$loading.hide())},getDailyReward(){this.$loading.show(),(0,n.Pp)({p0:"web",p1:11,p2:1422}).then(t=>{const{code:i,data:e=[]}=t;0===i&&e.length?(this.$store.commit("formdata/setDailyRewardStatus",!0),this.$root.$emit("closePop"),setTimeout(()=>{this.$root.$emit("showPop","DailyReward",{reward:e[0]})},0)):this.$toast.err(this.$t("network_err"))}).catch(()=>this.$toast.err(this.$t("network_err"))).finally(()=>this.$loading.hide())},focusInput(){this.$root.$emit("closePop"),this.$root.$emit("ClickPayButNotLogin")},install(){this.$root.$emit("closePop"),this.deferredPrompt?(this.deferredPrompt.prompt(),this.deferredPrompt.userChoice.then(t=>{if("accepted"===t.outcome){console.log("User accepted the A2HS prompt");const t=setInterval(()=>{"standalone"===R()&&(clearInterval(t),this.$root.$emit("installSuccessful"))},1e3)}else console.log("User dismissed the A2HS prompt");this.deferredPrompt=void 0})):setTimeout(()=>{this.$root.$emit("mobileSafariGuide")},500)},goToggleActivityPage(){let t=`${{NODE_ENV:"production",VUE_APP_PROD_ENV:"RELEASE",VUE_APP_PREFIX_TOKEN_KOA:"https://koa-store-release.kingsgroup.cn/api",VUE_APP_PREFIX_API_KOA:"http://**************:8990/api",VUE_APP_PREFIX_TOKEN_KOACN:"http://**************:12001/api",VUE_APP_OlD_STORE_URL_KOA:"http://**************:10085",VUE_APP_CN_ADDRESS_KOA:"http://**************:10301",VUE_APP_ROM_ADDRESS_KOA:"http://**************:10305",VUE_APP_PREFIX_AME_KOA:"https://ame-test.funplus.com",VUE_APP_PREFIX_AME:"https://ame-test.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account-stage.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid-test.funplus.com/login",VUE_APP_VipIntroducePageUrl_koa:"http://vip-test.funplus.com.cn/koa",VUE_APP_VipIntroducePageUrl_aof:"http://vip-test.funplus.com.cn/vip",VUE_APP_VipIntroducePageUrl_rom:"http://vip-test.funplus.com.cn/rom",VUE_APP_VipIntroducePageUrl_koaCn:"http://vip-test.funplus.com.cn/koa-cn",BASE_URL:"/res/"}.VUE_APP_URL_KOA_TOGGLE_COUPON}?l=${this.$i18n.locale}`;localStorage.getItem("openid")&&(t+=`&openid=${encodeURIComponent(localStorage.getItem("openid"))}`),window.open(t,"_blank")}},computed:{...(0,d.aH)("userinfo",["isLogin"]),...(0,d.aH)(["userinfo"]),...(0,d.aH)("formdata",["gotDailyReward","koaTopupEnable"]),calcShowInstall(){const t=new(l())(navigator.userAgent),i=t.getResult(),{browser:e}=i,a="Mobile Safari"===e.name||(e.name||"").includes("Chrome")&&this.deferredPrompt;return!this.isLogin||(!!this.hadInstall||a)}},created(){this.$root.$on("loginSuccess",()=>{setTimeout(()=>this.resetStatus(),2e3)}),this.isLogin&&this.resetStatus(),this.$watch("chosenIndex",t=>{this.swiperInstance.slideTo(t,200,!1)}),this.showInstallPart()}},f=v,N=e(81656),Z=(0,N.A)(f,a,o,!1,null,"02977080",null),k=Z.exports},98301:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzAuZDg0OTM3NzcucG5nIjs="},99631:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2luc3RhbGxfcmV3YXJkXzIuZmQ3OWQyZTAucG5nIjs="}}]);