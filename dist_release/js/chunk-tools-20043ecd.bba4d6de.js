"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[8406],{30973:function(e,t,s){s.d(t,{A:function(){return ye}});var i=s(85429),r=s(21612),a=s(27125),n=s(94394),l=s(15483),o=s(30881);function d({swiper:e,on:t,emit:s}){const r=(0,i.zk)();let a=null,n=null;const l=()=>{e&&!e.destroyed&&e.initialized&&(s("beforeResize"),s("resize"))},o=()=>{e&&!e.destroyed&&e.initialized&&(a=new ResizeObserver(t=>{n=r.requestAnimationFrame(()=>{const{width:s,height:i}=e;let r=s,a=i;t.forEach(({contentBoxSize:t,contentRect:s,target:i})=>{i&&i!==e.el||(r=s?s.width:(t[0]||t).inlineSize,a=s?s.height:(t[0]||t).blockSize)}),r===s&&a===i||l()})}),a.observe(e.el))},d=()=>{n&&r.cancelAnimationFrame(n),a&&a.unobserve&&e.el&&(a.unobserve(e.el),a=null)},c=()=>{e&&!e.destroyed&&e.initialized&&s("orientationchange")};t("init",()=>{e.params.resizeObserver&&"undefined"!==typeof r.ResizeObserver?o():(r.addEventListener("resize",l),r.addEventListener("orientationchange",c))}),t("destroy",()=>{d(),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",c)})}function c({swiper:e,extendParams:t,on:s,emit:r}){const a=[],n=(0,i.zk)(),l=(e,t={})=>{const s=n.MutationObserver||n.WebkitMutationObserver,i=new s(e=>{if(1===e.length)return void r("observerUpdate",e[0]);const t=function(){r("observerUpdate",e[0])};n.requestAnimationFrame?n.requestAnimationFrame(t):n.setTimeout(t,0)});i.observe(e,{attributes:"undefined"===typeof t.attributes||t.attributes,childList:"undefined"===typeof t.childList||t.childList,characterData:"undefined"===typeof t.characterData||t.characterData}),a.push(i)},o=()=>{if(e.params.observer){if(e.params.observeParents){const t=e.$el.parents();for(let e=0;e<t.length;e+=1)l(t[e])}l(e.$el[0],{childList:e.params.observeSlideChildren}),l(e.$wrapperEl[0],{attributes:!1})}},d=()=>{a.forEach(e=>{e.disconnect()}),a.splice(0,a.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",o),s("destroy",d)}var p={on(e,t,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!==typeof t)return i;const r=s?"unshift":"push";return e.split(" ").forEach(e=>{i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][r](t)}),i},once(e,t,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!==typeof t)return i;function r(...s){i.off(e,r),r.__emitterProxy&&delete r.__emitterProxy,t.apply(i,s)}return r.__emitterProxy=t,i.on(e,r,s)},onAny(e,t){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!==typeof e)return s;const i=t?"unshift":"push";return s.eventsAnyListeners.indexOf(e)<0&&s.eventsAnyListeners[i](e),s},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const s=t.eventsAnyListeners.indexOf(e);return s>=0&&t.eventsAnyListeners.splice(s,1),t},off(e,t){const s=this;return!s.eventsListeners||s.destroyed?s:s.eventsListeners?(e.split(" ").forEach(e=>{"undefined"===typeof t?s.eventsListeners[e]=[]:s.eventsListeners[e]&&s.eventsListeners[e].forEach((i,r)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&s.eventsListeners[e].splice(r,1)})}),s):s},emit(...e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsListeners)return t;let s,i,r;"string"===typeof e[0]||Array.isArray(e[0])?(s=e[0],i=e.slice(1,e.length),r=t):(s=e[0].events,i=e[0].data,r=e[0].context||t),i.unshift(r);const a=Array.isArray(s)?s:s.split(" ");return a.forEach(e=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach(t=>{t.apply(r,[e,...i])}),t.eventsListeners&&t.eventsListeners[e]&&t.eventsListeners[e].forEach(e=>{e.apply(r,i)})}),t}};function u(){const e=this;let t,s;const i=e.$el;t="undefined"!==typeof e.params.width&&null!==e.params.width?e.params.width:i[0].clientWidth,s="undefined"!==typeof e.params.height&&null!==e.params.height?e.params.height:i[0].clientHeight,0===t&&e.isHorizontal()||0===s&&e.isVertical()||(t=t-parseInt(i.css("padding-left")||0,10)-parseInt(i.css("padding-right")||0,10),s=s-parseInt(i.css("padding-top")||0,10)-parseInt(i.css("padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(s)&&(s=0),Object.assign(e,{width:t,height:s,size:e.isHorizontal()?t:s}))}function h(){const e=this;function t(t){return e.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}function s(e,s){return parseFloat(e.getPropertyValue(t(s))||0)}const i=e.params,{$wrapperEl:r,size:n,rtlTranslate:l,wrongRTL:o}=e,d=e.virtual&&i.virtual.enabled,c=d?e.virtual.slides.length:e.slides.length,p=r.children(`.${e.params.slideClass}`),u=d?e.virtual.slides.length:p.length;let h=[];const f=[],m=[];let g=i.slidesOffsetBefore;"function"===typeof g&&(g=i.slidesOffsetBefore.call(e));let v=i.slidesOffsetAfter;"function"===typeof v&&(v=i.slidesOffsetAfter.call(e));const w=e.snapGrid.length,T=e.slidesGrid.length;let S=i.spaceBetween,b=-g,C=0,E=0;if("undefined"===typeof n)return;"string"===typeof S&&S.indexOf("%")>=0&&(S=parseFloat(S.replace("%",""))/100*n),e.virtualSize=-S,l?p.css({marginLeft:"",marginBottom:"",marginTop:""}):p.css({marginRight:"",marginBottom:"",marginTop:""}),i.centeredSlides&&i.cssMode&&((0,a.LY)(e.wrapperEl,"--swiper-centered-offset-before",""),(0,a.LY)(e.wrapperEl,"--swiper-centered-offset-after",""));const x=i.grid&&i.grid.rows>1&&e.grid;let y;x&&e.grid.initSlides(u);const M="auto"===i.slidesPerView&&i.breakpoints&&Object.keys(i.breakpoints).filter(e=>"undefined"!==typeof i.breakpoints[e].slidesPerView).length>0;for(let a=0;a<u;a+=1){y=0;const r=p.eq(a);if(x&&e.grid.updateSlide(a,r,u,t),"none"!==r.css("display")){if("auto"===i.slidesPerView){M&&(p[a].style[t("width")]="");const n=getComputedStyle(r[0]),l=r[0].style.transform,o=r[0].style.webkitTransform;if(l&&(r[0].style.transform="none"),o&&(r[0].style.webkitTransform="none"),i.roundLengths)y=e.isHorizontal()?r.outerWidth(!0):r.outerHeight(!0);else{const e=s(n,"width"),t=s(n,"padding-left"),i=s(n,"padding-right"),a=s(n,"margin-left"),l=s(n,"margin-right"),o=n.getPropertyValue("box-sizing");if(o&&"border-box"===o)y=e+a+l;else{const{clientWidth:s,offsetWidth:n}=r[0];y=e+t+i+a+l+(n-s)}}l&&(r[0].style.transform=l),o&&(r[0].style.webkitTransform=o),i.roundLengths&&(y=Math.floor(y))}else y=(n-(i.slidesPerView-1)*S)/i.slidesPerView,i.roundLengths&&(y=Math.floor(y)),p[a]&&(p[a].style[t("width")]=`${y}px`);p[a]&&(p[a].swiperSlideSize=y),m.push(y),i.centeredSlides?(b=b+y/2+C/2+S,0===C&&0!==a&&(b=b-n/2-S),0===a&&(b=b-n/2-S),Math.abs(b)<.001&&(b=0),i.roundLengths&&(b=Math.floor(b)),E%i.slidesPerGroup===0&&h.push(b),f.push(b)):(i.roundLengths&&(b=Math.floor(b)),(E-Math.min(e.params.slidesPerGroupSkip,E))%e.params.slidesPerGroup===0&&h.push(b),f.push(b),b=b+y+S),e.virtualSize+=y+S,C=y,E+=1}}if(e.virtualSize=Math.max(e.virtualSize,n)+v,l&&o&&("slide"===i.effect||"coverflow"===i.effect)&&r.css({width:`${e.virtualSize+i.spaceBetween}px`}),i.setWrapperSize&&r.css({[t("width")]:`${e.virtualSize+i.spaceBetween}px`}),x&&e.grid.updateWrapperSize(y,h,t),!i.centeredSlides){const t=[];for(let s=0;s<h.length;s+=1){let r=h[s];i.roundLengths&&(r=Math.floor(r)),h[s]<=e.virtualSize-n&&t.push(r)}h=t,Math.floor(e.virtualSize-n)-Math.floor(h[h.length-1])>1&&h.push(e.virtualSize-n)}if(0===h.length&&(h=[0]),0!==i.spaceBetween){const s=e.isHorizontal()&&l?"marginLeft":t("marginRight");p.filter((e,t)=>!i.cssMode||t!==p.length-1).css({[s]:`${S}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let e=0;m.forEach(t=>{e+=t+(i.spaceBetween?i.spaceBetween:0)}),e-=i.spaceBetween;const t=e-n;h=h.map(e=>e<0?-g:e>t?t+v:e)}if(i.centerInsufficientSlides){let e=0;if(m.forEach(t=>{e+=t+(i.spaceBetween?i.spaceBetween:0)}),e-=i.spaceBetween,e<n){const t=(n-e)/2;h.forEach((e,s)=>{h[s]=e-t}),f.forEach((e,s)=>{f[s]=e+t})}}if(Object.assign(e,{slides:p,snapGrid:h,slidesGrid:f,slidesSizesGrid:m}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){(0,a.LY)(e.wrapperEl,"--swiper-centered-offset-before",-h[0]+"px"),(0,a.LY)(e.wrapperEl,"--swiper-centered-offset-after",e.size/2-m[m.length-1]/2+"px");const t=-e.snapGrid[0],s=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(e=>e+t),e.slidesGrid=e.slidesGrid.map(e=>e+s)}if(u!==c&&e.emit("slidesLengthChange"),h.length!==w&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),f.length!==T&&e.emit("slidesGridLengthChange"),i.watchSlidesProgress&&e.updateSlidesOffset(),!d&&!i.cssMode&&("slide"===i.effect||"fade"===i.effect)){const t=`${i.containerModifierClass}backface-hidden`,s=e.$el.hasClass(t);u<=i.maxBackfaceHiddenSlides?s||e.$el.addClass(t):s&&e.$el.removeClass(t)}}function f(e){const t=this,s=[],i=t.virtual&&t.params.virtual.enabled;let a,n=0;"number"===typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const l=e=>i?t.slides.filter(t=>parseInt(t.getAttribute("data-swiper-slide-index"),10)===e)[0]:t.slides.eq(e)[0];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||(0,r.A)([])).each(e=>{s.push(e)});else for(a=0;a<Math.ceil(t.params.slidesPerView);a+=1){const e=t.activeIndex+a;if(e>t.slides.length&&!i)break;s.push(l(e))}else s.push(l(t.activeIndex));for(a=0;a<s.length;a+=1)if("undefined"!==typeof s[a]){const e=s[a].offsetHeight;n=e>n?e:n}(n||0===n)&&t.$wrapperEl.css("height",`${n}px`)}function m(){const e=this,t=e.slides;for(let s=0;s<t.length;s+=1)t[s].swiperSlideOffset=e.isHorizontal()?t[s].offsetLeft:t[s].offsetTop}function g(e=this&&this.translate||0){const t=this,s=t.params,{slides:i,rtlTranslate:a,snapGrid:n}=t;if(0===i.length)return;"undefined"===typeof i[0].swiperSlideOffset&&t.updateSlidesOffset();let l=-e;a&&(l=e),i.removeClass(s.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(let r=0;r<i.length;r+=1){const e=i[r];let o=e.swiperSlideOffset;s.cssMode&&s.centeredSlides&&(o-=i[0].swiperSlideOffset);const d=(l+(s.centeredSlides?t.minTranslate():0)-o)/(e.swiperSlideSize+s.spaceBetween),c=(l-n[0]+(s.centeredSlides?t.minTranslate():0)-o)/(e.swiperSlideSize+s.spaceBetween),p=-(l-o),u=p+t.slidesSizesGrid[r],h=p>=0&&p<t.size-1||u>1&&u<=t.size||p<=0&&u>=t.size;h&&(t.visibleSlides.push(e),t.visibleSlidesIndexes.push(r),i.eq(r).addClass(s.slideVisibleClass)),e.progress=a?-d:d,e.originalProgress=a?-c:c}t.visibleSlides=(0,r.A)(t.visibleSlides)}function v(e){const t=this;if("undefined"===typeof e){const s=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*s||0}const s=t.params,i=t.maxTranslate()-t.minTranslate();let{progress:r,isBeginning:a,isEnd:n}=t;const l=a,o=n;0===i?(r=0,a=!0,n=!0):(r=(e-t.minTranslate())/i,a=r<=0,n=r>=1),Object.assign(t,{progress:r,isBeginning:a,isEnd:n}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&t.updateSlidesProgress(e),a&&!l&&t.emit("reachBeginning toEdge"),n&&!o&&t.emit("reachEnd toEdge"),(l&&!a||o&&!n)&&t.emit("fromEdge"),t.emit("progress",r)}function w(){const e=this,{slides:t,params:s,$wrapperEl:i,activeIndex:r,realIndex:a}=e,n=e.virtual&&s.virtual.enabled;let l;t.removeClass(`${s.slideActiveClass} ${s.slideNextClass} ${s.slidePrevClass} ${s.slideDuplicateActiveClass} ${s.slideDuplicateNextClass} ${s.slideDuplicatePrevClass}`),l=n?e.$wrapperEl.find(`.${s.slideClass}[data-swiper-slide-index="${r}"]`):t.eq(r),l.addClass(s.slideActiveClass),s.loop&&(l.hasClass(s.slideDuplicateClass)?i.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index="${a}"]`).addClass(s.slideDuplicateActiveClass):i.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index="${a}"]`).addClass(s.slideDuplicateActiveClass));let o=l.nextAll(`.${s.slideClass}`).eq(0).addClass(s.slideNextClass);s.loop&&0===o.length&&(o=t.eq(0),o.addClass(s.slideNextClass));let d=l.prevAll(`.${s.slideClass}`).eq(0).addClass(s.slidePrevClass);s.loop&&0===d.length&&(d=t.eq(-1),d.addClass(s.slidePrevClass)),s.loop&&(o.hasClass(s.slideDuplicateClass)?i.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index="${o.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicateNextClass):i.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index="${o.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicateNextClass),d.hasClass(s.slideDuplicateClass)?i.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index="${d.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicatePrevClass):i.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index="${d.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicatePrevClass)),e.emitSlidesClasses()}function T(e){const t=this,s=t.rtlTranslate?t.translate:-t.translate,{slidesGrid:i,snapGrid:r,params:a,activeIndex:n,realIndex:l,snapIndex:o}=t;let d,c=e;if("undefined"===typeof c){for(let e=0;e<i.length;e+=1)"undefined"!==typeof i[e+1]?s>=i[e]&&s<i[e+1]-(i[e+1]-i[e])/2?c=e:s>=i[e]&&s<i[e+1]&&(c=e+1):s>=i[e]&&(c=e);a.normalizeSlideIndex&&(c<0||"undefined"===typeof c)&&(c=0)}if(r.indexOf(s)>=0)d=r.indexOf(s);else{const e=Math.min(a.slidesPerGroupSkip,c);d=e+Math.floor((c-e)/a.slidesPerGroup)}if(d>=r.length&&(d=r.length-1),c===n)return void(d!==o&&(t.snapIndex=d,t.emit("snapIndexChange")));const p=parseInt(t.slides.eq(c).attr("data-swiper-slide-index")||c,10);Object.assign(t,{snapIndex:d,realIndex:p,previousIndex:n,activeIndex:c}),t.emit("activeIndexChange"),t.emit("snapIndexChange"),l!==p&&t.emit("realIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&t.emit("slideChange")}function S(e){const t=this,s=t.params,i=(0,r.A)(e).closest(`.${s.slideClass}`)[0];let a,n=!1;if(i)for(let r=0;r<t.slides.length;r+=1)if(t.slides[r]===i){n=!0,a=r;break}if(!i||!n)return t.clickedSlide=void 0,void(t.clickedIndex=void 0);t.clickedSlide=i,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt((0,r.A)(i).attr("data-swiper-slide-index"),10):t.clickedIndex=a,s.slideToClickedSlide&&void 0!==t.clickedIndex&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var b={updateSize:u,updateSlides:h,updateAutoHeight:f,updateSlidesOffset:m,updateSlidesProgress:g,updateProgress:v,updateSlidesClasses:w,updateActiveIndex:T,updateClickedSlide:S};function C(e=(this.isHorizontal()?"x":"y")){const t=this,{params:s,rtlTranslate:i,translate:r,$wrapperEl:n}=t;if(s.virtualTranslate)return i?-r:r;if(s.cssMode)return r;let l=(0,a.ro)(n[0],e);return i&&(l=-l),l||0}function E(e,t){const s=this,{rtlTranslate:i,params:r,$wrapperEl:a,wrapperEl:n,progress:l}=s;let o=0,d=0;const c=0;let p;s.isHorizontal()?o=i?-e:e:d=e,r.roundLengths&&(o=Math.floor(o),d=Math.floor(d)),r.cssMode?n[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-o:-d:r.virtualTranslate||a.transform(`translate3d(${o}px, ${d}px, ${c}px)`),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?o:d;const u=s.maxTranslate()-s.minTranslate();p=0===u?0:(e-s.minTranslate())/u,p!==l&&s.updateProgress(e),s.emit("setTranslate",s.translate,t)}function x(){return-this.snapGrid[0]}function y(){return-this.snapGrid[this.snapGrid.length-1]}function M(e=0,t=this.params.speed,s=!0,i=!0,r){const n=this,{params:l,wrapperEl:o}=n;if(n.animating&&l.preventInteractionOnTransition)return!1;const d=n.minTranslate(),c=n.maxTranslate();let p;if(p=i&&e>d?d:i&&e<c?c:e,n.updateProgress(p),l.cssMode){const e=n.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-p;else{if(!n.support.smoothScroll)return(0,a.dy)({swiper:n,targetPosition:-p,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-p,behavior:"smooth"})}return!0}return 0===t?(n.setTransition(0),n.setTranslate(p),s&&(n.emit("beforeTransitionStart",t,r),n.emit("transitionEnd"))):(n.setTransition(t),n.setTranslate(p),s&&(n.emit("beforeTransitionStart",t,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(e){n&&!n.destroyed&&e.target===this&&(n.$wrapperEl[0].removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.$wrapperEl[0].removeEventListener("webkitTransitionEnd",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,s&&n.emit("transitionEnd"))}),n.$wrapperEl[0].addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.$wrapperEl[0].addEventListener("webkitTransitionEnd",n.onTranslateToWrapperTransitionEnd))),!0}var k={getTranslate:C,setTranslate:E,minTranslate:x,maxTranslate:y,translateTo:M};function P(e,t){const s=this;s.params.cssMode||s.$wrapperEl.transition(e),s.emit("setTransition",e,t)}function $({swiper:e,runCallbacks:t,direction:s,step:i}){const{activeIndex:r,previousIndex:a}=e;let n=s;if(n||(n=r>a?"next":r<a?"prev":"reset"),e.emit(`transition${i}`),t&&r!==a){if("reset"===n)return void e.emit(`slideResetTransition${i}`);e.emit(`slideChangeTransition${i}`),"next"===n?e.emit(`slideNextTransition${i}`):e.emit(`slidePrevTransition${i}`)}}function L(e=!0,t){const s=this,{params:i}=s;i.cssMode||(i.autoHeight&&s.updateAutoHeight(),$({swiper:s,runCallbacks:e,direction:t,step:"Start"}))}function z(e=!0,t){const s=this,{params:i}=s;s.animating=!1,i.cssMode||(s.setTransition(0),$({swiper:s,runCallbacks:e,direction:t,step:"End"}))}var A={setTransition:P,transitionStart:L,transitionEnd:z};function I(e=0,t=this.params.speed,s=!0,i,r){if("number"!==typeof e&&"string"!==typeof e)throw new Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof e}] given.`);if("string"===typeof e){const t=parseInt(e,10),s=isFinite(t);if(!s)throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}const n=this;let l=e;l<0&&(l=0);const{params:o,snapGrid:d,slidesGrid:c,previousIndex:p,activeIndex:u,rtlTranslate:h,wrapperEl:f,enabled:m}=n;if(n.animating&&o.preventInteractionOnTransition||!m&&!i&&!r)return!1;const g=Math.min(n.params.slidesPerGroupSkip,l);let v=g+Math.floor((l-g)/n.params.slidesPerGroup);v>=d.length&&(v=d.length-1);const w=-d[v];if(o.normalizeSlideIndex)for(let a=0;a<c.length;a+=1){const e=-Math.floor(100*w),t=Math.floor(100*c[a]),s=Math.floor(100*c[a+1]);"undefined"!==typeof c[a+1]?e>=t&&e<s-(s-t)/2?l=a:e>=t&&e<s&&(l=a+1):e>=t&&(l=a)}if(n.initialized&&l!==u){if(!n.allowSlideNext&&w<n.translate&&w<n.minTranslate())return!1;if(!n.allowSlidePrev&&w>n.translate&&w>n.maxTranslate()&&(u||0)!==l)return!1}let T;if(l!==(p||0)&&s&&n.emit("beforeSlideChangeStart"),n.updateProgress(w),T=l>u?"next":l<u?"prev":"reset",h&&-w===n.translate||!h&&w===n.translate)return n.updateActiveIndex(l),o.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),"slide"!==o.effect&&n.setTranslate(w),"reset"!==T&&(n.transitionStart(s,T),n.transitionEnd(s,T)),!1;if(o.cssMode){const e=n.isHorizontal(),s=h?w:-w;if(0===t){const t=n.virtual&&n.params.virtual.enabled;t&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),f[e?"scrollLeft":"scrollTop"]=s,t&&requestAnimationFrame(()=>{n.wrapperEl.style.scrollSnapType="",n._swiperImmediateVirtual=!1})}else{if(!n.support.smoothScroll)return(0,a.dy)({swiper:n,targetPosition:s,side:e?"left":"top"}),!0;f.scrollTo({[e?"left":"top"]:s,behavior:"smooth"})}return!0}return n.setTransition(t),n.setTranslate(w),n.updateActiveIndex(l),n.updateSlidesClasses(),n.emit("beforeTransitionStart",t,i),n.transitionStart(s,T),0===t?n.transitionEnd(s,T):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(e){n&&!n.destroyed&&e.target===this&&(n.$wrapperEl[0].removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.$wrapperEl[0].removeEventListener("webkitTransitionEnd",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(s,T))}),n.$wrapperEl[0].addEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.$wrapperEl[0].addEventListener("webkitTransitionEnd",n.onSlideToWrapperTransitionEnd)),!0}function O(e=0,t=this.params.speed,s=!0,i){if("string"===typeof e){const t=parseInt(e,10),s=isFinite(t);if(!s)throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}const r=this;let a=e;return r.params.loop&&(a+=r.loopedSlides),r.slideTo(a,t,s,i)}function G(e=this.params.speed,t=!0,s){const i=this,{animating:r,enabled:a,params:n}=i;if(!a)return i;let l=n.slidesPerGroup;"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(l=Math.max(i.slidesPerViewDynamic("current",!0),1));const o=i.activeIndex<n.slidesPerGroupSkip?1:l;if(n.loop){if(r&&n.loopPreventsSlide)return!1;i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft}return n.rewind&&i.isEnd?i.slideTo(0,e,t,s):i.slideTo(i.activeIndex+o,e,t,s)}function D(e=this.params.speed,t=!0,s){const i=this,{params:r,animating:a,snapGrid:n,slidesGrid:l,rtlTranslate:o,enabled:d}=i;if(!d)return i;if(r.loop){if(a&&r.loopPreventsSlide)return!1;i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft}const c=o?i.translate:-i.translate;function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=p(c),h=n.map(e=>p(e));let f=n[h.indexOf(u)-1];if("undefined"===typeof f&&r.cssMode){let e;n.forEach((t,s)=>{u>=t&&(e=s)}),"undefined"!==typeof e&&(f=n[e>0?e-1:e])}let m=0;if("undefined"!==typeof f&&(m=l.indexOf(f),m<0&&(m=i.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(m=m-i.slidesPerViewDynamic("previous",!0)+1,m=Math.max(m,0))),r.rewind&&i.isBeginning){const r=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(r,e,t,s)}return i.slideTo(m,e,t,s)}function B(e=this.params.speed,t=!0,s){const i=this;return i.slideTo(i.activeIndex,e,t,s)}function N(e=this.params.speed,t=!0,s,i=.5){const r=this;let a=r.activeIndex;const n=Math.min(r.params.slidesPerGroupSkip,a),l=n+Math.floor((a-n)/r.params.slidesPerGroup),o=r.rtlTranslate?r.translate:-r.translate;if(o>=r.snapGrid[l]){const e=r.snapGrid[l],t=r.snapGrid[l+1];o-e>(t-e)*i&&(a+=r.params.slidesPerGroup)}else{const e=r.snapGrid[l-1],t=r.snapGrid[l];o-e<=(t-e)*i&&(a-=r.params.slidesPerGroup)}return a=Math.max(a,0),a=Math.min(a,r.slidesGrid.length-1),r.slideTo(a,e,t,s)}function _(){const e=this,{params:t,$wrapperEl:s}=e,i="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let n,l=e.clickedIndex;if(t.loop){if(e.animating)return;n=parseInt((0,r.A)(e.clickedSlide).attr("data-swiper-slide-index"),10),t.centeredSlides?l<e.loopedSlides-i/2||l>e.slides.length-e.loopedSlides+i/2?(e.loopFix(),l=s.children(`.${t.slideClass}[data-swiper-slide-index="${n}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),(0,a.dY)(()=>{e.slideTo(l)})):e.slideTo(l):l>e.slides.length-i?(e.loopFix(),l=s.children(`.${t.slideClass}[data-swiper-slide-index="${n}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),(0,a.dY)(()=>{e.slideTo(l)})):e.slideTo(l)}else e.slideTo(l)}var H={slideTo:I,slideToLoop:O,slideNext:G,slidePrev:D,slideReset:B,slideToClosest:N,slideToClickedSlide:_};function Y(){const e=this,t=(0,i.YE)(),{params:s,$wrapperEl:a}=e,n=a.children().length>0?(0,r.A)(a.children()[0].parentNode):a;n.children(`.${s.slideClass}.${s.slideDuplicateClass}`).remove();let l=n.children(`.${s.slideClass}`);if(s.loopFillGroupWithBlank){const e=s.slidesPerGroup-l.length%s.slidesPerGroup;if(e!==s.slidesPerGroup){for(let i=0;i<e;i+=1){const e=(0,r.A)(t.createElement("div")).addClass(`${s.slideClass} ${s.slideBlankClass}`);n.append(e)}l=n.children(`.${s.slideClass}`)}}"auto"!==s.slidesPerView||s.loopedSlides||(s.loopedSlides=l.length),e.loopedSlides=Math.ceil(parseFloat(s.loopedSlides||s.slidesPerView,10)),e.loopedSlides+=s.loopAdditionalSlides,e.loopedSlides>l.length&&e.params.loopedSlidesLimit&&(e.loopedSlides=l.length);const o=[],d=[];l.each((e,t)=>{const s=(0,r.A)(e);s.attr("data-swiper-slide-index",t)});for(let i=0;i<e.loopedSlides;i+=1){const e=i-Math.floor(i/l.length)*l.length;d.push(l.eq(e)[0]),o.unshift(l.eq(l.length-e-1)[0])}for(let i=0;i<d.length;i+=1)n.append((0,r.A)(d[i].cloneNode(!0)).addClass(s.slideDuplicateClass));for(let i=o.length-1;i>=0;i-=1)n.prepend((0,r.A)(o[i].cloneNode(!0)).addClass(s.slideDuplicateClass))}function V(){const e=this;e.emit("beforeLoopFix");const{activeIndex:t,slides:s,loopedSlides:i,allowSlidePrev:r,allowSlideNext:a,snapGrid:n,rtlTranslate:l}=e;let o;e.allowSlidePrev=!0,e.allowSlideNext=!0;const d=-n[t],c=d-e.getTranslate();if(t<i){o=s.length-3*i+t,o+=i;const r=e.slideTo(o,0,!1,!0);r&&0!==c&&e.setTranslate((l?-e.translate:e.translate)-c)}else if(t>=s.length-i){o=-s.length+t+i,o+=i;const r=e.slideTo(o,0,!1,!0);r&&0!==c&&e.setTranslate((l?-e.translate:e.translate)-c)}e.allowSlidePrev=r,e.allowSlideNext=a,e.emit("loopFix")}function X(){const e=this,{$wrapperEl:t,params:s,slides:i}=e;t.children(`.${s.slideClass}.${s.slideDuplicateClass},.${s.slideClass}.${s.slideBlankClass}`).remove(),i.removeAttr("data-swiper-slide-index")}var R={loopCreate:Y,loopFix:V,loopDestroy:X};function W(e){const t=this;if(t.support.touch||!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const s="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;s.style.cursor="move",s.style.cursor=e?"grabbing":"grab"}function j(){const e=this;e.support.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="")}var F={setGrabCursor:W,unsetGrabCursor:j};function q(e,t=this){function s(t){if(!t||t===(0,i.YE)()||t===(0,i.zk)())return null;t.assignedSlot&&(t=t.assignedSlot);const r=t.closest(e);return r||t.getRootNode?r||s(t.getRootNode().host):null}return s(t)}function U(e){const t=this,s=(0,i.YE)(),n=(0,i.zk)(),l=t.touchEventsData,{params:o,touches:d,enabled:c}=t;if(!c)return;if(t.animating&&o.preventInteractionOnTransition)return;!t.animating&&o.cssMode&&o.loop&&t.loopFix();let p=e;p.originalEvent&&(p=p.originalEvent);let u=(0,r.A)(p.target);if("wrapper"===o.touchEventsTarget&&!u.closest(t.wrapperEl).length)return;if(l.isTouchEvent="touchstart"===p.type,!l.isTouchEvent&&"which"in p&&3===p.which)return;if(!l.isTouchEvent&&"button"in p&&p.button>0)return;if(l.isTouched&&l.isMoved)return;const h=!!o.noSwipingClass&&""!==o.noSwipingClass,f=e.composedPath?e.composedPath():e.path;h&&p.target&&p.target.shadowRoot&&f&&(u=(0,r.A)(f[0]));const m=o.noSwipingSelector?o.noSwipingSelector:`.${o.noSwipingClass}`,g=!(!p.target||!p.target.shadowRoot);if(o.noSwiping&&(g?q(m,u[0]):u.closest(m)[0]))return void(t.allowClick=!0);if(o.swipeHandler&&!u.closest(o.swipeHandler)[0])return;d.currentX="touchstart"===p.type?p.targetTouches[0].pageX:p.pageX,d.currentY="touchstart"===p.type?p.targetTouches[0].pageY:p.pageY;const v=d.currentX,w=d.currentY,T=o.edgeSwipeDetection||o.iOSEdgeSwipeDetection,S=o.edgeSwipeThreshold||o.iOSEdgeSwipeThreshold;if(T&&(v<=S||v>=n.innerWidth-S)){if("prevent"!==T)return;e.preventDefault()}if(Object.assign(l,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),d.startX=v,d.startY=w,l.touchStartTime=(0,a.tB)(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,o.threshold>0&&(l.allowThresholdMove=!1),"touchstart"!==p.type){let e=!0;u.is(l.focusableElements)&&(e=!1,"SELECT"===u[0].nodeName&&(l.isTouched=!1)),s.activeElement&&(0,r.A)(s.activeElement).is(l.focusableElements)&&s.activeElement!==u[0]&&s.activeElement.blur();const i=e&&t.allowTouchMove&&o.touchStartPreventDefault;!o.touchStartForcePreventDefault&&!i||u[0].isContentEditable||p.preventDefault()}t.params.freeMode&&t.params.freeMode.enabled&&t.freeMode&&t.animating&&!o.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",p)}function J(e){const t=(0,i.YE)(),s=this,n=s.touchEventsData,{params:l,touches:o,rtlTranslate:d,enabled:c}=s;if(!c)return;let p=e;if(p.originalEvent&&(p=p.originalEvent),!n.isTouched)return void(n.startMoving&&n.isScrolling&&s.emit("touchMoveOpposite",p));if(n.isTouchEvent&&"touchmove"!==p.type)return;const u="touchmove"===p.type&&p.targetTouches&&(p.targetTouches[0]||p.changedTouches[0]),h="touchmove"===p.type?u.pageX:p.pageX,f="touchmove"===p.type?u.pageY:p.pageY;if(p.preventedByNestedSwiper)return o.startX=h,void(o.startY=f);if(!s.allowTouchMove)return(0,r.A)(p.target).is(n.focusableElements)||(s.allowClick=!1),void(n.isTouched&&(Object.assign(o,{startX:h,startY:f,currentX:h,currentY:f}),n.touchStartTime=(0,a.tB)()));if(n.isTouchEvent&&l.touchReleaseOnEdges&&!l.loop)if(s.isVertical()){if(f<o.startY&&s.translate<=s.maxTranslate()||f>o.startY&&s.translate>=s.minTranslate())return n.isTouched=!1,void(n.isMoved=!1)}else if(h<o.startX&&s.translate<=s.maxTranslate()||h>o.startX&&s.translate>=s.minTranslate())return;if(n.isTouchEvent&&t.activeElement&&p.target===t.activeElement&&(0,r.A)(p.target).is(n.focusableElements))return n.isMoved=!0,void(s.allowClick=!1);if(n.allowTouchCallbacks&&s.emit("touchMove",p),p.targetTouches&&p.targetTouches.length>1)return;o.currentX=h,o.currentY=f;const m=o.currentX-o.startX,g=o.currentY-o.startY;if(s.params.threshold&&Math.sqrt(m**2+g**2)<s.params.threshold)return;if("undefined"===typeof n.isScrolling){let e;s.isHorizontal()&&o.currentY===o.startY||s.isVertical()&&o.currentX===o.startX?n.isScrolling=!1:m*m+g*g>=25&&(e=180*Math.atan2(Math.abs(g),Math.abs(m))/Math.PI,n.isScrolling=s.isHorizontal()?e>l.touchAngle:90-e>l.touchAngle)}if(n.isScrolling&&s.emit("touchMoveOpposite",p),"undefined"===typeof n.startMoving&&(o.currentX===o.startX&&o.currentY===o.startY||(n.startMoving=!0)),n.isScrolling)return void(n.isTouched=!1);if(!n.startMoving)return;s.allowClick=!1,!l.cssMode&&p.cancelable&&p.preventDefault(),l.touchMoveStopPropagation&&!l.nested&&p.stopPropagation(),n.isMoved||(l.loop&&!l.cssMode&&s.loopFix(),n.startTranslate=s.getTranslate(),s.setTransition(0),s.animating&&s.$wrapperEl.trigger("webkitTransitionEnd transitionend"),n.allowMomentumBounce=!1,!l.grabCursor||!0!==s.allowSlideNext&&!0!==s.allowSlidePrev||s.setGrabCursor(!0),s.emit("sliderFirstMove",p)),s.emit("sliderMove",p),n.isMoved=!0;let v=s.isHorizontal()?m:g;o.diff=v,v*=l.touchRatio,d&&(v=-v),s.swipeDirection=v>0?"prev":"next",n.currentTranslate=v+n.startTranslate;let w=!0,T=l.resistanceRatio;if(l.touchReleaseOnEdges&&(T=0),v>0&&n.currentTranslate>s.minTranslate()?(w=!1,l.resistance&&(n.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+n.startTranslate+v)**T)):v<0&&n.currentTranslate<s.maxTranslate()&&(w=!1,l.resistance&&(n.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-n.startTranslate-v)**T)),w&&(p.preventedByNestedSwiper=!0),!s.allowSlideNext&&"next"===s.swipeDirection&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!s.allowSlidePrev&&"prev"===s.swipeDirection&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),s.allowSlidePrev||s.allowSlideNext||(n.currentTranslate=n.startTranslate),l.threshold>0){if(!(Math.abs(v)>l.threshold||n.allowThresholdMove))return void(n.currentTranslate=n.startTranslate);if(!n.allowThresholdMove)return n.allowThresholdMove=!0,o.startX=o.currentX,o.startY=o.currentY,n.currentTranslate=n.startTranslate,void(o.diff=s.isHorizontal()?o.currentX-o.startX:o.currentY-o.startY)}l.followFinger&&!l.cssMode&&((l.freeMode&&l.freeMode.enabled&&s.freeMode||l.watchSlidesProgress)&&(s.updateActiveIndex(),s.updateSlidesClasses()),s.params.freeMode&&l.freeMode.enabled&&s.freeMode&&s.freeMode.onTouchMove(),s.updateProgress(n.currentTranslate),s.setTranslate(n.currentTranslate))}function K(e){const t=this,s=t.touchEventsData,{params:i,touches:r,rtlTranslate:n,slidesGrid:l,enabled:o}=t;if(!o)return;let d=e;if(d.originalEvent&&(d=d.originalEvent),s.allowTouchCallbacks&&t.emit("touchEnd",d),s.allowTouchCallbacks=!1,!s.isTouched)return s.isMoved&&i.grabCursor&&t.setGrabCursor(!1),s.isMoved=!1,void(s.startMoving=!1);i.grabCursor&&s.isMoved&&s.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const c=(0,a.tB)(),p=c-s.touchStartTime;if(t.allowClick){const e=d.path||d.composedPath&&d.composedPath();t.updateClickedSlide(e&&e[0]||d.target),t.emit("tap click",d),p<300&&c-s.lastClickTime<300&&t.emit("doubleTap doubleClick",d)}if(s.lastClickTime=(0,a.tB)(),(0,a.dY)(()=>{t.destroyed||(t.allowClick=!0)}),!s.isTouched||!s.isMoved||!t.swipeDirection||0===r.diff||s.currentTranslate===s.startTranslate)return s.isTouched=!1,s.isMoved=!1,void(s.startMoving=!1);let u;if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,u=i.followFinger?n?t.translate:-t.translate:-s.currentTranslate,i.cssMode)return;if(t.params.freeMode&&i.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:u});let h=0,f=t.slidesSizesGrid[0];for(let a=0;a<l.length;a+=a<i.slidesPerGroupSkip?1:i.slidesPerGroup){const e=a<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;"undefined"!==typeof l[a+e]?u>=l[a]&&u<l[a+e]&&(h=a,f=l[a+e]-l[a]):u>=l[a]&&(h=a,f=l[l.length-1]-l[l.length-2])}let m=null,g=null;i.rewind&&(t.isBeginning?g=t.params.virtual&&t.params.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(m=0));const v=(u-l[h])/f,w=h<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;if(p>i.longSwipesMs){if(!i.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(v>=i.longSwipesRatio?t.slideTo(i.rewind&&t.isEnd?m:h+w):t.slideTo(h)),"prev"===t.swipeDirection&&(v>1-i.longSwipesRatio?t.slideTo(h+w):null!==g&&v<0&&Math.abs(v)>i.longSwipesRatio?t.slideTo(g):t.slideTo(h))}else{if(!i.shortSwipes)return void t.slideTo(t.activeIndex);const e=t.navigation&&(d.target===t.navigation.nextEl||d.target===t.navigation.prevEl);e?d.target===t.navigation.nextEl?t.slideTo(h+w):t.slideTo(h):("next"===t.swipeDirection&&t.slideTo(null!==m?m:h+w),"prev"===t.swipeDirection&&t.slideTo(null!==g?g:h))}}function Q(){const e=this,{params:t,el:s}=e;if(s&&0===s.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:r,snapGrid:a}=e;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),("auto"===t.slidesPerView||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=r,e.allowSlideNext=i,e.params.watchOverflow&&a!==e.snapGrid&&e.checkOverflow()}function Z(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function ee(){const e=this,{wrapperEl:t,rtlTranslate:s,enabled:i}=e;if(!i)return;let r;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const a=e.maxTranslate()-e.minTranslate();r=0===a?0:(e.translate-e.minTranslate())/a,r!==e.progress&&e.updateProgress(s?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}let te=!1;function se(){}const ie=(e,t)=>{const s=(0,i.YE)(),{params:r,touchEvents:a,el:n,wrapperEl:l,device:o,support:d}=e,c=!!r.nested,p="on"===t?"addEventListener":"removeEventListener",u=t;if(d.touch){const t=!("touchstart"!==a.start||!d.passiveListener||!r.passiveListeners)&&{passive:!0,capture:!1};n[p](a.start,e.onTouchStart,t),n[p](a.move,e.onTouchMove,d.passiveListener?{passive:!1,capture:c}:c),n[p](a.end,e.onTouchEnd,t),a.cancel&&n[p](a.cancel,e.onTouchEnd,t)}else n[p](a.start,e.onTouchStart,!1),s[p](a.move,e.onTouchMove,c),s[p](a.end,e.onTouchEnd,!1);(r.preventClicks||r.preventClicksPropagation)&&n[p]("click",e.onClick,!0),r.cssMode&&l[p]("scroll",e.onScroll),r.updateOnWindowResize?e[u](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",Q,!0):e[u]("observerUpdate",Q,!0)};function re(){const e=this,t=(0,i.YE)(),{params:s,support:r}=e;e.onTouchStart=U.bind(e),e.onTouchMove=J.bind(e),e.onTouchEnd=K.bind(e),s.cssMode&&(e.onScroll=ee.bind(e)),e.onClick=Z.bind(e),r.touch&&!te&&(t.addEventListener("touchstart",se),te=!0),ie(e,"on")}function ae(){const e=this;ie(e,"off")}var ne={attachEvents:re,detachEvents:ae};const le=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function oe(){const e=this,{activeIndex:t,initialized:s,loopedSlides:i=0,params:r,$el:n}=e,l=r.breakpoints;if(!l||l&&0===Object.keys(l).length)return;const o=e.getBreakpoint(l,e.params.breakpointsBase,e.el);if(!o||e.currentBreakpoint===o)return;const d=o in l?l[o]:void 0,c=d||e.originalParams,p=le(e,r),u=le(e,c),h=r.enabled;p&&!u?(n.removeClass(`${r.containerModifierClass}grid ${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&u&&(n.addClass(`${r.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===r.grid.fill)&&n.addClass(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(t=>{const s=r[t]&&r[t].enabled,i=c[t]&&c[t].enabled;s&&!i&&e[t].disable(),!s&&i&&e[t].enable()});const f=c.direction&&c.direction!==r.direction,m=r.loop&&(c.slidesPerView!==r.slidesPerView||f);f&&s&&e.changeDirection(),(0,a.X$)(e.params,c);const g=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),h&&!g?e.disable():!h&&g&&e.enable(),e.currentBreakpoint=o,e.emit("_beforeBreakpoint",c),m&&s&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-i+e.loopedSlides,0,!1)),e.emit("breakpoint",c)}function de(e,t="window",s){if(!e||"container"===t&&!s)return;let r=!1;const a=(0,i.zk)(),n="window"===t?a.innerHeight:s.clientHeight,l=Object.keys(e).map(e=>{if("string"===typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1)),s=n*t;return{value:s,point:e}}return{value:e,point:e}});l.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let i=0;i<l.length;i+=1){const{point:e,value:n}=l[i];"window"===t?a.matchMedia(`(min-width: ${n}px)`).matches&&(r=e):n<=s.clientWidth&&(r=e)}return r||"max"}var ce={setBreakpoint:oe,getBreakpoint:de};function pe(e,t){const s=[];return e.forEach(e=>{"object"===typeof e?Object.keys(e).forEach(i=>{e[i]&&s.push(t+i)}):"string"===typeof e&&s.push(t+e)}),s}function ue(){const e=this,{classNames:t,params:s,rtl:i,$el:r,device:a,support:n}=e,l=pe(["initialized",s.direction,{"pointer-events":!n.touch},{"free-mode":e.params.freeMode&&s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:i},{grid:s.grid&&s.grid.rows>1},{"grid-column":s.grid&&s.grid.rows>1&&"column"===s.grid.fill},{android:a.android},{ios:a.ios},{"css-mode":s.cssMode},{centered:s.cssMode&&s.centeredSlides},{"watch-progress":s.watchSlidesProgress}],s.containerModifierClass);t.push(...l),r.addClass([...t].join(" ")),e.emitContainerClasses()}function he(){const e=this,{$el:t,classNames:s}=e;t.removeClass(s.join(" ")),e.emitContainerClasses()}var fe={addClasses:ue,removeClasses:he};function me(e,t,s,a,n,l){const o=(0,i.zk)();let d;function c(){l&&l()}const p=(0,r.A)(e).parent("picture")[0];p||e.complete&&n?c():t?(d=new o.Image,d.onload=c,d.onerror=c,a&&(d.sizes=a),s&&(d.srcset=s),t&&(d.src=t)):c()}function ge(){const e=this;function t(){"undefined"!==typeof e&&null!==e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(let s=0;s<e.imagesToLoad.length;s+=1){const i=e.imagesToLoad[s];e.loadImage(i,i.currentSrc||i.getAttribute("src"),i.srcset||i.getAttribute("srcset"),i.sizes||i.getAttribute("sizes"),!0,t)}}var ve={loadImage:me,preloadImages:ge};function we(){const e=this,{isLocked:t,params:s}=e,{slidesOffsetBefore:i}=s;if(i){const t=e.slides.length-1,s=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*i;e.isLocked=e.size>s}else e.isLocked=1===e.snapGrid.length;!0===s.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===s.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var Te={checkOverflow:we},Se={init:!0,direction:"horizontal",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopedSlidesLimit:!0,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};function be(e,t){return function(s={}){const i=Object.keys(s)[0],r=s[i];"object"===typeof r&&null!==r?(["navigation","pagination","scrollbar"].indexOf(i)>=0&&!0===e[i]&&(e[i]={auto:!0}),i in e&&"enabled"in r?(!0===e[i]&&(e[i]={enabled:!0}),"object"!==typeof e[i]||"enabled"in e[i]||(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),(0,a.X$)(t,s)):(0,a.X$)(t,s)):(0,a.X$)(t,s)}}const Ce={eventsEmitter:p,update:b,translate:k,transition:A,slide:H,loop:R,grabCursor:F,events:ne,breakpoints:ce,checkOverflow:Te,classes:fe,images:ve},Ee={};class xe{constructor(...e){let t,s;if(1===e.length&&e[0].constructor&&"Object"===Object.prototype.toString.call(e[0]).slice(8,-1)?s=e[0]:[t,s]=e,s||(s={}),s=(0,a.X$)({},s),t&&!s.el&&(s.el=t),s.el&&(0,r.A)(s.el).length>1){const e=[];return(0,r.A)(s.el).each(t=>{const i=(0,a.X$)({},s,{el:t});e.push(new xe(i))}),e}const i=this;i.__swiper__=!0,i.support=(0,n.k)(),i.device=(0,l.P)({userAgent:s.userAgent}),i.browser=(0,o.X)(),i.eventsListeners={},i.eventsAnyListeners=[],i.modules=[...i.__modules__],s.modules&&Array.isArray(s.modules)&&i.modules.push(...s.modules);const d={};i.modules.forEach(e=>{e({swiper:i,extendParams:be(s,d),on:i.on.bind(i),once:i.once.bind(i),off:i.off.bind(i),emit:i.emit.bind(i)})});const c=(0,a.X$)({},Se,d);return i.params=(0,a.X$)({},c,Ee,s),i.originalParams=(0,a.X$)({},i.params),i.passedParams=(0,a.X$)({},s),i.params&&i.params.on&&Object.keys(i.params.on).forEach(e=>{i.on(e,i.params.on[e])}),i.params&&i.params.onAny&&i.onAny(i.params.onAny),i.$=r.A,Object.assign(i,{enabled:i.params.enabled,el:t,classNames:[],slides:(0,r.A)(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return"horizontal"===i.params.direction},isVertical(){return"vertical"===i.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev,touchEvents:function(){const e=["touchstart","touchmove","touchend","touchcancel"],t=["pointerdown","pointermove","pointerup"];return i.touchEventsTouch={start:e[0],move:e[1],end:e[2],cancel:e[3]},i.touchEventsDesktop={start:t[0],move:t[1],end:t[2]},i.support.touch||!i.params.simulateTouch?i.touchEventsTouch:i.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:i.params.focusableElements,lastClickTime:(0,a.tB)(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:i.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),i.emit("_swiper"),i.params.init&&i.init(),i}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const i=s.minTranslate(),r=s.maxTranslate(),a=(r-i)*e+i;s.translateTo(a,"undefined"===typeof t?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.each(s=>{const i=e.getSlideClasses(s);t.push({slideEl:s,classNames:i}),e.emit("_slideClass",s,i)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e="current",t=!1){const s=this,{params:i,slides:r,slidesGrid:a,slidesSizesGrid:n,size:l,activeIndex:o}=s;let d=1;if(i.centeredSlides){let e,t=r[o].swiperSlideSize;for(let s=o+1;s<r.length;s+=1)r[s]&&!e&&(t+=r[s].swiperSlideSize,d+=1,t>l&&(e=!0));for(let s=o-1;s>=0;s-=1)r[s]&&!e&&(t+=r[s].swiperSlideSize,d+=1,t>l&&(e=!0))}else if("current"===e)for(let c=o+1;c<r.length;c+=1){const e=t?a[c]+n[c]-a[o]<l:a[c]-a[o]<l;e&&(d+=1)}else for(let c=o-1;c>=0;c-=1){const e=a[o]-a[c]<l;e&&(d+=1)}return d}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;function i(){const t=e.rtlTranslate?-1*e.translate:e.translate,s=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(s),e.updateActiveIndex(),e.updateSlidesClasses()}let r;s.breakpoints&&e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode&&e.params.freeMode.enabled?(i(),e.params.autoHeight&&e.updateAutoHeight()):(r=("auto"===e.params.slidesPerView||e.params.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),r||i()),s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t=!0){const s=this,i=s.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(s.$el.removeClass(`${s.params.containerModifierClass}${i}`).addClass(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.each(t=>{"vertical"===e?t.style.width="":t.style.height=""}),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.$el.addClass(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.$el.removeClass(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;const s=(0,r.A)(e||t.params.el);if(e=s[0],!e)return!1;e.swiper=t;const a=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,n=()=>{if(e&&e.shadowRoot&&e.shadowRoot.querySelector){const t=(0,r.A)(e.shadowRoot.querySelector(a()));return t.children=e=>s.children(e),t}return s.children?s.children(a()):(0,r.A)(s).children(a())};let l=n();if(0===l.length&&t.params.createElements){const e=(0,i.YE)(),a=e.createElement("div");l=(0,r.A)(a),a.className=t.params.wrapperClass,s.append(a),s.children(`.${t.params.slideClass}`).each(e=>{l.append(e)})}return Object.assign(t,{$el:s,el:e,$wrapperEl:l,wrapperEl:l[0],mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===s.css("direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===e.dir.toLowerCase()||"rtl"===s.css("direction")),wrongRTL:"-webkit-box"===l.css("display")}),!0}init(e){const t=this;if(t.initialized)return t;const s=t.mount(e);return!1===s||(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.params.loop&&t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.preloadImages&&t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t}destroy(e=!0,t=!0){const s=this,{params:i,$el:r,$wrapperEl:n,slides:l}=s;return"undefined"===typeof s.params||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),i.loop&&s.loopDestroy(),t&&(s.removeClasses(),r.removeAttr("style"),n.removeAttr("style"),l&&l.length&&l.removeClass([i.slideVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(e=>{s.off(e)}),!1!==e&&(s.$el[0].swiper=null,(0,a.oR)(s)),s.destroyed=!0),null}static extendDefaults(e){(0,a.X$)(Ee,e)}static get extendedDefaults(){return Ee}static get defaults(){return Se}static installModule(e){xe.prototype.__modules__||(xe.prototype.__modules__=[]);const t=xe.prototype.__modules__;"function"===typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(e=>xe.installModule(e)),xe):(xe.installModule(e),xe)}}Object.keys(Ce).forEach(e=>{Object.keys(Ce[e]).forEach(t=>{xe.prototype[t]=Ce[e][t]})}),xe.use([d,c]);var ye=xe}}]);