"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[1307],{443:function(t,e,s){s.r(e),s.d(e,{default:function(){return f}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"checkout-page-wrapper"},[t.isPc?e("channel-logo"):t._e(),e("div",{staticClass:"content-wrapper"},[e("channel-order",{attrs:{coin:t.initParams.coinNums,currency:t.initParams.currency_symbol,amount:t.initParams.amount,"in-debt":t.initParams.inDebt}}),e("channel-wrapper",[e("ul",{staticClass:"card-option-list"},[t.historyCard.length?t._l(t.historyCard,function(s){return e("li",{key:s.key,class:["history-card-item",{active:s.key===t.chosenIndex}],on:{click:function(e){return t.toggle(s.key)}}},[e("div",{staticClass:"card-info"},[e("span",{class:["selected-status",{active:s.key===t.chosenIndex}]}),"amex"===s.cardOrg.toLowerCase()?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/amex.bf5ac3b0.svg",alt:""}}):t._e(),"jcb"===s.cardOrg.toLowerCase()?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/jcb.ab1fb383.svg",alt:""}}):t._e(),"mastercard"===s.cardOrg.toLowerCase()?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/mastercard.1c73bade.svg",alt:""}}):t._e(),"visa"===s.cardOrg.toLowerCase()?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/visa.ccab0c13.svg",alt:""}}):t._e(),"mada"===s.cardOrg.toLowerCase()?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/mada.svg",alt:""}}):t._e(),"diners"===s.cardOrg.toLowerCase()?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/diners.svg",alt:""}}):t._e(),"discover"===s.cardOrg.toLowerCase()?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/discover.svg",alt:""}}):t._e(),e("span",{staticClass:"card-number"},[t._v("•••• "+t._s(s.cardSummery))])]),t.chosenIndex===s.key?[e("div",{staticClass:"form-row-wrapper"},[e("div",{staticClass:"form-row-item date-wrapper"},[e("span",[t._v("Expiry date")]),e("div",{staticClass:"disabled-input"},[t._v(t._s(s.cardExpiry))])]),e("div",{staticClass:"form-row-item cvc-wrapper"},[e("span",{class:[{"cvc-error":t.showCvcError}]},[t._v("CVC / CVV")]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.cvc,expression:"cvc"}],class:[{"error-cvc_input":t.showCvcError}],attrs:{placeholder:"3 or 4 digits"},domProps:{value:t.cvc},on:{input:[function(e){e.target.composing||(t.cvc=e.target.value)},t.fixCvC],focus:function(e){t.showCvcError=!1}}}),t.showCvcError?t._e():e("div",{staticClass:"cvc-find-position-wrapper",attrs:{dir:"ltr"}},["AMEX"===s.cardOrg?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_cvc_position_tips--front.9b9669cd.svg"}}):e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_cvv_position_tips--back.1457d81b.svg"}})]),t.showCvcError?[e("img",{staticClass:"error-cvc__red-no",attrs:{alt:"field_error",src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_error.5f6b2397.svg"}}),e("span",{staticClass:"error-cvc_span"},[t._v(t._s(t.$t("channel-checkout-cvc-error")))])]:t._e()],2)]),e("button",{on:{click:function(e){return t.payByHistoryCard(s)}}},[e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg",alt:"","aria-hidden":"true"}}),t._v(" Pay "+t._s(t.initParams.currency_symbol)+t._s(t.initParams.amount)+" ")])]:t._e()],2)}):t._e(),e("li",{staticClass:"new-card-item",on:{click:function(e){return t.toggle(t.newCardTxt)}}},[e("div",{staticClass:"card-info"},[e("span",{class:["selected-status",{active:t.newCardTxt===t.chosenIndex}]}),e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/card.f49547d7.svg",alt:""}}),e("span",{staticClass:"card-number"},[t._v("Credit Card")]),t.newCardTxt!==t.chosenIndex?e("div",{staticClass:"support-card-list"},[e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/jcb.ab1fb383.svg",alt:""}}),e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/mastercard.1c73bade.svg",alt:""}}),e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/visa.ccab0c13.svg",alt:""}}),e("span",[t._v("+3")])]):t._e()]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.chosenIndex===t.newCardTxt,expression:"chosenIndex === newCardTxt"}],staticClass:"new-card-wrapper"},[e("section",{staticClass:"checkout-wrapper"},[e("div",{staticClass:"inner-wrapper"},[e("div",{attrs:{id:"payments"}})])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.checkoutInstance,expression:"checkoutInstance"}],staticClass:"operation"},[e("label",[e("span",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.recordCardNum,expression:"recordCardNum"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.recordCardNum)?t._i(t.recordCardNum,null)>-1:t.recordCardNum},on:{change:function(e){var s=t.recordCardNum,i=e.target,a=!!i.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);i.checked?o<0&&(t.recordCardNum=s.concat([n])):o>-1&&(t.recordCardNum=s.slice(0,o).concat(s.slice(o+1)))}else t.recordCardNum=a}}}),t.recordCardNum?e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/duigou.9e50b57c.svg",alt:""}}):t._e()]),t._v(" "+t._s(t.$t("channel-checkout-save-card-number"))+" ")]),e("button",{attrs:{id:"submit"},on:{click:t.payByNewCard}},[e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg",alt:"","aria-hidden":"true"}}),t._v(" Pay "+t._s(t.initParams.currency_symbol)+t._s(t.initParams.amount)+" ")])])])])],2)])],1),t.isMobile?e("channel-logo"):t._e()],1)},a=[],n=(s(18111),s(7588),s(61701),s(91874)),o=s(15058),r=s(15945),c=s(73082),d=s(95353),l=s(17845),h=s(7200);const p="newCard";var u={name:"checkout",components:{ChannelLogo:c.A,ChannelWrapper:r.A,ChannelOrder:o.A},mixins:[h.A],computed:{...(0,d.aH)(["isPc","isMobile"]),cvcValidated(){const t=this.cvc.length;return t>=3&&t<=4}},watch:{loading(t,e){!0===t&&!1===e&&this.$loading.show(),!1===t&&!0===e&&this.$loading.hide()}},data(){return{checkoutInstance:"",initParams:{},newCardTxt:p,historyCard:[],chosenIndex:-1,cvc:"",showCvcError:!1,recordCardNum:!1,loading:!1,isFirstPayFinished:!0}},methods:{async prepareParams(){let t;try{t=JSON.parse(sessionStorage.getItem("params")||"{}"),this.initParams=t,t.sources&&(this.historyCard=t.sources.map(t=>{const e=Object.keys(t)[0],s=t[e];return{key:e,...s}})),this.historyCard.length?this.chosenIndex=this.historyCard[0].key:this.toggle(p)}catch(e){console.error(e)}},async initCheckout(){const t={zh_tw:"zh-tw",zh_cn:"zh"},e=t[this.$i18n.locale]||this.$i18n.locale,s=this.initParams,i=await(0,n.B2)({paymentSession:s.session_data,publicKey:s.client_key,environment:s.env,appearance:{focusOutlineWidth:"0"},locale:e,onReady:()=>this.onReady(),onPaymentCompleted:(t,e)=>this.onPaymentCompleted(t,e),onChange:t=>this.onChange(t),onError:(t,e)=>this.onError(t,e),onSubmit:t=>this.onSubmit(t),componentOptions:{card:{displayCardholderName:"hidden"}}}),a=await i.create("flow",{showPayButton:!1});this.checkoutInstance=a.mount("#payments")},onChange(t){console.log("onChange","isValid: ",t.isValid()," for ",t.type)},onReady(){console.log("onReady")},onSubmit(t){console.log("onSubmit"),this.loading&&(this.loading=!1)},onPaymentCompleted(t,e){this.loading&&(this.loading=!1),this.isFirstPayFinished=!0,this.$router.replace("/completed?ir=cko")},onError(t,e){if(this.loading&&(this.loading=!1),this.isFirstPayFinished=!0,"Integration"===e.type&&e.details&&e.details.includes("PaymentSession Response needs to be provided")&&(console.error("checkout: 组件初始化失败!"),this.$router.go(-1),setTimeout(()=>this.$root.$emit("adyenInitError"),200)),"Submit"===e.type)switch(e.details){case"Payment Method not valid":break}if("Request"===e.type&&200!==e.status&&e.details.paymentId)return this.fetchErrorMessage(e.details.paymentId);console.error("initCheckout: 未知的错误！"+e.message)},fetchErrorMessage(t){if(!this.initParams.ext_detail_url)return null;const e=this.initParams;l.eu.get(e.ext_detail_url,{params:{sid:t}}).then(t=>{const{code:e,data:s}=t;0===e&&this.basicShowError("cko",s)})},fixCvC(t){const e=Number.isNaN(Number(t.data));if(e){const t=String(this.cvc),e=t.length;this.cvc=t.slice(0,e-1)}const s=String(this.cvc);s.length>4&&(this.cvc=s.slice(0,4))},payByHistoryCard(t){if(!this.cvcValidated)return this.showCvcError=!0,null;this.loading=!0;const e=this.initParams;l.eu.post(e.payment_url,{reference:e.reference,source_id:t.key,cvv:this.cvc}).then(t=>{const{code:e,data:s}=t;if(0===e)if(s.response_code)switch(s.response_code){case"10000":this.onPaymentCompleted();break;default:this.basicShowError("cko",s)}else location.href=s.redirect_url}).finally(()=>this.loading=!1)},payByNewCard(){if(this.loading)return null;if(!this.isFirstPayFinished)return null;this.isFirstPayFinished=!1,this.loading=!0;const t=this.initParams;this.recordCardNum&&l.eu.post(t.store_card_url,{reference:t.reference}),this.checkoutInstance.submit()},async toggle(t){this.chosenIndex=t;try{t!==p||this.checkoutInstance||await this.initCheckout()}catch(e){this.onError("",e)}},delJsScript(){const t="https://checkout-web-components.checkout.com/index.js",e=document.getElementsByTagName("script"),s=Array.prototype.slice.call(e);s.forEach(function(e){e.src===t&&e.parentNode.removeChild(e)})}},created(){this.$store.state.functionSwitch.ckoCheckedByDefault&&(this.recordCardNum=!0),this.prepareParams()},beforeDestroy(){this.delJsScript(),sessionStorage.removeItem("params")}},m=u,_=s(81656),g=(0,_.A)(m,i,a,!1,null,"31a3e2e3",null),f=g.exports},4898:function(t,e,s){s.r(e),s.d(e,{default:function(){return m}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"airwallex-page-wrapper"},[t.isPc?e("channel-logo"):t._e(),e("div",{staticClass:"content-wrapper"},[e("channel-order",{attrs:{coin:t.initParams.coinNums,currency:t.initParams.currency_symbol,amount:t.initParams.amount,"in-debt":t.initParams.inDebt}}),e("channel-wrapper",[e("section",{staticClass:"airewallex-wrapper"},[e("div",{staticClass:"inner-wrapper"},[e("div",{ref:"card",attrs:{id:"drop-in"}})])])])],1),t.isMobile?e("channel-logo"):t._e()],1)},a=[],n=s(95939),o=s(73082),r=s(15945),c=s(15058),d=s(95353),l={name:"airwallex",components:{ChannelLogo:o.A,ChannelWrapper:r.A,ChannelOrder:c.A},computed:{...(0,d.aH)(["isPc","isMobile"])},data(){return{airwalexInstance:"",initParams:{}}},methods:{async initAirwallext(){let t;try{t=JSON.parse(sessionStorage.getItem("params")||"{}"),this.initParams=t,await(0,n.T)({enabledElements:["payments"],env:t.env,origin:window.location.origin});const e=await(0,n.n)("dropIn",{intent_id:t.intent_id,client_secret:t.client_secret,currency:t.currency,mode:t.mode,cvcRequired:!0,theme:{palette:{primary:"#00112c"}},methods:[t.payment_method],customer_id:t.customer_id});e.mount("drop-in"),this.airwalexInstance=e;const s=this.$refs.card;s.addEventListener&&(s.addEventListener("onReady",this.onReady),s.addEventListener("onSuccess",this.onSuccess),s.addEventListener("onError",this.onError))}catch(e){console.error(e.message),console.log(`Airwallext 组件初始化失败，错误信息：${e.message}。`)}},onReady(){console.log("cmp ready!")},onSuccess(t){this.$router.replace("/completed?ir=aw")},onError(t){const{error:e}=t.detail;switch(e.code){default:this.$toast.err(e.message)}}},created(){this.initAirwallext()},beforeDestroy(){sessionStorage.removeItem("params")}},h=l,p=s(81656),u=(0,p.A)(h,i,a,!1,null,"fd385af2",null),m=u.exports},7200:function(t,e,s){s(44114),s(18111),s(61701);var i=s(17845);const a={CVC_VERIFICATION_FAILED:"channel-pay-error-cvc",NOT_ENOUGH_MONEY:"channel-pay-error-no-money",UNSAFE_PAYMENT_ENVIRONMENT:"channel-pay-error-environment-unsafe",CARD_MAX_AMOUNT:"channel-pay-error-max-amount",CARD_MAX_PAY_TIMES:"channel-pay-error-max-pay-times",CARD_INVALID_NUMBER:"channel-pay-error-invalid_number",CARD_HAS_EXPIRED:"channel-pay-error-has-expired",NETWORK_ERROR:"channel-pay-error-network-error",TRANSACTION_NOT_ALLOWED:"channel-pay-error-not-allowed",OTHER_ERROR:"channel-pay-error-other"};e.A={data(){return{clickPayTimes:Number(sessionStorage.getItem("7x9FkL2pQm")||0)}},methods:{basicShowError(t,e){switch(t){case"ad":{const t={CVC_VERIFICATION_FAILED:{t1:24},NOT_ENOUGH_MONEY:{t1:12,t2:51,t3:[24,25,26,27,28,29,30]},UNSAFE_PAYMENT_ENVIRONMENT:{t1:[20,31,2,14],t2:["05",13,83,59,88]},CARD_MAX_AMOUNT:{t1:28,t2:61},CARD_MAX_PAY_TIMES:{t1:29},CARD_INVALID_NUMBER:{t1:8,t2:[14,15]},CARD_HAS_EXPIRED:{t1:6,t2:54,t3:1},NETWORK_ERROR:{t1:"905_3"},TRANSACTION_NOT_ALLOWED:{t1:[23,22,25],t2:[1,3,12,41,43,46,57,58,62,63,70,82],t3:[3,21]}},s=[];e.refusalReasonCode&&s.push(`t1_${e.refusalReasonCode}`),e.RawCode&&s.push(`t2_${e.RawCode}`),e.MacCode&&s.push(`t3_${e.MacCode}`),this.showMessage(t,s);break}case"cko":{const t={CVC_VERIFICATION_FAILED:{t1:[20087,20100]},NOT_ENOUGH_MONEY:{t1:20051,t3:[24,25,26,27,28,29,30]},UNSAFE_PAYMENT_ENVIRONMENT:{t1:[20001,20002,20003,20005,20012,20046,20059,30004,30020,30034]},CARD_MAX_AMOUNT:{t1:[20061,30021]},CARD_MAX_PAY_TIMES:{t1:[20065,30022]},CARD_INVALID_NUMBER:{t1:[20014,30015]},CARD_HAS_EXPIRED:{t1:30033,t3:1},TRANSACTION_NOT_ALLOWED:{t1:[20057,20091,"2006P",20103,30041,30043,40101],t3:[3,21]}},s=[];e.code&&s.push(`t1_${e.code}`),e.raw_code&&s.push(`t2_${e.raw_code}`),e.mac_code&&s.push(`t3_${e.mac_code}`),this.showMessage(t,s);break}}},showMessage(t,e=[]){if(!e.length)return this.$toast.err(this.$t(a.OTHER_ERROR));for(const s of e){const[e]=s.split("_");for(const[i,n]of Object.entries(t)){const t=n[e],o=Array.isArray(t)?t.map(t=>`${e}_${t}`):[`${e}_${t}`];if(o.includes(s))return this.$toast.err(this.$t(a[i]),4e3)}}this.$toast.err(this.$t(a.OTHER_ERROR),4e3)},async prefetchValidation(t){if(sessionStorage.setItem("7x9FkL2pQm",++this.clickPayTimes),this.clickPayTimes<3)return;const e=JSON.parse(sessionStorage.getItem("3zRtY8vXwN")||"{}");if(!(e.payment_host&&e.order_id&&e.payment_order_id))return;let{payment_host:s,order_id:a,payment_order_id:n}=e;return s+="/api/payment/pay_risk_before",i.eu.post(s,{order_id:n,out_trade_no:a,channel:t}).then(t=>{const{code:e}=t;switch(e){case 0:break;case 120012:return this.$toast.err(this.$t("prefetch-safety-error").replace(/<br\/>/g,"")),sessionStorage.removeItem("ppParams"),setTimeout(()=>this.$router.replace("/"),500),Promise.reject("120012");default:this.$toast.err(this.$t("cb_page_title_err"))}})}},beforeDestroy(){sessionStorage.removeItem("7x9FkL2pQm"),sessionStorage.removeItem("3zRtY8vXwN")}}},10638:function(t,e,s){s.r(e),s.d(e,{default:function(){return _}});var i=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"show",rawName:"v-show",value:!t.paramIntent,expression:"!paramIntent"}],staticClass:"stripe-page-wrapper"},[t.isPc?e("channel-logo"):t._e(),e("div",{staticClass:"content-wrapper"},[e("channel-order",{attrs:{coin:t.initParams.coinNums,currency:t.initParams.currency_symbol,amount:t.initParams.amount}}),e("channel-wrapper",[e("section",{staticClass:"stripe-wrapper",staticStyle:{"font-size":"15px","text-align":"left"}},[e("div",{staticClass:"inner-wrapper"},[e("form",{attrs:{id:"payment-form"}},[e("div",{attrs:{id:"payment-element"}}),e("button",{staticClass:"stripe-submit",attrs:{id:"submit"},on:{click:function(e){return e.preventDefault(),t.onSubmit.apply(null,arguments)}}},[e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg","aria-hidden":"true"}}),t._v(" Pay "+t._s(t.initParams.currency_symbol)+t._s(t.initParams.amount)+" ")])])])])])],1),t.isMobile?e("channel-logo"):t._e()],1)},a=[],n=(s(18111),s(7588),s(14603),s(47566),s(98721),s(73082)),o=s(15058),r=s(15945),c=s(95353),d=s(17845);const l="https://js.stripe.com/v3/";var h={name:"stripe",components:{ChannelWrapper:r.A,ChannelOrder:o.A,ChannelLogo:n.A},computed:{...(0,c.aH)(["isPc","isMobile"])},data(){return{stripe:"",elements:"",initParams:{},paramIntent:""}},methods:{loadScript(){const t=new URLSearchParams(window.location.search);this.paramIntent=t.get("payment_intent_client_secret");const e=document.createElement("script");e.src=l,e.onload=this.paramIntent?this.redirect:this.initForm,document.body.appendChild(e)},async initForm(){const t=window.Stripe(this.initParams.pub_secret_key),e={clientSecret:this.initParams.stripe_client_secret,customerSessionClientSecret:this.initParams.custom_client_secret,appearance:{variables:{colorPrimary:"black",borderRadius:"10px"},rules:{".Input":{border:"1px solid #b9c4c9"},".Input:hover":{border:"1px solid #99a3ad"},".Input:focus":{border:"1px solid #0066ff",boxShadow:"0 0 0 2px #99c2ff"},".CheckboxInput:hover":{border:"1px solid #99a3ad"},".CheckboxInput:focus":{border:"1px solid #0066ff",boxShadow:"0 0 0 2px #99c2ff"}}}},s=t.elements(e),i={layout:{type:"accordion",defaultCollapsed:!1},fields:{billingDetails:{address:"never"}}},a=s.create("payment",i);a.on("loaderror",this.onError),a.mount("#payment-element"),this.stripe=t,this.elements=s},onError(t){this.$toast.err(this.$t("cb_page_pending_desc")),this.$router.go(-1),console.error(`stripe init error: ${JSON.stringify(t)}`),setTimeout(()=>this.$root.$emit("adyenInitError"),200)},async prefetchValidation(t,e){const{code:s}=await d.eu.post(`${this.initParams.host}/api/payment/pay_risk_before`,{order_id:this.initParams.order_id,out_trade_no:this.initParams.out_trade_no,channel:t,ctoken:e});return s},async onSubmit(){const{error:t}=await this.elements.submit();if(t)return;const{error:e,confirmationToken:s}=await this.stripe.createConfirmationToken({elements:this.elements,params:{payment_method_data:{billing_details:{address:{line1:this.$store.state.country,line2:this.$store.state.country,country:this.$store.state.country,state:this.$store.state.country,city:this.$store.state.country,postal_code:this.$store.state.zipCode}}}}});if(e)return console.error(`stripe create token error: ${JSON.stringify(e)}`),this.$toast.err(this.$t("cb_page_title_err"));const i=await this.prefetchValidation("stripe",s.id);return 120012===i?(this.$toast.err(this.$t("prefetch-safety-error").replace(/<br\/>/g,"")),sessionStorage.removeItem("params"),void setTimeout(()=>this.$router.replace("/"),500)):i?this.$toast.err(this.$t("cb_page_title_err")):void this.stripe.confirmPayment({elements:this.elements,confirmParams:{return_url:location.href,payment_method_data:{billing_details:{address:{line1:this.$store.state.country,line2:this.$store.state.country,country:this.$store.state.country,state:this.$store.state.country,city:this.$store.state.country,postal_code:this.$store.state.zipCode}}}}}).then(t=>{t.error&&(console.error(`stripe payment error: ${JSON.stringify(t.error)}`),this.$toast.err(t.error.message))})},async redirect(){const t=JSON.parse(sessionStorage.getItem("params")||"{}"),e=window.Stripe(t.pub_secret_key),s=new URLSearchParams(window.location.search),i=s.get("payment_intent_client_secret");e.retrievePaymentIntent(i).then(({paymentIntent:t})=>{switch(t.status){case"succeeded":this.$router.replace("/completed?rf=1");break;case"processing":this.$router.replace("/pending?rf=1");break;case"requires_payment_method":this.$router.replace("/fail");break;default:this.$router.replace("/fail");break}}).catch(t=>{console.error(`stripe retrieve error: ${JSON.stringify(t)}`);const e=this.initParams.order_id;location.href=`${sessionStorage.getItem("url")}/api/payment/result?channel=stripe&app_id=8519&OrderId=${e}`})}},created(){this.initParams=JSON.parse(sessionStorage.getItem("params")||"{}"),this.loadScript()},beforeDestroy(){const t=l,e=document.getElementsByTagName("script"),s=Array.prototype.slice.call(e);s.forEach(function(e){e.src===t&&e.parentNode.removeChild(e)})}},p=h,u=s(81656),m=(0,u.A)(p,i,a,!1,null,"1784f58b",null),_=m.exports},15058:function(t,e,s){s.d(e,{A:function(){return l}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"info-wrapper",staticStyle:{"text-align":"left"}},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("adyen-order-details")))]),e("p",[t._v(t._s(t.$t("adyen-order-info"))+" "),t.goodsName?e("span",{staticClass:"sdk-products-name"},[t._v(t._s(t.goodsName))]):e("span",{staticClass:"token-name"},[t._v(t._s(t.coin)+" "+t._s(t.$vt("tokenName")))])]),e("p",[t._v(t._s(t.$t("totalPrice"))+" "),e("span",[t._v(t._s(t.currency)+" "+t._s(t.amount))])]),t.showForm?e("div",{staticClass:"form"},[e("div",{staticClass:"form-title"},[t._v(t._s(t.$t("channel-order-info")))]),e("div",{staticClass:"divider"}),e("div",{staticClass:"form-item"},[e("div",{staticClass:"label"},[t._v(t._s(t.$t("label-zipcode")))]),e("div",{staticClass:"content"},[e("i"),e("input",{directives:[{name:"model",rawName:"v-model",value:t.zipCode,expression:"zipCode"}],attrs:{type:"text",placeholder:t.$t("label-zipcode")},domProps:{value:t.zipCode},on:{change:t.change,input:[function(e){e.target.composing||(t.zipCode=e.target.value)},function(e){return t.fixInput()}]}})])])]):t._e()])},a=[],n=s(17845),o={name:"orderInfo",props:["coin","amount","currency"],data(){const t=this.$store.state.zipCode,e=window.__showEmailForm;return{zipCode:t,showForm:e,goodsName:sessionStorage.getItem("goodsName")}},methods:{change(){const t=sessionStorage.getItem("id_sign"),e=sessionStorage.getItem("url")+"/api/payment/save_billingaddress";t&&sessionStorage.getItem("url")&&this.zipCode&&(this.zipCode.length>15&&(this.zipCode=this.zipCode.slice(0,15)),n.eu.post(e,{zipcode:this.zipCode,order_id:t}))},fixInput(){"US"===this.$store.state.country&&(this.zipCode=this.zipCode.replace(/[^0-9]/g,""))}}},r=o,c=s(81656),d=(0,c.A)(r,i,a,!1,null,"0c84201b",null),l=d.exports},15945:function(t,e,s){s.d(e,{A:function(){return d}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"adyen-container"},[t._t("default")],2)},a=[],n={name:"adyen-wrapper"},o=n,r=s(81656),c=(0,r.A)(o,i,a,!1,null,"494e066e",null),d=c.exports},25504:function(t,e,s){s.r(e),s.d(e,{default:function(){return f}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"adyen-page-wrapper"},[t.isPc?e("channel-logo"):t._e(),e("div",{staticClass:"content-wrapper"},[e("channel-order",{attrs:{coin:t.initParams.coinNums,currency:t.initParams.currency_symbol,amount:t.initParams.amount}}),e("channel-wrapper",[e("section",{staticClass:"adyen-wrapper",staticStyle:{"font-size":"15px","text-align":"left"}},[e("div",{staticClass:"inner-wrapper"},[e("div",{ref:"card",attrs:{id:"card-container"}})])])])],1),t.isMobile?e("channel-logo"):t._e()],1)},a=[],n=s(65063),o=s(17845),r=s(15058),c=s(15945),d=s(73082),l=s(95353),h=s(7200),p=s(52112),u={name:"Adyen",components:{ChannelLogo:d.A,ChannelWrapper:c.A,ChannelOrder:r.A},mixins:[h.A],computed:{...(0,l.aH)(["isPc","isMobile"])},data(){return{adyenInstance:"",initParams:{},uO5iSAOB:[void 0,void 0]}},methods:{async initAdyen(){let t;try{t=JSON.parse(sessionStorage.getItem("params")||"{}"),this.initParams=t}catch(i){console.error(i)}const e={environment:t.environment,clientKey:t.client_key,session:{id:t.id,sessionData:t.session_data},onPaymentCompleted:(t,e)=>this.onPaymentCompleted(t,e),onError:(t,e)=>this.onError(t,e),onSubmit:(e,s)=>this.onSubmit(e,t,s),onAdditionalDetails:(t,e)=>this.onAdditionalDetailsV2(t,e),showPayButton:!0,paymentMethodsConfiguration:{card:{hasHolderName:!1,holderNameRequired:!1,billingAddressRequired:!1,enableStoreDetails:!0,onConfigSuccess:()=>this.onConfigSuccess(),onBinValue:t=>this.setEncryptParams(0,t),onFieldValid:t=>this.setEncryptParams(1,t)},threeDS2:{challengeWindowSize:"05","05":["100%","100%"]}}},s=await(0,n.A)(e);this.adyenInstance=s.create("dropin").mount(this.$refs.card)},onPaymentCompleted(t,e){if("905_3"===t.errorCode)return this.$toast.err(this.$t("RU_refused")),void this.reBuildAdyen();switch(t.resultCode){case"Authorised":this.$router.replace("/completed?ir=ad");break;case"Refused":case"Cancelled":this.basicShowError("ad",t),this.reBuildAdyen();break;default:this.reBuildAdyen(),this.$toast.err(this.$t("cb_page_title_err"))}},onError(t,e){if(t&&t.message&&t.message.includes("ThreeDS2"))return null;this.$toast.err(this.$t("cb_page_pending_desc")),this.$router.go(-1),console.error(`adyen init error:\n      name: ${t.name}\n      msg: ${t.message}\n      session_data: ${this.initParams.session_data}`),setTimeout(()=>this.$root.$emit("adyenInitError"),200)},onSubmit(t,e,s){t.isValid?(t.data.reference=e.reference,t.data.returnUrl=location.origin+location.pathname,this.setEncryptParams(2,t.data),o.eu.post(e.payment_url,t.data).then(t=>{if(0!==t.code)throw Error("adyen submit err!");t.data.action?(s.handleAction(t.data.action),["redirect","threeDS2"].includes(t.data.action.type)&&sessionStorage.setItem("requestUrl",e.payment_url)):this.onPaymentCompleted(t.data)}).catch(t=>{console.error(t),this.onPaymentCompleted({resultCode:"Error"})})):(this.$toast.err(this.$t("cb_page_title_err")),console.error("err"))},onAdditionalDetailsV1(t){const e=sessionStorage.getItem("requestUrl"),s={details:{redirectResult:t.redirectResult}};o.eu.post(e,s).then(t=>{if(0!==t.code)throw Error("adyen submit err!");this.onPaymentCompleted(t.data)}).catch(t=>{console.error(t),this.onPaymentCompleted({resultCode:"Error"})})},onAdditionalDetailsV2(t,e){const s=sessionStorage.getItem("requestUrl");o.eu.post(s,t.data).then(t=>{if(0!==t.code)throw Error("adyen submit err!");t.data.action?e.handleAction(t.data.action):this.onPaymentCompleted(t.data)}).catch(t=>{console.error(t),this.onPaymentCompleted({resultCode:"Error"})})},reBuildAdyen(){this.adyenInstance&&this.adyenInstance.unmount(),this.adyenInstance="",this.initAdyen()},setEncryptParams(t,e){0===t&&e.binValue&&(this.uO5iSAOB[0]=e.binValue),1===t&&e.endDigits&&(this.uO5iSAOB[1]=e.endDigits);const s=this.uO5iSAOB;2===t&&s[0]&&s[1]&&(e.uO5iSAOB=(0,p.r9)(`${s[0]}|${s[1]}`))},onConfigSuccess(){if(!this.$store.state.functionSwitch.ckoCheckedByDefault)return;const t=document.querySelector(".adyen-checkout__checkbox__input");t&&!1===t.checked&&t.click()}},mounted(){if(sessionStorage.getItem("TestAdyen"))return this.onError({message:"test_message",name:"test_name"});const t=this.$route.query;t&&t.redirectResult?this.onAdditionalDetailsV1(t):this.initAdyen()},beforeDestroy(){sessionStorage.removeItem("params")}},m=u,_=s(81656),g=(0,_.A)(m,i,a,!1,null,null,null),f=g.exports},38101:function(t,e,s){s.r(e),s.d(e,{default:function(){return m}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pingpong-page-wrapper"},[t.isPc?e("channel-logo"):t._e(),e("div",{staticClass:"content-wrapper"},[e("channel-order",{attrs:{coin:t.initParams.coinNums,currency:t.initParams.currency_symbol,amount:t.initParams.amount,"in-debt":t.initParams.inDebt}}),e("channel-wrapper",[e("section",{staticClass:"pingpong-wrapper"},[e("div",{staticClass:"inner-wrapper"},[e("div",{staticClass:"cmp-wrapper"},[e("div",{staticClass:"frame-card"}),e("button",{attrs:{id:"submit"},on:{click:t.submitForm}},[e("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg",alt:"","aria-hidden":"true"}}),t._v(" Pay "+t._s(t.initParams.currency_symbol)+t._s(t.initParams.amount)+" ")])])])])])],1),t.isMobile?e("channel-logo"):t._e()],1)},a=[],n=s(95353),o=s(15058),r=s(15945),c=s(73082),d=s(17845),l={name:"payermax",components:{ChannelLogo:c.A,ChannelWrapper:r.A,ChannelOrder:o.A},computed:{...(0,n.aH)(["isPc","isMobile"])},data(){return{initParams:{},cardInstance:{},isFormValid:!1,cardChosen:!1}},methods:{loadPingpongScript(){const t="https://cdn.payermax.com/dropin/js/pmdropin.min.js",e=document.createElement("script");e.src=t,e.onload=this.onScriptLoad,document.body.appendChild(e)},onScriptLoad(){const t=window.PMdropin;this.initParams=JSON.parse(sessionStorage.getItem("params")||"{}");const e=t.create("card",{clientKey:this.initParams.clientKey,sessionKey:this.initParams.sessionKey,language:this.$i18n.locale,sandbox:this.initParams.sand_box});e.mount(".frame-card"),e.on("form-check",t=>{this.isFormValid=t.isFormValid,this.cardChosen=t.isFormValid||Boolean(t.from)}),e.on("ready",()=>this.onReady()),this.cardInstance=e},onReady(){console.log("cmp ready!")},onCmpError(){this.$router.go(-1),setTimeout(()=>this.$root.$emit("adyenInitError"),200)},submitForm(){const t=this.cardInstance;return this.cardChosen?this.isFormValid?(t.emit("setDisabled",!0),void t.emit("canMakePayment").then(e=>{const{code:s}=e;switch(s){case"APPLY_SUCCESS":{const t=e?.data?.paymentToken;this.requestPay(t);break}default:}t.emit("setDisabled",!1)}).catch(e=>{t.emit("setDisabled",!1),console.log(e)})):(t.emit("canMakePayment"),null):this.$toast.err("Please select a payment method!")},requestPay(t){const e=this.initParams,s={reference:e.payment_order_id,sek:e.sessionKey,pt:t,subject:e.name};this.$loading.show(),d.eu.post(e.pay_url,s).then(t=>{const{code:e,data:s}=t;switch(e){case 0:{const{status:t}=s;"SUCCESS"===t&&this.$router.replace("/completed?rf=1");break}default:this.$toast.err(this.$t("cb_page_title_err"))}}).finally(()=>this.$loading.hide())}},created(){this.loadPingpongScript()},beforeDestroy(){sessionStorage.removeItem("params")}},h=l,p=s(81656),u=(0,p.A)(h,i,a,!1,null,null,null),m=u.exports},44988:function(t,e,s){s.r(e),s.d(e,{default:function(){return m}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pingpong-page-wrapper"},[t.isPc?e("channel-logo"):t._e(),e("div",{staticClass:"content-wrapper"},[e("channel-order",{attrs:{coin:t.initParams.coinNums,currency:t.initParams.currency_symbol,amount:t.initParams.amount,"in-debt":t.initParams.inDebt}}),e("channel-wrapper",[e("section",{staticClass:"pingpong-wrapper"},[e("div",{staticClass:"inner-wrapper"},[e("pp-funplus-checkout",{attrs:{savepay:t.savePay,accessToken:t.initParams.ppToken}})],1)])])],1),t.isMobile?e("channel-logo"):t._e()],1)},a=[],n=s(95353),o=s(15058),r=s(15945),c=s(73082),d=s(7200),l={name:"pingpong",components:{ChannelLogo:c.A,ChannelWrapper:r.A,ChannelOrder:o.A},mixins:[d.A],computed:{...(0,n.aH)(["isPc","isMobile"]),savePay(){return this.$store.state.functionSwitch.ckoCheckedByDefault?"Y":"N"}},data(){return{initParams:{}}},methods:{loadPingpongScript(){const t="https://pay-cdn.pingpongx.com/production-fra/static/pp-funplus-checkout/sandbox/pp-funplus-checkout.js",e=document.createElement("script");e.src=t,e.type="module",e.onload=this.onScriptLoad,document.body.appendChild(e)},onScriptLoad(){const t=window.PingPong;t.Checkout.initializedHook=t=>{t?this.onReady(t):this.onCmpError()},t.Checkout.beforeCheckoutHook=async()=>this.prefetchValidation("pingpong"),this.initParams=JSON.parse(sessionStorage.getItem("ppParams")||"{}")},onReady(){console.log("cmp ready!")},onCmpError(){this.$router.go(-1),setTimeout(()=>this.$root.$emit("adyenInitError"),200)}},created(){this.loadPingpongScript()}},h=l,p=s(81656),u=(0,p.A)(h,i,a,!1,null,null,null),m=u.exports},45662:function(t,e,s){s.d(e,{A:function(){return u}});var i=function(){var t=this,e=t._self._c;return e("common-part",{class:[t.$store.state.gameinfo.gameCode,"channel-part-wrapper",t.$gameName,{sdk:t.$store.state.IS_CHECKOUT_SDK}],attrs:{"label-font":t.$t("channelChosen"),id:"channel-part-wrapper"}},[e("div",{staticClass:"channel-list"},[t.calChannelList.length?t._l(t.calChannelList,function(s,i){return e("div",{key:s.FE_CHANNEL_ID,staticClass:"channel-btn",class:[{"channel-chosen__active":s.FE_CHANNEL_ID===t.chosenChannel.FE_CHANNEL_ID}],on:{click:function(e){return t.toggleStatus(i)}}},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:backgroundImage",value:s.icon_url,expression:"channel.icon_url",arg:"backgroundImage"}],staticClass:"image common-fade-in"}),s.subscript||t.whetherShowVipBonus(s)?e("div",{class:["recommendation","recommendation-REC"]},[e("span",{staticClass:"blank"}),t.whetherShowVipBonus(s)?e("span",{staticClass:"bonus-description"},[t._v(" VIP +"+t._s(t.vip.channelBonus*(2===t.chosenDiamond.type?t.chosenDiamond.totalDiamond:t.chosenDiamond.coin))+" "),e("i")]):e("span",{staticClass:"txt"},[t._v(t._s(t.$t("recommend-txt")))])]):t._e()])}):e("div",{staticClass:"empty"},[t._v(t._s(t.$t("nothingHere")))])],2)])},a=[],n=(s(18111),s(22489),s(61701),s(49933)),o=s(87367),r=s(95353),c=s(52112),d={name:"ChannelChoose",components:{CommonPart:n.A},props:{activity:{type:Object,default:()=>{}}},data(){return{channelList:[],unwatch:void 0,isUserChosen:!1}},computed:{...(0,r.aH)(["urlParams","isArZone","currencyUnit"]),...(0,r.aH)("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip","isInit","isFirstPayUsed"]),whetherShowVipBonus(){return t=>this.isInit&&this.vip.discountSubChannelId.includes(t.sub_channel_id)&&this.vip.channelBonus&&this.isFirstPayUsed&&!this.chosenCoupon.FE_INDEX},calChannelList(){const t=this.$store.getters["riskPolicy/hideSomeChannel"]||[];return t.length?(t.includes(this.chosenChannel.channel_id)&&this.$store.commit("formdata/resetChannel"),this.channelList.filter(e=>!t.includes(e.channel_id))):this.channelList}},methods:{loadChannelList(){const t={currency:this.urlParams.cr,price:+this.chosenDiamond.price,product_id:this.chosenDiamond.product_id},e=this.$store.state.formdata.chosenCoupon;if(e.FE_INDEX&&("discount_coupon"===e.feType&&(t.price=e.discount_price),"cash_coupon"===e.feType&&(t.price=e.price),"first_pay"===e.feType&&(t.price=e.discount_price)),this.$store.getters["formdata/takeEffectDefaultDiscount"]){const e=["BDT","CLP","COP","CRC","DZD","HUF","IDR","INR","IQD","JPY","KES","KRW","KZT","LBP","LKR","MMK","NGN","PHP","PKR","PYG","RSD","RUB","THB","TWD","TZS","VND"];let s=.95*t.price;s=e.includes(this.chosenDiamond.currency)?Math.ceil(s):s.toFixed(2),t.price=+s}const{type:s,nowPrice:i}=this.chosenDiamond;2===s&&(t.price=+i),this.$loading.show(),this.$store.commit("formdata/resetChannel"),(0,o.Tu)(t).then(({data:t,code:e,message:s})=>{0===e?(this.channelList=this.adapterChannel(t),setTimeout(()=>{const{lastChannel:t}=this.$store.state.userinfo;if(t){const e=this.calChannelList.filter(e=>e.channel_id===t.channel_id&&t.sub_channel_id===e.sub_channel_id);e&&e.length&&this.$store.commit("formdata/setChosenChannel",e[0]);const s=this.calChannelList.filter(e=>e.channel_name===t.channel_name);if(s&&s.length)return this.$store.commit("formdata/setChosenChannel",s[0])}},0),this.$gcbk("switch.enableAnimation",!1)&&!window.channelFlag&&this.$store.state.isPc&&(window.channelFlag=1,this.$nextTick(()=>{gsap&&gsap.from(".channel-list",{height:0,duration:.4,clearProps:"height"})}))):this.$toast.err(this.$t("fetchChannelError"))}).finally(()=>this.$loading.hide())},adapterChannel(t){if(this.$store.state.gameinfo.isCn){let e;e=c.oi?["WxpayJSAPI","Alipaywap"]:window.isMobile?["WxpayMWEB","Alipaywap"]:["WxpayPcNATIVE","Alipaypc"],t=t.filter(t=>"wxpay"!==t.channel_id&&"alipay"!==t.channel_id||e.indexOf(t.channel_name+t.sub_channel_id)>-1)}return t.map(t=>(t.FE_CHANNEL_ID=`${t.channel_id}__${t.channel_name}`,t))},toggleStatus(t){this.isUserChosen=!0;const e=this.calChannelList[t],s=this.chosenChannel;if(e.FE_CHANNEL_ID===s.FE_CHANNEL_ID)return null;this.$store.commit("formdata/setChosenChannel",this.calChannelList[t])},initLastChannel(){(0,o.aU)().then(t=>{const{code:e,data:s}=t;if(0===e){if(this.$store.commit("userinfo/saveLastChannelInfo",s),this.isUserChosen)return null;const{lastChannel:t}=this.$store.state.userinfo;if(t){const e=this.calChannelList.filter(e=>e.channel_id===t.channel_id&&t.sub_channel_id===e.sub_channel_id);if(e&&e.length)return this.$store.commit("formdata/setChosenChannel",e[0])}}})}},created(){this.$root.$on("couponChoose",()=>this.loadChannelList()),this.$root.$on("activityInitEnd",()=>this.loadChannelList()),this.$root.$on("loginEnd",t=>{1===t&&this.initLastChannel()})},beforeDestroy(){this.unwatch&&this.unwatch()}},l=d,h=s(81656),p=(0,h.A)(l,i,a,!1,null,"1f14313e",null),u=p.exports},69139:function(t,e,s){s.d(e,{A:function(){return L}});var i=function(){var t=this,e=t._self._c;return e("common-part",{class:["coupon-bar-wrapper",t.$gameName],attrs:{"label-font":t.$t("coupon"),id:"coupon-bar-wrapper"}},[t.$store.state.userinfo.isLogin?e("div",{staticStyle:{display:"inline-block"},on:{click:function(e){t.activity.showPop=!0}}},[t.activity.isFirstPayUsed?[0===t.availableCouponNum?e("div",{staticClass:"coupons-wrapper coupons-wrapper__no_coupon"},[t._v(" "+t._s(t.$t("coupon_desc_unavailable"))+" ")]):t._e(),t.availableCouponNum>0&&!t.chosenCoupon.FE_INDEX?e("div",{staticClass:"coupons-wrapper coupons-wrapper__available"},[e("span",[t._v(t._s(t.$t("coupon_nums",{0:t.availableCouponNum})))]),t._v(" "),e("i")]):t._e(),t.availableCouponNum>0&&t.chosenCoupon.FE_INDEX?e("div",{staticClass:"coupons-wrapper coupons-wrapper__chosen"},[e("div",{staticClass:"left"},[e("span",[t._v(t._s(t.$t("coupon_desc_chosen_erver")))])]),e("div",{staticClass:"right"},[e("over-size-scale",{key:t.chosenCoupon.FE_INDEX},[e("span",["discount_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.$t("coupon_discount",{0:t.chosenCoupon.rateWidthOutPercent})))]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.chosenCoupon.deduct_price)+" "+t._s(t.currencyUnit))]),t._v(" OFF")]:t._e(),"rebate_coupon"===t.chosenCoupon.feType?[e("span",[t._v(t._s(t.$t("bonus_tips"))+" ")]),t._v(t._s(t.chosenCoupon.rate)),e("i",{staticClass:"diamond-icon"})]:t._e()],2)]),e("i")],1)]):t._e()]:e("div",{staticClass:"coupons-wrapper coupons-wrapper__unavailable"},[e("div",{staticClass:"left"},[0===t.availableCouponNum?e("span",[t._v(t._s(t.$t("coupon_desc_unavailable")))]):e("span",[t._v(t._s(t.$t("coupon_nums",{0:t.availableCouponNum})))])])])],2):e("div",{staticClass:"coupons-wrapper coupons-wrapper__not-login",on:{click:function(e){return t.$root.$emit("ClickPayButNotLogin")}}},[t._v(t._s(t.$t("view_coupons_after_login")))]),t.activity.showPop?e("coupon-choose-pop",{attrs:{"had-obtained-list":t.activity.hadObtainedList,"not-obtained-list":t.activity.notObtainedList,"last-index":t.activity.chosenIndex,"is-first-charge-used":t.activity.isFirstPayUsed},on:{close:t.closeCouponPop}}):t._e()],1)},a=[],n=(s(44114),s(18111),s(22489),s(7588),s(61701),s(49933)),o=s(87367),r=s(95353),c=function(){var t=this,e=t._self._c;return e("div",{class:["ticket-chosen-wrapper",t.$gameName],attrs:{id:"ticket-chosen-wrapper"}},[e("div",{staticClass:"pop-main"},[e("div",{staticClass:"pop-close",on:{click:function(e){return t.$emit("close")}}}),e("div",{staticClass:"pop-title"},[e("h3",[t._v(t._s(t.$t("coupon")))]),e("span",[t._v(t._s(t.$t("discount_offer_tips")))])]),e("div",{staticClass:"divider"}),e("div",{staticClass:"nav-btn-wrapper"},[e("div",{class:["nav",{"nav-active":0===t.navIndex}],on:{click:function(e){t.navIndex=0}}},[t._v(t._s(t.$t("nav_my_coupon")))]),e("div",{class:["nav",{"nav-active":1===t.navIndex}],on:{click:function(e){t.navIndex=1}}},[t._v(t._s(t.$t("nav_other_coupon")))])]),e("div",{staticClass:"main-container"},[0===t.navIndex?[e("coupon-choose-list",{key:0,attrs:{"coupon-list":t.hadObtainedList,"is-first-charge-used":t.isFirstChargeUsed,reach:!0,"temp-chosen-coupon":t.tempChosenCoupon},on:{"update:tempChosenCoupon":function(e){t.tempChosenCoupon=e},"update:temp-chosen-coupon":function(e){t.tempChosenCoupon=e}}}),e("div",{class:["btn-confirm","click-btn",{"btn-confirm__unavailable":!t.isFirstChargeUsed}],on:{click:t.chooseCoupon}},[t._v(t._s(t.$t("modalBtnOk")))])]:e("coupon-choose-list",{key:1,attrs:{"coupon-list":t.notObtainedList,"is-first-charge-used":t.isFirstChargeUsed,reach:!1}})],2),e("p",{staticClass:"coupon-repeat-tips"},[t._v(" *"+t._s(t.$t("construction_faq_q5a1"))+" "),"RU"===t.$store.state.country?[t._v("Купоны не поддерживают Huawei Pay.")]:t._e()],2)])])},d=[],l=function(){var t=this,e=t._self._c;return e("div",{class:["ticket-wrapper",t.$gameName],attrs:{id:"ticket-wrapper"}},[e("div",{staticClass:"ticket-list"},[t._l(t.couponList,function(s,i){return["first_pay"!==s.feType&&"first_pay_rebate"!==s.feType&&(!("leaveCount"in s)||s.leaveCount>0)?e("div",{key:s.coupon_id+s.type+i,class:["item",{item__active:t.reach&&s.FE_INDEX===t.tempChosenCoupon.FE_INDEX},{item__unavailable:!t.isFirstChargeUsed||!t.reach||0===s.is_invalid}],on:{click:function(e){return t.choose(i,s)}}},[e("div",{staticClass:"left"},[t.$store.state.formdata.switchToggleState&&s.change_enable?e("coupon-toggle",{attrs:{"temp-chosen":t.reach&&s.FE_INDEX===t.tempChosenCoupon.FE_INDEX,"coupon-item":s}}):t._e(),e("div",{staticClass:"title"},["discount_coupon"===s.feType?[t._v(t._s(s.rate)+" "),e("span",[t._v("OFF")])]:"cash_coupon"===s.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(s.deduct_price)+" "+t._s(t.currencyUnit))]),t._v(" OFF ")]:"rebate_coupon"===s.feType?[e("span",[t._v(t._s(t.$t("bonus_tips"))+" ")]),t._v(t._s(s.rate)),e("i",{staticClass:"diamond-icon"})]:t._e()],2)],1),e("div",{staticClass:"right"},[e("div",{staticClass:"desc"},[t._v(t._s(t.$t(s.langKey,{0:s.num}))+" "+t._s(s.langValue))]),s.showLeaveDate?e("div",{staticClass:"time"},[t._v(t._s(s.showLeaveDate))]):t._e()])]):t._e()]})],2),t.showEmpty?e("span",{staticClass:"no-data"},[t._v(t._s(t.$t("nothingHere")))]):t._e()])},h=[],p=function(){var t=this,e=t._self._c;return e("div",{staticClass:"coupon-type-toggle-wrapper",on:{click:function(e){return e.stopPropagation(),t.beginToggleCoupon.apply(null,arguments)},mouseover:function(e){t.tips="discount_coupon"===t.couponItem.feType?t.$t("toggle-to-rebate"):t.$t("toggle-to-common")},mouseleave:function(e){t.tips=""}}},[t.tips?e("div",{staticClass:"tips"},[t._v(t._s(t.tips))]):t._e()])},u=[],m={name:"couponToggle",props:["couponItem","tempChosen"],data(){return{tips:""}},methods:{beginToggleCoupon(){this.tips="";const t=this.couponItem,e={change_type:"discount_coupon"===t.feType?"rebate":"coupon",coupon_id:this.couponItem.coupon_id};this.$loading.show(),(0,o.B9)(e).then(t=>{const{code:e}=t;0===e?(sessionStorage.setItem("reopenCoupon","1"),this.tempChosen&&sessionStorage.setItem("reChooseCoupon",this.couponItem.coupon_id),this.$root.$emit("couponToggleSuccess")):this.$toast.err(this.$t("toggle-fail-tips"))}).catch(t=>{console.error(t.message)}).finally(()=>this.$loading.hide())}},mounted(){this.couponItem.FE_INDEX===sessionStorage.getItem("popIndex")&&(sessionStorage.removeItem("popIndex"),this.tips="discount_coupon"===this.couponItem.feType?this.$t("toggle-to-rebate"):this.$t("toggle-to-common"),this.$el.scrollIntoView({behavior:"smooth"}))}},_=m,g=s(81656),f=(0,g.A)(_,p,u,!1,null,"82b9ba40",null),y=f.exports,C=s(52112),v={name:"CouponChooseList",components:{CouponToggle:y},props:["couponList","isFirstChargeUsed","reach","tempChosenCoupon"],methods:{choose(t,e){if(!this.isFirstChargeUsed||!this.reach||0===e.is_invalid)return null;this.tempChosenCoupon.FE_INDEX===e.FE_INDEX?this.$emit("update:tempChosenCoupon",{}):(this.$emit("update:tempChosenCoupon",e),this.$root.$emit("availableTicketChosen"))}},computed:{...(0,r.aH)(["isArZone","currencyUnit"]),showEmpty(){return!this.couponList.filter(t=>t.feType.includes("_coupon")).length}},created(){if(this.$store.state.formdata.switchToggleState&&this.reach&&(0,C.D_)("toggleCoupon")){const t=this.couponList.findIndex(t=>t.change_enable);this.couponList[t]&&sessionStorage.setItem("popIndex",this.couponList[t].FE_INDEX)}}},b=v,$=(0,g.A)(b,l,h,!1,null,"312ab878",null),w=$.exports,I={name:"CouponChoosePop",components:{CouponChooseList:w},props:["hadObtainedList","notObtainedList","isFirstChargeUsed","lastIndex"],data(){return{navIndex:0,tempChosenCoupon:this.$store.state.formdata.chosenCoupon}},computed:{...(0,r.aH)("formdata",["switchToggleState"])},methods:{chooseCoupon(){if(!this.isFirstChargeUsed)return null;this.tempChosenCoupon&&this.$store.commit("formdata/setChosenCoupon",this.tempChosenCoupon),this.$emit("close"),this.$root.$emit("couponChoose"),this.tempChosenCoupon.FE_INDEX&&this.$root.$emit("couponChosen")}},mounted(){this.$gcbk("switch.enableAnimation",!1)&&(gsap&&gsap.from(".ticket-chosen-wrapper .pop-main",{top:"45%",duration:.4,ease:"back",clearProps:!0}),gsap&&gsap.from(".ticket-chosen-wrapper",{backgroundColor:"rgba(0, 0, 0, 0)",duration:.4,clearProps:!0}))}},E=I,P=(0,g.A)(E,c,d,!1,null,"bf6f092c",null),k=P.exports,N=s(44374),S=s(90176);const x=t=>(100*(1-t)).toFixed(0),A=t=>100*(t-1)&&(100*(t-1)).toFixed(0),D=t=>t.coin-t.level_coin;var O={name:"CouponChoose",components:{OverSizeScale:N.A,CommonPart:n.A,CouponChoosePop:k},data(){return{activity:{notObtainedList:[],hadObtainedList:[],chosenIndex:-1,showPop:!1,isFirstPayUsed:!0,timeInterval:void 0,isLoadingCoupon:!1}}},computed:{...(0,r.aH)(["urlParams","isArZone","currencyUnit"]),...(0,r.aH)("formdata",["chosenChannel","chosenCoupon","chosenDiamond","isFixedRebateWork"]),...(0,r.L8)("formdata",["FinalPriceState"]),availableCouponNum(){const t=this.activity.hadObtainedList;return t&&t.length?t.filter(t=>t.feType.includes("_coupon")&&("leaveCount"in t&&t.leaveCount>0||!("leaveCount"in t))).length:0}},methods:{initActInfo(t){const{timeInterval:e}=this.activity;e&&clearInterval(e),this.activity={notObtainedList:[],hadObtainedList:[],chosenIndex:-1,showPop:!1,isFirstPayUsed:!0,timeInterval:void 0},this.$store.commit("formdata/resetCouponInfo");const s=this.chosenDiamond.price;this.$root.$emit("setDefaultDiscountInfo",{price:s,discount_price:(.95*s).toFixed(2),feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_1",rateWidthOutPercent:5,type:"fixed_discount"}),(sessionStorage.getItem("reopenCoupon")||t)&&(sessionStorage.removeItem("reopenCoupon"),this.$nextTick(()=>{this.activity.showPop=!0}))},couponSort(t){const e=t;return e.sort((t,e)=>t.coupon_expire_time>e.coupon_expire_time?1:t.coupon_expire_time===e.coupon_expire_time?parseFloat(t.rateWidthOutPercent)<parseFloat(e.rateWidthOutPercent)?1:parseFloat(t.rateWidthOutPercent)===parseFloat(e.rateWidthOutPercent)?0:-1:-1),e},loadActivity(t=!1){const e={};e.price=this.chosenDiamond.price,e.product_id=this.chosenDiamond.product_id;const{type:s,chosenNum:i}=this.chosenDiamond;if(2===s&&(e.custom_multiple=i),!e.product_id)return null;this.chosenChannel&&(e.channel_id=this.chosenChannel.channel_id,e.sub_channel_id=this.chosenChannel.sub_channel_id),this.$store.state.IS_CHECKOUT_SDK&&(e.package_type=this.chosenDiamond.package_type),this.$store.state.IS_CHECKOUT_SDK&&!this.$store.state.country&&(e.country="US",e.currency="USD"),this.$loading.show(),this.couponLoading=!0,(0,o.QC)(e).then(s=>{this.initActInfo();const{code:i,data:a,message:n}=s;if(t&&(this.isFixedRebateWork?a.fixed_discount=[]:a.fixed_rebate=[],this.$store.commit("formdata/toggleCoupon")),0!==i)throw Error(n);{this.$store.getters["formdata/TWMyCard"]&&(a.first_pay=a.coupon=a.deduct=a.fixed_discount=[]),this.$store.commit("formdata/setIsInit",!0),this.$store.state.gameinfo.isKOA&&this.$store.commit("formdata/setFirstPayProducts",a.range_first_pay||[]),"foundation"===this.$gameName&&this.fixFoundationCoupon(e,a),this.adapterCouponType(a);let t=a.first_pay||[];t=t.map((t,s)=>({...t,feType:"first_pay",rateWidthOutPercent:x(t.discount),rate:`${x(t.discount)}%`,FE_INDEX:`first_pay_${s}`,productId:e.product_id})),a.first_pay&&a.first_pay.length&&!a.first_pay[0].discount&&(t=[]),(a.first_pay_rebate||[]).length&&(t=(a.first_pay_rebate||[]).map((t,s)=>({...t,feType:"first_pay_rebate",rate:`${D(t)}`,FE_INDEX:`first_pay_rebate_${s}`,productId:e.product_id})));let s=a.coupon||[];s=s.map((t,s)=>({...t,feType:"discount_coupon",rateWidthOutPercent:x(t.discount),rate:`${x(t.discount)}%`,FE_INDEX:`discount_coupon_${s}`,productId:e.product_id}));const i=s.filter(t=>t.is_received&&t.is_invalid),n=s.filter(t=>t.is_received&&!t.is_invalid);let o=a.deduct||[];o=o.map((t,s)=>({...t,feType:"cash_coupon",FE_INDEX:`cash_coupon_${s}`,productId:e.product_id}));const r=o.filter(t=>t.is_received&&t.is_invalid),c=o.filter(t=>t.is_received&&!t.is_invalid);let d=a.rebate||[];d=d.map((t,s)=>({...t,feType:"rebate_coupon",FE_INDEX:`rebate_coupon_${s}`,rate:`${A(t.discount)}%`,rateWidthOutPercent:A(t.discount),productId:e.product_id})),d=this.couponSort(d);const l=d.filter(t=>t.is_received&&t.is_invalid),h=d.filter(t=>t.is_received&&!t.is_invalid);let p=a.fixed_discount||[];p=p.map((t,e)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:`fixed_discount_coupon_${e}`,rateWidthOutPercent:x(t.discount)})),p.length&&this.$store.commit("formdata/setFixedCoupon",p[0]);let u=a.fixed_rebate||[];u=u.map((t,s)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:A(t.discount),rate:`${A(t.discount)}%`,FE_INDEX:`fixed_rebate_${s}`,productId:e.product_id})),this.$store.commit("formdata/setFixedRebate",u.length?u[0]:{});let m=a.product_fixed_rebate||[];m=m.map((t,s)=>({...t,feType:"fixed_dynamic_rebate",rateWidthOutPercent:A(t.discount),rate:`${A(t.discount)}%`,FE_INDEX:`fixed_dynamic_rebate_${s}`,productId:e.product_id})),this.$store.commit("formdata/setFixedDynamicRebate",{chosen:m[0]||{},all:a.range_product_fixed_rebate||[]}),this.calcLeaveTime([...s,...o,...d].filter(t=>t.is_received));const _=[...t,...l,...r,...i,...h,...c,...n];this.activity.isFirstPayUsed=0===t.length,this.$store.commit("formdata/setFirstPayStatus",this.activity.isFirstPayUsed),this.activity.hadObtainedList=_;const g=[...l,...r,...i];if(t.length)this.$store.commit("formdata/setChosenCoupon",_[0]);else{const t=this.$store.state.IS_CHECKOUT_SDK_V2&&[...p,...u].length>0;if(g.length&&!t){let t=0;const e=sessionStorage.getItem("reChooseCoupon");e&&(t=_.findIndex(t=>t.coupon_id===+e),t=Math.max(0,t)),this.$store.commit("formdata/setChosenCoupon",_[t])}}this.parsingSdk2Coupon(g),this.activity.hadObtainedList=this.activity.hadObtainedList.map(t=>{if(t.discount_range){const e=this.$store.state.IS_CHECKOUT_SDK,s=e&&t.feType.includes("_coupon")?t.discount_price_range.split("-"):t.discount_range.split("-"),i=this.$store.state.currency;switch(this.$store.state.gameinfo.gameCode){case"KOA":case"MO":"cash_coupon"===t.feType&&(t.langValue=this.$t("min_cash_available_num",{0:s[0],1:this.$vt("tokenName")})),"discount_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:s[1],1:this.$vt("tokenName")})),"rebate_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:s[1],1:this.$vt("tokenName")}));break;default:s.length>1?"0"===s[0]?t.langValue=this.$t("max_cash_available_num",{0:s[1],1:this.$vt("tokenName")}):"0"===s[1]?t.langValue=this.$t("min_cash_available_num",{0:s[0],1:this.$vt("tokenName")}):t.langValue=this.$t("btw_cash_available_num2",{1:s[0],2:s[1],0:this.$vt("tokenName")}):t.langValue=this.$t("cash-num-eq-to",{0:s[0],1:this.$vt("tokenName")})}this.$store.state.IS_CHECKOUT_SDK&&(s.length>1?"0"===s[0]?t.langValue=this.$t("max_cash_available_num",{0:i,1:s[1]}):"0"===s[1]?t.langValue=this.$t("min_cash_available_num",{0:i,1:s[0]}):t.langValue=this.$t("btw_cash_available_num",{0:i,1:s[0],2:s[1]}):t.langValue=this.$t("cash-num-eq-to",{0:i,1:s[0]}))}return t});const f=[...d,...o,...s].filter(t=>!t.is_received),y={"login_0.9":[],"comm_third_0.8":[],"comm_third_0.9":[]},C={"login_0.9":"login_gain_coupon","comm_third_0.9":"invite_gain_coupon","comm_third_0.8":"invite_gain_coupon"};f.forEach(t=>{t.type.includes("login_")&&.9===t.discount&&y["login_0.9"].push(t),t.type.includes("comm_third")&&.8===t.discount&&y["comm_third_0.8"].push(t),t.type.includes("comm_third")&&.9===t.discount&&y["comm_third_0.9"].push(t)});for(const[e,a]of Object.entries(y))a.length&&this.activity.notObtainedList.push({...a[0],num:a.length,langKey:C[e]});this.activity.notObtainedList=this.activity.notObtainedList.sort((t,e)=>t.discount-e.discount),this.$root.$emit("activityInitEnd")}}).catch(t=>{this.initActInfo(),this.$toast.err(this.$t("network_err")),console.error(`优惠券初始化失败：${t.message}`)}).finally(()=>{this.couponLoading=!1,this.$loading.hide(),sessionStorage.removeItem("reChooseCoupon")})},fixActivityInfo(){if(this.couponLoading)return null;const t=this.chosenCoupon.FE_INDEX,e={};e.price=this.chosenDiamond.price,e.product_id=this.chosenDiamond.product_id;const{type:s,chosenNum:i}=this.chosenDiamond;if(2===s&&(e.custom_multiple=i),!e.product_id)return null;this.chosenChannel&&(e.channel_id=this.chosenChannel.channel_id,e.sub_channel_id=this.chosenChannel.sub_channel_id),this.$store.state.IS_CHECKOUT_SDK&&(e.package_type=this.chosenDiamond.package_type),this.$store.state.IS_CHECKOUT_SDK&&!this.$store.state.country&&(e.country="US",e.currency="USD"),(0,o.QC)(e).then(s=>{this.initActInfo(this.activity.showPop);const{code:i,data:a,message:n}=s;if(0!==i)throw Error(n);{this.$store.getters["formdata/TWMyCard"]&&(a.first_pay=a.coupon=a.deduct=a.fixed_discount=[]),this.$store.commit("formdata/setIsInit",!0),"foundation"===this.$gameName&&this.fixFoundationCoupon(e,a),this.adapterCouponType(a);let s=a.first_pay||[];s=s.map((t,s)=>({...t,feType:"first_pay",rateWidthOutPercent:x(t.discount),rate:`${x(t.discount)}%`,FE_INDEX:`first_pay_${s}`,productId:e.product_id})),a.first_pay&&a.first_pay.length&&!a.first_pay[0].discount&&(s=[]),(a.first_pay_rebate||[]).length&&(s=(a.first_pay_rebate||[]).map((t,s)=>({...t,feType:"first_pay_rebate",rate:`${D(t)}`,FE_INDEX:`first_pay_rebate_${s}`,productId:e.product_id})));let i=a.coupon||[];i=i.map((t,s)=>({...t,feType:"discount_coupon",rateWidthOutPercent:x(t.discount),rate:`${x(t.discount)}%`,FE_INDEX:`discount_coupon_${s}`,productId:e.product_id}));const n=i.filter(t=>t.is_received&&t.is_invalid),o=i.filter(t=>t.is_received&&!t.is_invalid);let r=a.deduct||[];r=r.map((t,s)=>({...t,feType:"cash_coupon",FE_INDEX:`cash_coupon_${s}`,productId:e.product_id}));const c=r.filter(t=>t.is_received&&t.is_invalid),d=r.filter(t=>t.is_received&&!t.is_invalid);let l=a.rebate||[];l=l.map((t,s)=>({...t,feType:"rebate_coupon",FE_INDEX:`rebate_coupon_${s}`,rate:`${A(t.discount)}%`,rateWidthOutPercent:A(t.discount),productId:e.product_id})),l=this.couponSort(l);const h=l.filter(t=>t.is_received&&t.is_invalid),p=l.filter(t=>t.is_received&&!t.is_invalid);let u=a.fixed_discount||[];u=u.map((t,e)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:`fixed_discount_coupon_${e}`,rateWidthOutPercent:x(t.discount)})),u.length&&this.$store.commit("formdata/setFixedCoupon",u[0]);let m=a.fixed_rebate||[];m=m.map((t,s)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:A(t.discount),rate:`${A(t.discount)}%`,FE_INDEX:`fixed_rebate_${s}`,productId:e.product_id})),this.$store.commit("formdata/setFixedRebate",m.length?m[0]:{});let _=a.product_fixed_rebate||[];_=_.map((t,s)=>({...t,feType:"fixed_dynamic_rebate",rateWidthOutPercent:A(t.discount),rate:`${A(t.discount)}%`,FE_INDEX:`fixed_dynamic_rebate_${s}`,productId:e.product_id})),this.$store.commit("formdata/setFixedDynamicRebate",{chosen:_[0]||{},all:a.range_product_fixed_rebate||[]}),this.calcLeaveTime([...i,...r,...l].filter(t=>t.is_received));const g=[...s,...h,...c,...n,...p,...d,...o];this.activity.isFirstPayUsed=0===s.length,this.$store.commit("formdata/setFirstPayStatus",this.activity.isFirstPayUsed),this.activity.hadObtainedList=g;const f=[...h,...c,...n];if(t)if(s.length)t===g[0].FE_INDEX&&this.$store.commit("formdata/setChosenCoupon",g[0]);else if(f.length){const e=g.findIndex(e=>e.FE_INDEX===t)||0;this.$store.commit("formdata/setChosenCoupon",g[e])}this.parsingSdk2Coupon(f),this.activity.hadObtainedList=this.activity.hadObtainedList.map(t=>{if(t.discount_range){const e=this.$store.state.IS_CHECKOUT_SDK,s=e&&t.feType.includes("_coupon")?t.discount_price_range.split("-"):t.discount_range.split("-"),i=this.$store.state.currency;switch(this.$store.state.gameinfo.gameCode){case"KOA":case"MO":"cash_coupon"===t.feType&&(t.langValue=this.$t("min_cash_available_num",{0:s[0],1:this.$vt("tokenName")})),"discount_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:s[1],1:this.$vt("tokenName")})),"rebate_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:s[1],1:this.$vt("tokenName")}));break;default:s.length>1?"0"===s[0]?t.langValue=this.$t("max_cash_available_num",{0:s[1],1:this.$vt("tokenName")}):"0"===s[1]?t.langValue=this.$t("min_cash_available_num",{0:s[0],1:this.$vt("tokenName")}):t.langValue=this.$t("btw_cash_available_num2",{1:s[0],2:s[1],0:this.$vt("tokenName")}):t.langValue=this.$t("cash-num-eq-to",{0:s[0],1:this.$vt("tokenName")})}this.$store.state.IS_CHECKOUT_SDK&&(s.length>1?"0"===s[0]?t.langValue=this.$t("max_cash_available_num",{0:i,1:s[1]}):"0"===s[1]?t.langValue=this.$t("min_cash_available_num",{0:i,1:s[0]}):t.langValue=this.$t("btw_cash_available_num",{0:i,1:s[0],2:s[1]}):t.langValue=this.$t("cash-num-eq-to",{0:i,1:s[0]}))}return t});const y=[...l,...r,...i].filter(t=>!t.is_received),C={"login_0.9":[],"comm_third_0.8":[],"comm_third_0.9":[]},v={"login_0.9":"login_gain_coupon","comm_third_0.9":"invite_gain_coupon","comm_third_0.8":"invite_gain_coupon"};y.forEach(t=>{t.type.includes("login_")&&.9===t.discount&&C["login_0.9"].push(t),t.type.includes("comm_third")&&.8===t.discount&&C["comm_third_0.8"].push(t),t.type.includes("comm_third")&&.9===t.discount&&C["comm_third_0.9"].push(t)});for(const[t,e]of Object.entries(C))e.length&&this.activity.notObtainedList.push({...e[0],num:e.length,langKey:v[t]});this.activity.notObtainedList=this.activity.notObtainedList.sort((t,e)=>t.discount-e.discount)}}).catch(t=>{this.initActInfo(),this.$toast.err(this.$t("network_err")),console.error(`优惠券初始化失败：${t.message}`)})},calcLeaveTime(t){const e=t=>t<10?`0${Math.floor(t)}`:Math.floor(t),s=t=>`${Math.floor(t/3600/24)}d ${e(t/3600%24)} : ${e(t/60%60)} : ${e(t%60)}`,i=t.filter(t=>t.coupon_expire_time&&t.coupon_expire_time>0);for(const a of Object.values(i)){const t=a.leaveCount=a.coupon_expire_time;a.showLeaveDate=s(t)}this.activity.timeInterval=setInterval(()=>{for(const t of Object.values(i)){const e=t.leaveCount-1;e>=0&&(t.leaveCount--,t.showLeaveDate=s(e)),0===e&&this.chosenCoupon.FE_INDEX===t.FE_INDEX&&(this.$store.commit("formdata/setChosenCoupon",{}),this.$root.$emit("couponChoose"))}},1e3)},closeCouponPop(t){this.activity.showPop=!1,this.$root.$emit("TicketPopClose")},parsingSdk2Coupon(t){this.$store.state.IS_CHECKOUT_SDK_V2&&this.$root.$emit("updateSdk2CouponList",t)},fixFoundationCoupon(t,e){const s=e.coin_level_first_pay;if(!s||!s.length)return null;const i=s.map(t=>(t.rate=`${D(t)}`,t.feType="first_pay_rebate",t));i.length&&this.$store.commit("formdata/setFirstPayProducts",[{product_discount_range:i}]);const a=i.filter(e=>e.product_id===t.product_id)||[];a.length&&(a[0].act_type="coin_level_first_pay",e.first_pay_rebate=a)},adapterCouponType(t){this.$store.state.IS_CHECKOUT_SDK_V2&&(t.first_pay=t.direct_first_pay,t.first_pay_rebate=t.direct_first_pay_rebate,t.fixed_discount=t.direct_fixed_discount,t.fixed_rebate=t.direct_fixed_rebate,Reflect.deleteProperty(t,"direct_first_pay"),Reflect.deleteProperty(t,"direct_first_pay_rebate"),Reflect.deleteProperty(t,"direct_fixed_discount"),Reflect.deleteProperty(t,"direct_fixed_rebate"))}},created(){const t="$store.state.formdata.chosenDiamond.product_id";this.unwatch=this.$watch(t,t=>t&&this.loadActivity(),{immediate:!0});const e="$store.state.formdata.chosenDiamond.totalDiamond";this.unwatch=this.$watch(e,(t,e)=>t&&e&&this.loadActivity()),this.$root.$once("loginSuccess",()=>this.loadActivity());const s="$store.state.formdata.chosenChannel.FE_CHANNEL_ID";this.unwatch=this.$watch(s,t=>t&&this.fixActivityInfo()),this.$root.$on("couponToggleSuccess",()=>this.loadActivity()),this.$root.$on("reloadActivity",()=>this.loadActivity()),this.$root.$on("toggleFixedCoupon",t=>this.loadActivity(t))},mounted(){S.A.ios()&&this.$watch("activity.showPop",t=>this.$root.$emit("showCouponPop",t))},beforeDestroy(){this.unwatch&&this.unwatch()}},T=O,F=(0,g.A)(T,i,a,!1,null,"1c5021e2",null),L=F.exports},73082:function(t,e,s){s.d(e,{A:function(){return d}});var i=function(){var t=this;t._self._c;return t._m(0)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"image-wrapper"},[e("img",{attrs:{src:s(35895),alt:""}})])}],n={name:"channelLogo"},o=n,r=s(81656),c=(0,r.A)(o,i,a,!1,null,"5ba3ee7d",null),d=c.exports}}]);