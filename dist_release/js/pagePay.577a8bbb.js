"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[3568],{49811:function(t,e,s){s.d(e,{A:function(){return d}});var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"extra-diamond-wrapper",on:{click:function(t){t.stopPropagation()}}},[e("div",{staticClass:"select-wrapper"},[e("div",{class:["label-wrapper",{"label-wrapper-active":t.chosenSpecialDiamond.product_id}],on:{click:function(e){t.showList=!t.showList}}},[t._m(0),e("div",{staticClass:"right-part"},[t.chosenSpecialDiamond.product_id?e("div",{staticClass:"chosen-diamond"},[e("span",{staticClass:"products"},[t._v(t._s(t.chosenSpecialDiamond.coin)+" "),e("i")]),e("span",[t._v(t._s(t.chosenSpecialDiamond.price)+" "+t._s(t.chosenSpecialDiamond.currency_symbol))])]):e("div",{staticClass:"more"},[t._v(t._s(t.$t("load_more")))]),e("i",{class:[{"toggle-active":t.showList}]})])]),e("transition",{attrs:{name:"option"}},[t.showList?e("div",{staticClass:"option-list"},t._l(t.extraList,function(s,o){return e("div",{key:o,class:["option-item",{"option-item-active":s.product_id===t.chosenSpecialDiamond.product_id}],on:{click:function(e){return t.toggleStatus(o)}}},[e("span",{staticClass:"products"},[t._v(t._s(s.coin)+" "),e("i")]),e("span",[t._v(t._s(s.price)+" "+t._s(s.currency_symbol))])])}),0):t._e()])],1)])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"my-card-logo"},[e("img",{attrs:{src:s(50423),alt:""}})])}],a=s(95353),n={name:"extraDiamond",data(){return{showList:!1,extraList:[]}},computed:{...(0,a.aH)("formdata",["chosenDiamond"]),chosenSpecialDiamond(){return this.$store.getters["formdata/TWMyCard"]?this.chosenDiamond:{}}},methods:{toggleStatus(t){this.$store.commit("formdata/setChosenDiamond",this.extraList[t]),this.showList=!1}},created(){this.$root.$on("updateSpecialDiamond",t=>{this.extraList=t}),this.$root.$on("BodyClick",()=>{this.showList=!1})}},r=n,c=s(81656),l=(0,c.A)(r,o,i,!1,null,"4bcbdca5",null),d=l.exports},50423:function(t){t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL215Y2FyZC1sb2dvLmU4MDI3ZmE0LnBuZyI7"},78336:function(t,e,s){s.r(e),s.d(e,{default:function(){return mt}});var o=function(){var t=this,e=t._self._c;return e("div",{class:["shopping-wrapper",t.$gameName,{sdk:t.IS_CHECKOUT_SDK}],attrs:{id:"page-pay-wrapper"}},[t.isPc&&!t.IS_CHECKOUT_SDK?[e("section",{staticClass:"pc-content-wrapper"},[e("add-screen-btn"),e("add-ios-safari-btn"),e("div",{staticClass:"page-title"},[t._v(t._s(t.$vt("pageTitle"))+" "),e("span",[t._v(t._s(t.$t("mobile_available"))+" "),e("i")])]),e("div",{staticClass:"content-center content-center__main"},[e("div",{staticClass:"left-part"},[e("div",{staticClass:"logo"}),e("div",{staticClass:"name"}),e("p",{staticClass:"description"},[e("span",[t._v(t._s(t.$vt("whatIsDiamondTitle")))]),t.$store.state.functionSwitch.showPcDiscountTips?e("span",[t._v(t._s(t.$vt("discount95Tips")))]):t._e()]),e("div",{staticClass:"charge-construction",on:{click:function(e){return t.$root.$emit("showPop","ChargeConstruction")}}},[e("i"),t._v(t._s(t.$t("construction_title"))+" ")])]),e("div",{staticClass:"right-part"},[e("login-module"),t.boon?e("entrance-of-boon"):t._e(),e("coupon-choose"),e("diamond-choose-k-o-a"),e("channel-choose"),t.$store.state.gameinfo.isCn?e("checkout-counter-cn"):e("checkout-counter"),e("div",{staticClass:"shop-btn"},[e("span",{staticClass:"click-btn",class:[{disable:t.requestLoading||t.$store.getters["riskPolicy/forbiddenAccess"]}],on:{click:function(e){return t.judgeRisk()}}},[t._v(t._s(t.$t("shop_now"))+" "),t.vip.isNewUser||!t.isLogin?e("i"):t._e()])]),"DE"===t.$store.state.country&&t.isLogin?e("common-part",[e("private-permission")],1):t._e()],1)])],1),t.$store.state.gameinfo.isCn?e("common-footer-cn"):t._e(),t.$store.state.gameinfo.mainBody?e("CommonFooterPuzala"):e("common-footer")]:t._e(),t.isMobile&&!t.IS_CHECKOUT_SDK?[e("div",{staticClass:"mobile-body-wrapper"},[e("add-screen-btn"),e("add-ios-safari-btn"),e("login-module"),t.boon?e("entrance-of-boon"):t._e(),e("coupon-choose"),e("diamond-choose-k-o-a"),e("channel-choose"),"DE"===t.$store.state.country&&t.isLogin?e("common-part",[e("private-permission")],1):t.showMobilePolicy?e("refund-policy"):t._e(),t.$store.state.gameinfo.isCn?e("common-footer-cn"):t._e()],1),e("checkout-footer",{attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(e){return t.judgeRisk()}}})]:t._e(),t.IS_CHECKOUT_SDK?[e("div",{staticClass:"sdk-body-wrapper"},[e("direct-gift-package"),e("coupon-choose"),e("channel-choose"),"DE"===t.$store.state.country&&t.isLogin?e("common-part",[e("private-permission")],1):t._e(),e("checkout-counter-s-d-k",{attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(e){return t.judgeRisk()}}}),e("login-module",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]})],1),e("checkout-footer",{attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(e){return t.judgeRisk()}}}),e("common-footer")]:t._e()],2)},i=[],a=function(){var t=this,e=t._self._c;return t.showFooter?e("div",{staticClass:"copyright"},[e("img",{staticStyle:{"vertical-align":"text-bottom","padding-right":"10px"},attrs:{src:s(35895),alt:"funplus"}}),t._m(0)]):t._e()},n=[function(){var t=this,e=t._self._c;return e("p",[t._v(" © FUNPLUS INTERNATIONAL AG(Bahnhofstrasse 2, 6300 Zug) - ALL RIGHTS RESERVED "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://funplus.com/privacy-policy/",target:"_blank"}},[t._v("Privacy Policy")]),t._v(" , "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://funplus.com/terms-conditions/",target:"_blank"}},[t._v("Terms and Conditions")]),t._v(" and "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Refund Policy")]),t._v(". ")])}],r={name:"CommonFooter",computed:{showFooter(){return!["aof","rom"].includes(this.$gameName)}}},c=r,l=s(81656),d=(0,l.A)(c,a,n,!1,null,"ae65575c",null),p=d.exports,u=s(95044),h=s(45662),_=s(69139),m=function(){var t=this,e=t._self._c;return t.expandMode?e("common-part",{class:["expand",t.$gameName],attrs:{id:"checkout-counter-expand"},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"sub-total"},[t._v(t._s(t.$t("tax-sub-total")))]),t.taxCost?e("div",{staticClass:"tax"},[t._v(t._s(t.$t("tax-txt")))]):t._e(),t.extraCost?e("div",{staticClass:"tax"},[t._v(t._s(t.$t("extra-txt")))]):t._e(),e("div",{staticClass:"total"},[t._v(" "+t._s(t.$t("totalPrice")))]),e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")])]},proxy:!0}],null,!1,2468513191)},[e("div",{staticClass:"price-wrapper"},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.rawNowPrice))]),t.FinalPriceState.rawOriginPrice?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.FinalPriceState.rawOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()]),t.taxCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.taxCost)+" "+t._s(t.currencyUnit))]):t._e(),t.extraCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.extraCost)+" "+t._s(t.currencyUnit))]):t._e(),e("div",{staticClass:"final-price"},[t._v(t._s(t.FinalPriceState.finalNowPrice))])]):e("common-part",{class:["normal",t.$gameName],attrs:{id:"checkout-counter-normal"},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"total"},[t._v(t._s(t.$t("totalPrice")))]),t.showTaxBtn?e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")]):t._e()]},proxy:!0}])},[e("div",{staticClass:"total-price",attrs:{id:"total-price"}},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalNowPrice))]),t.FinalPriceState.finalOriginPrice?e("span",{staticClass:"origin-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()])])},f=[],v=s(49933),C=s(95353),g={name:"CheckoutCounterTax",components:{CommonPart:v.A},data(){return{expandMode:!1}},computed:{...(0,C.aH)(["urlParams","isArZone","currencyUnit","IS_CHECKOUT_SDK"]),...(0,C.aH)("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip"]),...(0,C.aH)("gameinfo",["defaultDiscount"]),...(0,C.aH)("userinfo",["isLogin"]),...(0,C.L8)("formdata",["FinalPriceState","getRebateCoin","getSDKRebateCoin"]),taxCost(){return this.chosenCoupon.taxation||this.FinalPriceState.taxation||this.chosenDiamond.taxation},extraCost(){return this.chosenCoupon.extra_fee_amount||this.FinalPriceState.extra_fee_amount||this.chosenDiamond.extra_fee_amount},showTaxBtn(){return this.taxCost||this.extraCost}},watch:{showTaxBtn(t){t||(this.expandMode=!1)}}},w={name:"CheckoutCounterTax",mixins:[g]},$=w,x=(0,l.A)($,m,f,!1,null,"42731809",null),P=x.exports,b=function(){var t=this,e=t._self._c;return e("common-part",{class:t.$store.state.gameinfo.gameCode,staticStyle:{color:"#000"},attrs:{"label-font":"合计"}},[e("div",{staticClass:"total-price",attrs:{id:"total-price"}},[t.chosenCoupon.FE_INDEX?[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},["first_pay"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.discount_price)))]:t._e(),"discount_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.discount_price)))]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.price)))]:t._e(),"rebate_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.price)))]:t._e()],2),"rebate_coupon"!==t.chosenCoupon.feType?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenDiamond.nowPrice||t.chosenDiamond.price)))]):t._e(),e("div",{staticClass:"off-count-tips"},["first_pay"===t.chosenCoupon.feType?[t._v(t._s(`首充 ${t.chosenCoupon.rateWidthOutPercent} 折`))]:t._e(),"discount_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.chosenCoupon.rateWidthOutPercent)+"折 优惠券")]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v("减 "+t._s(t.currencyUnit)+t._s(t.chosenCoupon.deduct_price))])]:t._e(),"rebate_coupon"===t.chosenCoupon.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v("送 "+t._s(t.chosenCoupon.rate)+" "),e("i",{staticClass:"diamond-icon"})])]:t._e()],2)]:e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenDiamond.nowPriceWidthTax||t.chosenDiamond.price)))])],2)])},S=[],y={name:"CheckoutCounter",components:{CommonPart:v.A},data(){return{defaultDiscountInfo:{}}},computed:{...(0,C.aH)(["urlParams","isArZone","currencyUnit"]),...(0,C.aH)("formdata",["chosenChannel","chosenDiamond","chosenCoupon"]),...(0,C.aH)("gameinfo",["defaultDiscount"])},created(){this.$root.$on("setDefaultDiscountInfo",t=>{this.defaultDiscountInfo=t})}},k=y,A=(0,l.A)(k,b,S,!1,null,"6eb7f255",null),T=A.exports,D=s(87367),L=s(13626),I=function(){var t=this,e=t._self._c;return e("div",{class:["add-screen-pc-wrapper",[t.$gameName]]},[e("transition",{attrs:{name:"addScreen"}},[t.deferredPrompt&&t.$store.state.isMobile?e("div",{staticClass:"add-to-main-screen__mobile",on:{click:t.goInstall}},[t._v(t._s(t.$t("add-to-screen")))]):t._e()]),t.deferredPrompt&&t.$store.state.isPc?e("div",{staticClass:"add-to-main-screen__pc"},[t._v(" "+t._s(t.$t("add_screen_des1"))+" "+t._s(t.$t("add_screen_des2"))+" "),e("span",{staticClass:"click-btn",on:{click:t.goInstall}},[t._v(t._s(t.$t("add-to-screen")))])]):t._e()],1)},F=[];function O(){const t=window.matchMedia("(display-mode: standalone)").matches;return document.referrer.startsWith("android-app://")?"twa":navigator.standalone||t?"standalone":"browser"}var M={name:"AddScreenBtn",data(){return{deferredPrompt:window.__deferredPrompt||void 0}},methods:{showBtn(){window.addEventListener("beforeinstallprompt",t=>{t.preventDefault(),this.deferredPrompt=t})},goInstall(){this.$gtag.event("click_chrome_install",{event_label:this.$store.state.isMobile?"mobile":"pc"}),this.deferredPrompt.prompt(),this.deferredPrompt.userChoice.then(t=>{if("accepted"===t.outcome){console.log("User accepted the A2HS prompt");const t=setInterval(()=>{"standalone"===O()&&(clearInterval(t),this.$root.$emit("installSuccessful"))},1e3)}else console.log("User dismissed the A2HS prompt");this.deferredPrompt=void 0})}},created(){const t=(navigator.userAgent||"").toLowerCase();if(t.includes("chrome")&&this.showBtn(),this.$gcbk("switch.enableAnimation",!1)&&this.$store.state.isPc){const t=this.$watch("deferredPrompt",e=>{e&&(this.$nextTick(()=>gsap&&gsap.from(".add-to-main-screen__pc",{height:0,duration:.4,clearProps:"height"})),t())})}}},N=M,U=(0,l.A)(N,I,F,!1,null,"4d53a49d",null),E=U.exports,H=function(){var t=this,e=t._self._c;return e("div",{class:["add-ios-wrapper",t.$gameName],attrs:{id:"add-ios-wrapper"}},[e("transition",{attrs:{name:"addScreen"}},[t.isShowBtn&&t.showTimes<t.defaultShowTimes||!this.hadInstall?e("div",{staticClass:"add-to-main-screen__mobile heart",on:{click:t.showGuide}},[t._v(t._s(t.$t("add-to-screen")))]):t._e()]),e("transition",{attrs:{name:"iosGuide"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowGuide,expression:"isShowGuide"}],staticClass:"ios-guide",class:{active:t.isShowGuide},on:{click:function(e){t.isShowGuide=!1}}},[e("div",{staticClass:"guide-wrap",on:{click:function(t){t.stopPropagation()}}},[e("div",{staticClass:"close",on:{click:function(e){t.isShowGuide=!1}}}),e("div",{staticClass:"title"},[t._v(t._s(t.$t("ios-guide-0")))]),e("div",{staticClass:"subtitle"},[t._v(t._s(t.$t("ios-guide-2"))+" "+t._s(t.$t("ios-guide-3")))]),e("div",{staticClass:"logo"}),e("div",{staticClass:"game"},[t._v(t._s(t.gameName)+" TopupCenter")]),e("div",{staticClass:"phone"}),e("i18n",{staticClass:"txt",attrs:{path:"ios-guide-4",tag:"div"}},[e("div",{staticClass:"up",attrs:{slot:0},slot:0})]),e("i18n",{staticClass:"txt",attrs:{path:"ios-guide-5",tag:"div"}},[e("div",{staticClass:"add",attrs:{slot:0},slot:0}),e("template",{slot:1},[t._v(t._s(t.$t("ios-guide-1")))])],2),e("div",{staticClass:"mune"},[e("div",[t._v(t._s(t.$t("ios-guide-1")))])]),e("div",{staticClass:"arr"})],1)])])],1)},B=[],Z=s(52112),G={name:"AddIosSafariBtn",data(){return{isShowBtn:!1,isShowGuide:!1,showTimes:Number(window.localStorage.getItem("_i_g_times")||0),defaultShowTimes:3,hadInstall:!0}},created(){const t=window.navigator.userAgent.toLowerCase(),e=window.navigator.standalone;var s=t.indexOf("applewebkit")>-1&&t.indexOf("mobile")>-1&&t.indexOf("safari")>-1&&-1===t.indexOf("linux")&&-1===t.indexOf("android")&&-1===t.indexOf("chrome")&&-1===t.indexOf("ios")&&-1===t.indexOf("browser");s&&this.$store.state.isMobile&&!e&&(this.showTimes<this.defaultShowTimes&&(this.isShowBtn=!0),window.localStorage.setItem("_i_g_times",this.showTimes+1)),s&&this.$store.state.isMobile&&!e&&this.$root.$on("loginSuccess",()=>this.rejudge()),this.$root.$on("mobileSafariGuide",()=>{this.isShowGuide=!0})},methods:{showGuide(){this.$gtag.event("click_guide",{event_label:"safari"}),this.showTimes=this.defaultShowTimes,window.localStorage.setItem("_i_g_times",this.showTimes),this.isShowGuide=!0},rejudge(){const{projectId:t,pwaOpenAction:e}=this.$gcbk("apiParams.boonAme",{});if(!e)return;const s={p0:"web",p1:t,p2:e};this.$loading.show(),(0,D.OL)(s).then(t=>{if("standalone"===(0,Z.TH)())return!1;const{data:e=[],code:s}=t;if(0===s&&(this.hadInstall=1===e.length,!this.hadInstall)){const t=setInterval(()=>{"standalone"===(0,Z.TH)()&&(clearInterval(t),this.hadInstall=!0)},2e3)}}).finally(()=>this.$loading.hide())}},computed:{gameName(){return this.$gcbk("gameinfo.sortName")||(this.$store.state.gameinfo.game||"").toUpperCase()}}},R=G,K=(0,l.A)(R,H,B,!1,null,"6039a47f",null),z=K.exports,q=s(317),W=function(){var t=this,e=t._self._c;return e("common-part",{staticClass:"entrance-part"},[e("div",{staticClass:"entrance-wrapper",on:{click:t.go}},[t.$store.state.formdata.showDoubleExperience?e("div",{staticClass:"toggle-coupon-act-level-up"}):e("div",{staticClass:"award",class:{rotate:t.rotate}}),t._v(" "+t._s(t.$t("boon-page-title"))+" "),e("i")])])},j=[];const{projectId:X,pwaOpenAction:Y,loginAction:J,getLoginReward:Q,getPwaReward:V}=window.$gcbk("apiParams.boonAme",{});var tt={name:"EntranceOfBoon",components:{CommonPart:v.A},data(){return{clicked:!1,gain:!1}},computed:{...(0,C.aH)("userinfo",["isLogin"]),rotate(){return!this.isLogin||!this.clicked&&!this.gain}},methods:{getStatus(){const t={p0:"web",p1:X,p2:`${Q},${V}`};this.$loading.show(),(0,D.OL)(t).then(t=>{const{data:e=[],code:s}=t;0===s&&(2===e.length&&(this.gain=!0),"KOA"===this.$store.state.gameinfo.gameCode&&(this.gain=this.gain&&this.$store.state.formdata.gotDailyReward))}).finally(()=>this.$loading.hide())},go(){this.clicked=!0,this.$root.$emit("showPop","BoonPop")},informServer(t){const e={p0:"web",p1:X};(0,D.AO)({p2:t,...e}).then(e=>{const{code:s}=e;0===s&&this.getReward(t)})},getReward(t){const e={p2:+t+1},s={p0:"web",p1:X};this.$loading.show(),(0,D.Pp)({...e,...s}).finally(()=>this.$loading.hide())},checkAddBtn(){this.setInterval=setInterval(()=>{const t=document.querySelector(".add-to-main-screen__mobile"),e=document.querySelector(".mobile-body-wrapper");t?(t.style.zoom=.75,e.style.paddingTop="1.3rem"):e.style.paddingTop="0.53333rem"},1e3)}},created(){this.$root.$on("loginEnd",t=>{"standalone"===Z.Y_?this.informServer(Y):(window.$event=this.$root,this.$root.$on("installSuccessful",()=>this.informServer(Y))),this.informServer(J),this.getStatus()}),this.$store.state.isMobile&&this.checkAddBtn()},beforeDestroy(){this.setInterval&&clearInterval(this.setInterval)}},et=tt,st=(0,l.A)(et,W,j,!1,null,null,null),ot=st.exports,it=s(33960),at=function(){var t=this,e=t._self._c;return e("common-part",{staticClass:"checkout-counter-sdk-b",class:[t.$gameName],attrs:{labelFont:t.$t("totalPrice"),id:"checkout-counter-sdk-b"}},[t.expandMode?e("div",{staticClass:"expand",class:[t.$gameName]},[e("div",{staticClass:"price-wrapper"},[e("span",{staticClass:"sub-total"},[t._v(t._s(t.$t("tax-sub-total"))+":  ")]),e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.rawNowPrice))]),t.FinalPriceState.rawOriginPrice?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.FinalPriceState.rawOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()]),t.taxCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.$t("tax-txt"))+":  "+t._s(t.taxCost)+" "+t._s(t.currencyUnit))]):t._e(),t.extraCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.$t("extra-txt"))+":  "+t._s(t.extraCost)+" "+t._s(t.currencyUnit))]):t._e(),e("div",{staticClass:"final-price"},[t._v(t._s(t.FinalPriceState.finalNowPrice))]),e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")])]):e("div",{staticClass:"normal",class:[t.$gameName]},[e("div",{staticClass:"price-wrapper"},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalNowPrice||"-"))]),t.FinalPriceState.finalOriginPrice?e("span",{staticClass:"origin-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()]),t.showTaxBtn?e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")]):t._e()]),e("div",{staticClass:"click-btn",class:[{disable:t.requestLoading||t.$store.getters["riskPolicy/forbiddenAccess"]}],on:{click:function(e){return t.$emit("purchaseGoods")}}},[t._v(" "+t._s(t.$t("shop_now"))+" "),t.vip.isNewUser?e("i"):t._e()])])},nt=[],rt={name:"CheckoutCounterSDK",props:["requestLoading"],mixins:[g]},ct=rt,lt=(0,l.A)(ct,at,nt,!1,null,"7f327ce4",null),dt=lt.exports,pt=s(87007),ut={name:"Pay",components:{CheckoutCounterSDK:dt,CommonPart:v.A,RefundPolicy:()=>Promise.all([s.e(7996),s.e(1493),s.e(6964),s.e(8406),s.e(471),s.e(9936),s.e(3975),s.e(9614),s.e(3331),s.e(782),s.e(8547),s.e(2707),s.e(513),s.e(5642),s.e(9030),s.e(4402)]).then(s.bind(s,9192)),EntranceOfBoon:ot,DiamondChooseKOA:q.A,AddScreenBtn:E,AddIosSafariBtn:z,CheckoutFooter:L.A,CheckoutCounter:P,CouponChoose:_.A,ChannelChoose:h.A,LoginModule:u.A,CommonFooter:p,CheckoutCounterCn:T,CommonFooterCn:()=>Promise.all([s.e(7996),s.e(1493),s.e(6964),s.e(8406),s.e(471),s.e(9936),s.e(3975),s.e(9614),s.e(3331),s.e(782),s.e(8547),s.e(2707),s.e(513),s.e(5642),s.e(9030),s.e(4402)]).then(s.bind(s,32605)),CommonFooterPuzala:()=>Promise.all([s.e(7996),s.e(1493),s.e(6964),s.e(8406),s.e(471),s.e(9936),s.e(3975),s.e(9614),s.e(3331),s.e(782),s.e(8547),s.e(2707),s.e(513),s.e(5642),s.e(9030),s.e(4402)]).then(s.bind(s,98363)),PrivatePermission:()=>Promise.all([s.e(7996),s.e(1493),s.e(6964),s.e(8406),s.e(471),s.e(9936),s.e(3975),s.e(9614),s.e(3331),s.e(782),s.e(8547),s.e(2707),s.e(513),s.e(5642),s.e(9030),s.e(4402)]).then(s.bind(s,18374)),DirectGiftPackage:it.A},mixins:[pt.A],methods:{showDiamondPop(){this.$root.$on("showWhatIsDiamondPop",()=>{const t=this.$imageLoader("whatsDiamond",[]),e=new Image;e.src=t[0].imageUrl,this.$loading.show(),e.onload=()=>{this.$root.$emit("showPop","WhatIsDiamond"),this.$loading.hide()},e.onerror=()=>{this.$loading.hide()}})},initShowDoubleExperience(){if(this.IS_CHECKOUT_SDK)return null;(0,D.AO)({p0:"web",p1:11,p2:2195,p3:"api"}).then(t=>{const{code:e,data:s}=t;0===e&&this.$store.commit("formdata/switchDoubleExperience",s.double_flage||!1)})}},created(){this.isKOA&&this.initShowDoubleExperience(),this.showDiamondPop()}},ht=ut,_t=(0,l.A)(ht,o,i,!1,null,"762007ce",null),mt=_t.exports}}]);