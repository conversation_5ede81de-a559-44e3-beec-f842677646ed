(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[9936],{77232:function(i,e,o){var a;(function(r,n){"use strict";var t="1.0.40",s="",b="?",w="function",l="undefined",d="object",c="string",u="major",m="model",p="name",h="type",f="vendor",v="version",g="architecture",k="console",x="mobile",y="tablet",_="smarttv",T="wearable",q="embedded",S=500,z="Amazon",N="Apple",A="ASUS",C="BlackBerry",O="Browser",E="Chrome",U="Edge",j="Firefox",P="Google",B="Huawei",M="LG",R="Microsoft",D="Motorola",V="Opera",I="Samsung",G="Sharp",L="Sony",H="Xiaomi",F="Zebra",Z="Facebook",$="Chromium OS",W="Mac OS",X=" Browser",K=function(i,e){var o={};for(var a in i)e[a]&&e[a].length%2===0?o[a]=e[a].concat(i[a]):o[a]=i[a];return o},Q=function(i){for(var e={},o=0;o<i.length;o++)e[i[o].toUpperCase()]=i[o];return e},Y=function(i,e){return typeof i===c&&-1!==J(e).indexOf(J(i))},J=function(i){return i.toLowerCase()},ii=function(i){return typeof i===c?i.replace(/[^\d\.]/g,s).split(".")[0]:n},ei=function(i,e){if(typeof i===c)return i=i.replace(/^\s\s*/,s),typeof e===l?i:i.substring(0,S)},oi=function(i,e){var o,a,r,t,s,b,l=0;while(l<e.length&&!s){var c=e[l],u=e[l+1];o=a=0;while(o<c.length&&!s){if(!c[o])break;if(s=c[o++].exec(i),s)for(r=0;r<u.length;r++)b=s[++a],t=u[r],typeof t===d&&t.length>0?2===t.length?typeof t[1]==w?this[t[0]]=t[1].call(this,b):this[t[0]]=t[1]:3===t.length?typeof t[1]!==w||t[1].exec&&t[1].test?this[t[0]]=b?b.replace(t[1],t[2]):n:this[t[0]]=b?t[1].call(this,b,t[2]):n:4===t.length&&(this[t[0]]=b?t[3].call(this,b.replace(t[1],t[2])):n):this[t]=b||n}l+=2}},ai=function(i,e){for(var o in e)if(typeof e[o]===d&&e[o].length>0){for(var a=0;a<e[o].length;a++)if(Y(e[o][a],i))return o===b?n:o}else if(Y(e[o],i))return o===b?n:o;return e.hasOwnProperty("*")?e["*"]:i},ri={"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},ni={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},ti={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[v,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[v,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,v],[/opios[\/ ]+([\w\.]+)/i],[v,[p,V+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[v,[p,V+" GX"]],[/\bopr\/([\w\.]+)/i],[v,[p,V]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[v,[p,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[v,[p,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,v],[/quark(?:pc)?\/([-\w\.]+)/i],[v,[p,"Quark"]],[/\bddg\/([\w\.]+)/i],[v,[p,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[v,[p,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[v,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[v,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[v,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[v,[p,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[v,[p,"Smart Lenovo "+O]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+O],v],[/\bfocus\/([\w\.]+)/i],[v,[p,j+" Focus"]],[/\bopt\/([\w\.]+)/i],[v,[p,V+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[v,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[v,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[v,[p,V+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[v,[p,"MIUI"+X]],[/fxios\/([\w\.-]+)/i],[v,[p,j]],[/\bqihoobrowser\/?([\w\.]*)/i],[v,[p,"360"]],[/\b(qq)\/([\w\.]+)/i],[[p,/(.+)/,"$1Browser"],v],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1"+X],v],[/samsungbrowser\/([\w\.]+)/i],[v,[p,I+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[v,[p,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[p,"Sogou Mobile"],v],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[p,v],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[p],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[v,p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,Z],v],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[p,v],[/\bgsa\/([\w\.]+) .*safari\//i],[v,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[v,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[v,[p,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,E+" WebView"],v],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[v,[p,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,v],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[v,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[v,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[v,ai,ri]],[/(webkit|khtml)\/([\w\.]+)/i],[p,v],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],v],[/(wolvic|librewolf)\/([\w\.]+)/i],[p,v],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[v,[p,j+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[p,[v,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[p,[v,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,J]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,s,J]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[m,[f,I],[h,y]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[m,[f,I],[h,x]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[m,[f,N],[h,x]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[m,[f,N],[h,y]],[/(macintosh);/i],[m,[f,N]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[m,[f,G],[h,x]],[/(?:honor)([-\w ]+)[;\)]/i],[m,[f,"Honor"],[h,x]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[m,[f,B],[h,y]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[m,[f,B],[h,x]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[m,/_/g," "],[f,H],[h,x]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[m,/_/g," "],[f,H],[h,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[m,[f,"OPPO"],[h,x]],[/\b(opd2\d{3}a?) bui/i],[m,[f,"OPPO"],[h,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[m,[f,"Vivo"],[h,x]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[m,[f,"Realme"],[h,x]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[m,[f,D],[h,x]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[m,[f,D],[h,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[m,[f,M],[h,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[m,[f,M],[h,x]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[m,[f,"Lenovo"],[h,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[m,/_/g," "],[f,"Nokia"],[h,x]],[/(pixel c)\b/i],[m,[f,P],[h,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[m,[f,P],[h,x]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[m,[f,L],[h,x]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[m,"Xperia Tablet"],[f,L],[h,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[m,[f,"OnePlus"],[h,x]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[m,[f,z],[h,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[m,/(.+)/g,"Fire Phone $1"],[f,z],[h,x]],[/(playbook);[-\w\),; ]+(rim)/i],[m,f,[h,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[m,[f,C],[h,x]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[m,[f,A],[h,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[m,[f,A],[h,x]],[/(nexus 9)/i],[m,[f,"HTC"],[h,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[f,[m,/_/g," "],[h,x]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[m,[f,"TCL"],[h,y]],[/(itel) ((\w+))/i],[[f,J],m,[h,ai,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[m,[f,"Acer"],[h,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[m,[f,"Meizu"],[h,x]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[m,[f,"Ulefone"],[h,x]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[m,[f,"Energizer"],[h,x]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[m,[f,"Cat"],[h,x]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[m,[f,"Smartfren"],[h,x]],[/droid.+; (a(?:015|06[35]|142p?))/i],[m,[f,"Nothing"],[h,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[f,m,[h,x]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[f,m,[h,y]],[/(surface duo)/i],[m,[f,R],[h,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[m,[f,"Fairphone"],[h,x]],[/(u304aa)/i],[m,[f,"AT&T"],[h,x]],[/\bsie-(\w*)/i],[m,[f,"Siemens"],[h,x]],[/\b(rct\w+) b/i],[m,[f,"RCA"],[h,y]],[/\b(venue[\d ]{2,7}) b/i],[m,[f,"Dell"],[h,y]],[/\b(q(?:mv|ta)\w+) b/i],[m,[f,"Verizon"],[h,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[m,[f,"Barnes & Noble"],[h,y]],[/\b(tm\d{3}\w+) b/i],[m,[f,"NuVision"],[h,y]],[/\b(k88) b/i],[m,[f,"ZTE"],[h,y]],[/\b(nx\d{3}j) b/i],[m,[f,"ZTE"],[h,x]],[/\b(gen\d{3}) b.+49h/i],[m,[f,"Swiss"],[h,x]],[/\b(zur\d{3}) b/i],[m,[f,"Swiss"],[h,y]],[/\b((zeki)?tb.*\b) b/i],[m,[f,"Zeki"],[h,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[f,"Dragon Touch"],m,[h,y]],[/\b(ns-?\w{0,9}) b/i],[m,[f,"Insignia"],[h,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[m,[f,"NextBook"],[h,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],m,[h,x]],[/\b(lvtel\-)?(v1[12]) b/i],[[f,"LvTel"],m,[h,x]],[/\b(ph-1) /i],[m,[f,"Essential"],[h,x]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[m,[f,"Envizen"],[h,y]],[/\b(trio[-\w\. ]+) b/i],[m,[f,"MachSpeed"],[h,y]],[/\btu_(1491) b/i],[m,[f,"Rotor"],[h,y]],[/(shield[\w ]+) b/i],[m,[f,"Nvidia"],[h,y]],[/(sprint) (\w+)/i],[f,m,[h,x]],[/(kin\.[onetw]{3})/i],[[m,/\./g," "],[f,R],[h,x]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[m,[f,F],[h,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[m,[f,F],[h,x]],[/smart-tv.+(samsung)/i],[f,[h,_]],[/hbbtv.+maple;(\d+)/i],[[m,/^/,"SmartTV"],[f,I],[h,_]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[f,M],[h,_]],[/(apple) ?tv/i],[f,[m,N+" TV"],[h,_]],[/crkey/i],[[m,E+"cast"],[f,P],[h,_]],[/droid.+aft(\w+)( bui|\))/i],[m,[f,z],[h,_]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[m,[f,G],[h,_]],[/(bravia[\w ]+)( bui|\))/i],[m,[f,L],[h,_]],[/(mitv-\w{5}) bui/i],[m,[f,H],[h,_]],[/Hbbtv.*(technisat) (.*);/i],[f,m,[h,_]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[f,ei],[m,ei],[h,_]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[h,_]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,m,[h,k]],[/droid.+; (shield) bui/i],[m,[f,"Nvidia"],[h,k]],[/(playstation [345portablevi]+)/i],[m,[f,L],[h,k]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[m,[f,R],[h,k]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[m,[f,I],[h,T]],[/((pebble))app/i],[f,m,[h,T]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[m,[f,N],[h,T]],[/droid.+; (glass) \d/i],[m,[f,P],[h,T]],[/droid.+; (wt63?0{2,3})\)/i],[m,[f,F],[h,T]],[/droid.+; (glass) \d/i],[m,[f,P],[h,T]],[/(pico) (4|neo3(?: link|pro)?)/i],[f,m,[h,T]],[/; (quest( \d| pro)?)/i],[m,[f,Z],[h,T]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[f,[h,q]],[/(aeobc)\b/i],[m,[f,z],[h,q]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[m,[h,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[m,[h,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[h,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[h,x]],[/(android[-\w\. ]{0,9});.+buil/i],[m,[f,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[v,[p,U+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[p,v],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[v,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,v],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[v,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,v],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[p,[v,ai,ni]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[v,ai,ni],[p,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[v,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,W],[v,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[v,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,v],[/\(bb(10);/i],[v,[p,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[v,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[v,[p,j+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[v,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[v,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[v,[p,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,$],v],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,v],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],v],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,v]]},si=function(i,e){if(typeof i===d&&(e=i,i=n),!(this instanceof si))return new si(i,e).getResult();var o=typeof r!==l&&r.navigator?r.navigator:n,a=i||(o&&o.userAgent?o.userAgent:s),t=o&&o.userAgentData?o.userAgentData:n,b=e?K(ti,e):ti,k=o&&o.userAgent==a;return this.getBrowser=function(){var i={};return i[p]=n,i[v]=n,oi.call(i,a,b.browser),i[u]=ii(i[v]),k&&o&&o.brave&&typeof o.brave.isBrave==w&&(i[p]="Brave"),i},this.getCPU=function(){var i={};return i[g]=n,oi.call(i,a,b.cpu),i},this.getDevice=function(){var i={};return i[f]=n,i[m]=n,i[h]=n,oi.call(i,a,b.device),k&&!i[h]&&t&&t.mobile&&(i[h]=x),k&&"Macintosh"==i[m]&&o&&typeof o.standalone!==l&&o.maxTouchPoints&&o.maxTouchPoints>2&&(i[m]="iPad",i[h]=y),i},this.getEngine=function(){var i={};return i[p]=n,i[v]=n,oi.call(i,a,b.engine),i},this.getOS=function(){var i={};return i[p]=n,i[v]=n,oi.call(i,a,b.os),k&&!i[p]&&t&&t.platform&&"Unknown"!=t.platform&&(i[p]=t.platform.replace(/chrome os/i,$).replace(/macos/i,W)),i},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return a},this.setUA=function(i){return a=typeof i===c&&i.length>S?ei(i,S):i,this},this.setUA(a),this};si.VERSION=t,si.BROWSER=Q([p,v,u]),si.CPU=Q([g]),si.DEVICE=Q([m,f,h,k,x,_,y,T,q]),si.ENGINE=si.OS=Q([p,v]),typeof e!==l?("object"!==l&&i.exports&&(e=i.exports=si),e.UAParser=si):"function"===w&&o.amdO?(a=function(){return si}.call(e,o,e,i),a===n||(i.exports=a)):typeof r!==l&&(r.UAParser=si);var bi=typeof r!==l&&(r.jQuery||r.Zepto);if(bi&&!bi.ua){var wi=new si;bi.ua=wi.getResult(),bi.ua.get=function(){return wi.getUA()},bi.ua.set=function(i){wi.setUA(i);var e=wi.getResult();for(var o in e)bi.ua[o]=e[o]}}})("object"===typeof window?window:this)}}]);