"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[9030],{11602:function(e,t,n){n.r(t),n.d(t,{default:function(){return w}});var o=n(85471),i=n(95353),r=(n(18111),n(7588),n(52112)),a=n(81721),s={namespaced:!0,state:{chosenChannel:{},chosenDiamond:{},chosenCoupon:{},isFirstPayUsed:!0,isInit:!1,chosenCouponOther:{},vip:{discountSubChannelId:[],diamondBonus:0,channelBonus:0,level:0,isInit:!1,isNewUser:!1},firstPayProducts:{},gotDailyReward:!1,extraCostInfo:{},koaTopupEnable:!1,switchToggleState:!1,showDoubleExperience:!1,defaultRebateDynamicInfo:{},defaultRebateDynamicInfoAll:{},defaultRebateInfo:{},defaultRebateInfoByProduct4:{},defaultDiscountInfo:{},defaultDiscountInfoByProduct4:{},isFixedEventOpen:!1,isFixedRebateWork:!1},getters:{takeEffectDefaultDiscount(e,t,n){return!!n.gameinfo.defaultDiscount&&(n.userinfo.isLogin&&!(!e.isFirstPayUsed||e.chosenCoupon.FE_INDEX)||!n.userinfo.isLogin)},takeEffectDefaultRebate(e,t,n){return!!e.defaultRebateInfo.FE_INDEX&&(n.userinfo.isLogin&&!(!e.isFirstPayUsed||e.chosenCoupon.FE_INDEX)||!n.userinfo.isLogin)},TWMyCard(e){const t=e.chosenDiamond;return t.product_id&&t.product_id.includes("com.kingsgroup.ss.tw.mc")},FinalPriceState(e,t,n){const o=e.chosenCoupon,i=e.chosenDiamond,s={rawOriginPrice:0,rawNowPrice:0,taxation:0,extra_fee_amount:0,offCountTips:"",offCountAmount:"",finalNowPrice:0,finalOriginPrice:0,rate:"",coin:0,level_coin:0,feType:"",isDefault:!1,isFixedCoupon:!1,isFixedRebate:!1,sdkType:""},c=o.feType||e.defaultRebateInfo.feType||e.defaultDiscountInfo.feType||e.defaultRebateDynamicInfo.feType;switch(c){case"first_pay":s.feType="first_pay",s.finalOriginPrice=o.price,s.finalNowPrice=o.discount_price,s.rawNowPrice=o.no_tax_price,s.rawOriginPrice=o.level_currency_price,s.offCountTips=`1st Pay ${o.rate} OFF`,s.sdkType=o.type;break;case"first_pay_rebate":s.feType="first_pay_rebate",s.finalNowPrice=o.price,s.rawNowPrice=i.no_tax_price,s.taxation=o.taxation,s.extra_fee_amount=o.extra_fee_amount,s.rate=o.rate,s.coin=o.coin,s.level_coin=o.level_coin,s.sdkType=o.type,s.offCountTips=`1st Pay ${a.A.t("bonus_tips")} ${o.rate} <i class="diamond-icon"></i>`;break;case"discount_coupon":s.feType="discount_coupon",s.finalOriginPrice=o.price,s.finalNowPrice=o.discount_price,s.rawNowPrice=o.no_tax_price,s.rawOriginPrice=o.level_currency_price,s.offCountTips=a.A.t("coupon_discount",{0:o.rateWidthOutPercent});break;case"cash_coupon":s.feType="cash_coupon",s.finalOriginPrice=i.price,s.finalNowPrice=o.price,s.rawNowPrice=o.no_tax_price,s.rawOriginPrice=o.level_currency_price,s.offCountTips=`${o.deduct_price} ${i.currency_symbol} OFF`;break;case"rebate_coupon":s.feType="rebate_coupon",s.finalNowPrice=o.price,s.rawNowPrice=o.no_tax_price,s.offCountTips=`${a.A.t("bonus_tips")} ${o.rate} <i class="diamond-icon"></i>`;break;case"fixed_discount_coupon":{const t=e.defaultDiscountInfo;s.feType="fixed_discount_coupon",s.isDefault=!0,s.finalNowPrice=t.discount_price,s.finalOriginPrice=t.price,s.rawNowPrice=t.no_tax_price,s.rawOriginPrice=t.level_currency_price,s.offCountTips=`${t.rateWidthOutPercent}% OFF`,s.offCountAmount=t.discount_amount,s.taxation=t.taxation,s.extra_fee_amount=t.extra_fee_amount,s.isFixedCoupon=!0,s.sdkType=t.type;break}case"fixed_rebate":{const t=e.defaultRebateInfo;s.feType="fixed_rebate",s.isDefault=!0,s.finalNowPrice=t.price,s.rawNowPrice=t.no_tax_price,s.taxation=t.taxation,s.extra_fee_amount=t.extra_fee_amount,s.offCountTips=`${a.A.t("bonus_tips")} ${t.rate} <i class="diamond-icon"></i>`,s.isFixedRebate=!0,s.sdkType=t.type;break}case"fixed_dynamic_rebate":{const t=e.defaultRebateDynamicInfo;s.feType="fixed_dynamic_rebate",s.isDefault=!0,s.finalNowPrice=t.price,s.rawNowPrice=t.no_tax_price,s.taxation=t.taxation,s.extra_fee_amount=t.extra_fee_amount,s.offCountTips=`${a.A.t("bonus_tips")} ${t.rate} <i class="diamond-icon"></i>`,s.isFixedRebate=!0;break}default:{s.rawNowPrice=i.no_tax_price,s.finalNowPrice=i.price;const t=e.chosenChannel,n=t.channel_id+(t.sub_channel_id?`_${t.sub_channel_id}`:""),{level_currency_price:o,currency:a,tax_rate:c,chosenNum:u=1}=e.chosenDiamond,d=e.extraCostInfo[n]||e.extraCostInfo[t.channel_id]||1;d&&o&&(s.extra_fee_amount=(0,r.YK)(u*o*(d-1),a),s.finalNowPrice=(0,r.YK)(u*o*(d-1+(c||1)),a),e.chosenDiamond.extra_fee_amount=s.extra_fee_amount)}}return d(s,e.chosenDiamond)},isDiamondOwn95Off:e=>t=>e.defaultDiscountInfo[t.product_id]||e.defaultDiscountInfoByProduct4[t.product_id]||!1,isDiamondOwnRebate:e=>t=>e.defaultRebateInfo[t.product_id]||e.defaultRebateInfoByProduct4[t.product_id]||e.defaultRebateDynamicInfoAll[t.product_id]||!1,getRebateCoin(e){return t=>{const n=e.defaultRebateInfo[t.product_id]||e.defaultRebateInfoByProduct4[t.product_id]||e.defaultRebateDynamicInfoAll[t.product_id];return n.coin-n.level_coin||0}},getSDKRebateCoin(e){return e.defaultRebateInfo.coin-e.defaultRebateInfo.level_coin||0}},mutations:{setChosenChannel(e,t){e.chosenChannel=t,u(e),n(58411).Z3(JSON.stringify({channel:t.channel_name,channel_id:t.channel_id,sub_channel_id:t.sub_channel_id}))},resetChannel(e){e.chosenChannel={},u(e)},setChosenDiamond(e,t){e.chosenDiamond=t,u(e),n(58411).sr(JSON.stringify(t))},setChosenCoupon(e,t){if(t){e.chosenCoupon=t,e.chosenCouponOther={};for(const n of Object.values(t.product_discount_range||[]))o["default"].set(e.chosenCouponOther,n.product_id,n);n(58411).ws(t)}},setFirstPayStatus(e,t){e.isFirstPayUsed=t},setIsInit(e,t){e.isInit=t},resetCouponInfo(e){e.chosenCoupon={},e.chosenCouponOther={},e.isFirstPayUsed=!0,e.isInit=!1,e.firstPayProducts={}},initVipInfo(e,t){e.vip.discountSubChannelId=["scheme","1380"],e.vip.diamondBonus=t.vip_bonus,e.vip.channelBonus=t.pay_bonus,e.vip.level=t.level,e.vip.isInit=t.is_white,e.vip.isNewUser=0===t.exp&&1===t.level,e.gotDailyReward=t.daily_reward_receive},setFirstPayProducts(e,t){t.length&&(t[0].product_discount_range||[]).forEach(t=>{e.firstPayProducts[t.product_id]={...t}})},setFixedCoupon(e,t){e.defaultDiscountInfo=t;for(const n of Object.values(t.product_discount_range||[]))o["default"].set(e.defaultDiscountInfo,n.product_id,n)},setFixedRebate(e,t){e.defaultRebateInfo=t;for(const n of Object.values(t.product_discount_range||[]))o["default"].set(e.defaultRebateInfo,n.product_id,n)},setFixedCouponByProduct4(e,t){for(const n of Object.values(t.product_discount_range||[]))o["default"].set(e.defaultDiscountInfoByProduct4,n.product_id,n)},setFixedRebateByProduct4(e,t){for(const n of Object.values(t.product_discount_range||[]))o["default"].set(e.defaultRebateInfoByProduct4,n.product_id,n)},setDailyRewardStatus(e,t){e.gotDailyReward=t},setExtraCostInfo(e,t=[]){t.forEach(t=>{const n=t.channel_id+(t.sub_channel_id?`_${t.sub_channel_id}`:"");e.extraCostInfo[n]=t.extra_fee_rate})},setKoaTopupEnable(e,t){e.koaTopupEnable=t},switchToggle(e,t){e.switchToggleState=t||!1},switchDoubleExperience(e,t){e.showDoubleExperience=t||!1},setFixedDynamicRebate(e,{chosen:t,all:n}={}){e.defaultRebateDynamicInfo=t;for(const i of n||[])o["default"].set(e.defaultRebateDynamicInfoAll,i.product_id,i)},toggleCoupon(e){e.isFixedRebateWork=!e.isFixedRebateWork,localStorage.setItem("fixedRebateWorkStatus",Number(e.isFixedRebateWork))},setFixedToggleEvent(e,t){e.isFixedEventOpen=t,t&&(e.isFixedRebateWork="1"===localStorage.getItem("fixedRebateWorkStatus"))}}};let c;function u(e){c&&(clearTimeout(c),c=void 0),c=setTimeout(()=>{const t=e.chosenChannel,n=t.channel_id+(t.sub_channel_id?`_${t.sub_channel_id}`:""),{level_currency_price:o,currency:i,tax_rate:r,chosenNum:a=1}=(e.extraCostInfo[n]||e.extraCostInfo[t.channel_id],e.chosenDiamond);clearTimeout(c),c=null},500)}function d(e,t){const n=t.currency_symbol,o=["rawOriginPrice","rawNowPrice","finalNowPrice","finalOriginPrice"];return o.forEach(t=>{e[t]&&(e[t]+=` ${n}`)}),e}var l={namespaced:!0,state:{gameProject:"",gameCode:"",gameName:"",appId:"",gameId:"",defaultDiscount:!0,whiteChannel:[],blackChannel:[],greyChannel:[],mainBody:"funplus",isSS:!1,isKOA:!1,isROMCP:!1,game:window.__GAMENAME,isCn:!1},mutations:{setGameInfo:e=>{const t=window.$gcbk("gameinfo",{});e.gameProject=t.gameProject,e.gameCode=t.gameCode,e.gameName=t.gameName,e.appId=t.appId,e.gameId=t.gameId,e.defaultDiscount=t.defaultDiscount,e.whiteChannel=t.whiteChannel,e.blackChannel=t.blackChannel,e.greyChannel=t.greyChannel,e.mainBody=t.mainBody,e.isCn=t.isCn,o["default"].set(e,`is${e.gameCode}`,!0)}},getters:{isPuzalaGame:e=>"puzala"===e.mainBody}},p=n(82851),m={namespaced:!0,state:{isLogin:!1,country:"",currency:"",fpid:"",icon:"",lang:"",level:0,name:"",openid:"",pkg_channel:"",server:0,lastChannel:{}},mutations:{setUserInfo(e,t){e.level=t.level,e.server=t.server,e.uid=t.uid,e.openid=t.openid,e.icon=t.icon,e.name=t.name,e.fpid=t.fpid,localStorage.setItem("openid",e.openid),e.isLogin=!0;const n={};t.uid&&(n.uid=t.uid),t.openid&&(n.openid=t.openid),window.__ReportExtraData__&&window.__ReportExtraData__(n),p.b.init(t.openid)},saveLastChannelInfo(e,t){e.lastChannel=t.last_channel},logout(){localStorage.removeItem("openid");const{origin:e,pathname:t}=location;window.location.href=`${e}${t}`}}},h=n(3003),f={namespaced:!0,state:{userinfo:{isLogin:!1,country:"",currency:"",fpid:"",icon:"",lang:"",level:0,name:"",openid:"",pkg_channel:"",server:0}},mutations:{setUserInfo(e,t){e.userinfo.level=t.level,e.userinfo.server=t.server,e.userinfo.uid=t.uid,e.userinfo.openid=t.openid,e.userinfo.icon=t.icon,e.userinfo.name=t.name,localStorage.setItem(h.yW,e.userinfo.openid),e.userinfo.isLogin=!0;const n={};t.uid&&(n.uid=t.uid),t.openid&&(n.openid=t.openid),window.__ReportExtraData__&&window.__ReportExtraData__(n)},logout(){localStorage.removeItem(h.yW);const{origin:e,pathname:t}=location;window.location.href=`${e}${t}/order`}}},_=(n(44114),{namespaced:!0,state:{currentKey:[],isInit:!1,emit:()=>{}},getters:{forbiddenAccess(e){return!!e.isInit&&e.currentKey._hasUnion(["always_banned","banned_uid"])},showAdyenTips(e){return!!e.isInit&&e.currentKey._hasUnion(["use_adyen"])},showTipsWhenSomeChannel(e,t){const n=[];return e.isInit?(t.showAdyenTips&&n.push("adyen"),e.currentKey.includes("use_wxpay")&&n.push("wxpay"),e.currentKey.includes("use_alipay")&&n.push("alipay"),e.currentKey.includes("use_paypal")&&n.push("paypal"),e.currentKey.includes("use_pingpong")&&n.push("pingpong"),n):n},hideAdyenChannel(e){return!!e.isInit&&e.currentKey._hasUnion(["banned_adyen","banned_adyen_card"])},hideSomeChannel(e,t){const n=[];return e.isInit?(t.hideAdyenChannel&&n.push("adyen"),e.currentKey.includes("banned_wxpay")&&n.push("wxpay"),e.currentKey.includes("banned_alipay")&&n.push("alipay"),e.currentKey.includes("banned_paypal")&&n.push("paypal"),e.currentKey.includes("banned_pingpong")&&n.push("pingpong"),n):n}},mutations:{init(e,t={}){const{list:n=[],emit:o}=t;e.isInit=!0,e.emit=o,n.forEach(t=>{const{banned_status:n,expire_time:i}=t;e.currentKey.push(n),["always_banned","banned_uid","access_warn","access_warn_black_room"].includes(n)&&o("showPop","RiskControlPolicy",{key:n,value:i})})}}}),y={namespaced:!0,state:{builtInCashier:!1,isDiscountUsed:!1},mutations:{setIsDiscountUsed(e,t){e.isDiscountUsed=t},savePrefixChannel(e,t){t&&t.length&&(e.builtInCashier="drop_in"===t[0].type)},resetBuiltInCashierStatus(e,t){if(!t||!t.length)return null;const n={};t.forEach(e=>{n[`${e.channel_id}_${e.channel_name}_${e.sub_channel_id}_${e.type}`]=e});const o=n.payermax_A34_A34_dropin;if(o)switch(o.status){case"close":e.builtInCashier=!1;break;case"open_all":e.builtInCashier=!0;break;default:}}}},g={namespaced:!0,state:{loginValidation:!1,showMobilePolicy:!1,showPopPolicy:!1,boon:!1,fixedDiscountType:"",smallDiamondDoubleDiscount:!1,ckoCheckedByDefault:!0,showPcDiscountTips:!1},mutations:{setFunctionInfo:e=>{const t=window.$gcbk("switch",{});e.loginValidation=t.loginValidation,e.showMobilePolicy=t.showMobilePolicy,e.showPopPolicy=t.showPopPolicy,e.boon=t.boon,e.fixedDiscountType=t.fixedDiscountType,e.ckoCheckedByDefault=t.ckoCheckedByDefault,e.smallDiamondDoubleDiscount=t.smallDiamondDoubleDiscount,e.showPcDiscountTips=t.showPcDiscountTips},updateFunctionInfo(e,t){"send_code_enable"in t&&(e.loginValidation=t.send_code_enable),"switches"in t&&"bind_card"in t["switches"]&&(e.ckoCheckedByDefault=t["switches"].bind_card)}}};o["default"].use(i.Ay);var w=new i.Ay.Store({state:{urlParams:(0,r.al)(),currencyUnit:"",country:"",city:"",state:"",currency:"",zipCode:"",isArZone:!1,isPc:window.innerWidth>940,isMobile:window.innerWidth<940,agreePrivacyPolicy:!0,isPCSDK:!!window.__isPCSDK,IS_CHECKOUT_SDK:!!window.__IS_CHECKOUT_SDK,IS_CHECKOUT_SDK_V2:!!window.__IS_CHECKOUT_SDK_V2},mutations:{setCurrencyUnitByIp(e,t){e.currencyUnit=t.currency_symbol,e.country=t.country,e.city=t.city,e.state=t.state,e.currency=t.currency,e.isArZone=(0,r.aY)(t.currency),e.zipCode=t.zipcode},resetIsXXX(e){e.isPc=window.innerWidth>940,e.isMobile=window.innerWidth<940},setPrivacyPolicyStatus(e,t){e.agreePrivacyPolicy=t},updateUrlParams(e,t){const n=JSON.parse(e.urlParams.tc||"{}");Object.assign(n,t),e.urlParams.tc=JSON.stringify(n)}},actions:{},modules:{formdata:s,gameinfo:l,userinfo:m,orderPage:f,riskPolicy:_,vb:y,functionSwitch:g}})},17845:function(e,t,n){n.d(t,{Jt:function(){return y},XF:function(){return p},bE:function(){return g},eu:function(){return h}});n(18111),n(61701);var o=n(16299),i=n(11602);n(81721),n(7588),n(18237);class r{constructor(){this.memoryCache=new Map,this.memoryTTL=new Map,this.maxMemorySize=50,this.defaultTTL=3e5}generateKey(e,t={}){const n=Object.keys(t).sort().reduce((e,n)=>(e[n]=t[n],e),{});return`${e}:${JSON.stringify(n)}`}setMemoryCache(e,t,n=this.defaultTTL){if(this.memoryCache.size>=this.maxMemorySize){const e=this.memoryCache.keys().next().value;this.memoryCache.delete(e),this.memoryTTL.delete(e)}this.memoryCache.set(e,t),this.memoryTTL.set(e,Date.now()+n)}getMemoryCache(e){const t=this.memoryTTL.get(e);return!t||Date.now()>t?(this.memoryCache.delete(e),this.memoryTTL.delete(e),null):this.memoryCache.get(e)}setLocalStorageCache(e,t,n=this.defaultTTL){try{const o={data:t,timestamp:Date.now(),ttl:n};localStorage.setItem(`api_cache_${e}`,JSON.stringify(o))}catch(o){console.warn("Failed to set localStorage cache:",o)}}getLocalStorageCache(e){try{const t=localStorage.getItem(`api_cache_${e}`);if(!t)return null;const n=JSON.parse(t),{data:o,timestamp:i,ttl:r}=n;return Date.now()-i>r?(localStorage.removeItem(`api_cache_${e}`),null):o}catch(t){return console.warn("Failed to get localStorage cache:",t),null}}setSessionCache(e,t){try{sessionStorage.setItem(`api_cache_${e}`,JSON.stringify({data:t,timestamp:Date.now()}))}catch(n){console.warn("Failed to set sessionStorage cache:",n)}}getSessionCache(e){try{const t=sessionStorage.getItem(`api_cache_${e}`);if(!t)return null;const{data:n}=JSON.parse(t);return n}catch(t){return console.warn("Failed to get sessionStorage cache:",t),null}}get(e,t={},n={}){const o=this.generateKey(e,t);let i=this.getMemoryCache(o);return i?Promise.resolve(i):n.useSessionCache&&(i=this.getSessionCache(o),i)?(this.setMemoryCache(o,i,n.ttl),Promise.resolve(i)):n.useLocalStorage&&(i=this.getLocalStorageCache(o),i)?(this.setMemoryCache(o,i,n.ttl),n.useSessionCache&&this.setSessionCache(o,i),Promise.resolve(i)):null}set(e,t={},n,o={}){const i=this.generateKey(e,t),r=o.ttl||this.defaultTTL;this.setMemoryCache(i,n,r),o.useSessionCache&&this.setSessionCache(i,n),o.useLocalStorage&&this.setLocalStorageCache(i,n,r)}delete(e,t={}){const n=this.generateKey(e,t);this.memoryCache.delete(n),this.memoryTTL.delete(n);try{localStorage.removeItem(`api_cache_${n}`),sessionStorage.removeItem(`api_cache_${n}`)}catch(o){console.warn("Failed to delete cache:",o)}}clear(){this.memoryCache.clear(),this.memoryTTL.clear();try{Object.keys(localStorage).forEach(e=>{e.startsWith("api_cache_")&&localStorage.removeItem(e)}),Object.keys(sessionStorage).forEach(e=>{e.startsWith("api_cache_")&&sessionStorage.removeItem(e)})}catch(e){console.warn("Failed to clear cache:",e)}}getStats(){return{memorySize:this.memoryCache.size,maxMemorySize:this.maxMemorySize,memoryKeys:Array.from(this.memoryCache.keys())}}async warmup(e=[]){const t=e.map(async({url:e,params:t,options:n,fetcher:o})=>{try{const i=this.get(e,t,n);if(!i&&o){const i=await o();return this.set(e,t,i,n),i}return i}catch(i){return console.warn(`Failed to warmup cache for ${e}:`,i),null}});return Promise.allSettled(t)}}const a=new r;const s={USER_INFO:{ttl:18e5,useLocalStorage:!0,useSessionCache:!0},PRODUCT_LIST:{ttl:6e5,useSessionCache:!0},PAYMENT_CHANNELS:{ttl:36e5,useLocalStorage:!0,useSessionCache:!0},COUPON_LIST:{ttl:3e5,useSessionCache:!0},CURRENCY_RATE:{ttl:864e5,useLocalStorage:!0,useSessionCache:!0}};var c=a,u=n(27796);class d{constructor(){this.queue=new Map,this.maxConcurrent=6,this.currentRequests=0}async add(e,t){if(this.queue.has(e))return this.queue.get(e);const n=this.executeRequest(t);this.queue.set(e,n);try{const e=await n;return e}finally{this.queue.delete(e)}}async executeRequest(e){while(this.currentRequests>=this.maxConcurrent)await new Promise(e=>setTimeout(e,10));this.currentRequests++;try{return await e()}finally{this.currentRequests--}}}const l=new d;function p(e){const t=location.pathname.includes("/order"),n=i["default"].state,o=t?n.orderPage.userinfo:n.userinfo,r=n.gameinfo;if(e||(e={}),e.game_id=+r.gameId,e.game_project=r.gameProject,e.source="web",n.country&&(e.country=n.country),n.currency&&(e.currency=n.currency),!e.openid&&o.openid&&(e.openid=o.openid),!e.uid&&o.uid&&(e.uid=o.uid),!t){const t=localStorage.getItem("openid")||"";!e.openid&&t&&(e.openid=t)}return e}function m(e){if(e.startsWith("http"))return e;const t=i["default"].state,n=t.gameinfo;return e.startsWith("/token/")?n.tokenHost+e:e.startsWith("/account/")?n.accountHost+e:e.startsWith("/api/")?n.apiHost+e:e.startsWith("/ame/")?n.ameHost+e:n.apiHost+e}const h=o.Ay.create({timeout:15e3,headers:{"Accept-Encoding":"gzip, deflate, br"}});async function f(e,t=3,n=1e3){let o;for(let r=0;r<=t;r++)try{return await e()}catch(i){if(o=i,i.response&&i.response.status>=400&&i.response.status<500)throw i;if(r===t)throw i;const e=n*Math.pow(2,r);await new Promise(t=>setTimeout(t,e))}throw o}async function _(e,t,n,o={}){const{cache:i=!1,cacheOptions:r={},retry:a=!1,maxRetries:s=3,priority:u="normal"}=o,d=`${e}:${t}:${JSON.stringify(n)}`;if(i){const e=c.get(t,n,r);if(e)return e}const p=async()=>{const o={method:e,url:t};"get"===e?o.params=n:o.data=n;const a=await h(o);return i&&a&&c.set(t,n,a,r),a},m=()=>l.add(d,p);return a?f(m,s):m()}h.interceptors.request.use(e=>{const t=Date.now();e.metadata={startTime:t},"post"===e.method?e.data=p(e.data):e.params=p(e.params),e.url=m(e.url);const n=sessionStorage.getItem("localCountry")||i["default"].state.urlParams.localCountry;if(n){const t=e.url.includes("?")?"&":"?";e.url+=`${t}country=${n}`}const o=sessionStorage.getItem("localAccountEnv")||i["default"].state.urlParams.localAccountEnv;if(o){const t=e.url.includes("?")?"&":"?";e.url+=t+`gameServerEnv=${o}`}if(e.url.includes("/account/store/user")){const{uid:t,openid:n}=e.data||e.params||{};n&&t&&e.data&&delete e.data.openid}return e},e=>(console.error("Request interceptor error:",e),Promise.reject(e))),h.interceptors.response.use(e=>{const{config:t}=e,n=Date.now(),o=n-t.metadata.startTime;return u.A.recordAPIMetric(t.url,o,e.status,{method:t.method,size:JSON.stringify(e.data).length}),e.data},e=>{const{config:t,response:n}=e;if(t&&t.metadata){const o=Date.now(),i=o-t.metadata.startTime;u.A.recordAPIMetric(t.url,i,n?.status||0,{method:t.method,error:e.message})}if(n)switch(n.status){case 401:i["default"].dispatch("user/logout");break;case 403:console.warn("Access forbidden");break;case 429:console.warn("Too many requests");break;case 500:case 502:case 503:case 504:console.error("Server error:",n.status);break;default:console.error("Request failed:",n.status)}else"ECONNABORTED"===e.code?console.error("Request timeout"):console.error("Network error:",e.message);return Promise.reject(e)});const y=async(e,t,n={})=>_("get",e,t,{cache:!0,cacheOptions:s.PRODUCT_LIST,retry:!0,...n}),g=async(e,t,n={})=>_("post",e,t,{retry:!1,...n})},27796:function(e,t,n){n(44114),n(18111),n(7588);class o{constructor(){this.metrics=new Map,this.observers=[],this.isEnabled=!0,this.isEnabled&&this.init()}init(){this.observeWebVitals(),this.observeResourceTiming(),this.observeLongTasks(),this.observeVisibilityChange(),this.setupBeforeUnload()}observeWebVitals(){if("PerformanceObserver"in window){const t=new PerformanceObserver(e=>{const t=e.getEntries(),n=t[t.length-1];this.recordMetric("LCP",n.startTime,{element:n.element?.tagName||"unknown",url:n.url||location.href})});try{t.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(t)}catch(e){console.warn("LCP observer not supported")}}if("PerformanceObserver"in window){const t=new PerformanceObserver(e=>{const t=e.getEntries();t.forEach(e=>{this.recordMetric("FID",e.processingStart-e.startTime,{eventType:e.name,target:e.target?.tagName||"unknown"})})});try{t.observe({entryTypes:["first-input"]}),this.observers.push(t)}catch(e){console.warn("FID observer not supported")}}if("PerformanceObserver"in window){let t=0;const n=new PerformanceObserver(e=>{const n=e.getEntries();n.forEach(e=>{e.hadRecentInput||(t+=e.value)}),this.recordMetric("CLS",t)});try{n.observe({entryTypes:["layout-shift"]}),this.observers.push(n)}catch(e){console.warn("CLS observer not supported")}}if("PerformanceObserver"in window){const t=new PerformanceObserver(e=>{const t=e.getEntries();t.forEach(e=>{"first-contentful-paint"===e.name&&this.recordMetric("FCP",e.startTime)})});try{t.observe({entryTypes:["paint"]}),this.observers.push(t)}catch(e){console.warn("FCP observer not supported")}}}observeResourceTiming(){if("PerformanceObserver"in window){const t=new PerformanceObserver(e=>{const t=e.getEntries();t.forEach(e=>{this.isKeyResource(e.name)&&this.recordMetric("resource_load_time",e.duration,{name:e.name,type:e.initiatorType,size:e.transferSize||0})})});try{t.observe({entryTypes:["resource"]}),this.observers.push(t)}catch(e){console.warn("Resource observer not supported")}}}observeLongTasks(){if("PerformanceObserver"in window){const t=new PerformanceObserver(e=>{const t=e.getEntries();t.forEach(e=>{this.recordMetric("long_task",e.duration,{startTime:e.startTime,attribution:e.attribution?.[0]?.name||"unknown"})})});try{t.observe({entryTypes:["longtask"]}),this.observers.push(t)}catch(e){console.warn("Long task observer not supported")}}}observeVisibilityChange(){let e=Date.now();document.addEventListener("visibilitychange",()=>{if(document.hidden){const t=Date.now()-e;this.recordMetric("page_visible_time",t)}else e=Date.now()})}isKeyResource(e){const t=[/\.js$/,/\.css$/,/chunk-.*\.js$/,/vendor.*\.js$/,/app.*\.js$/];return t.some(t=>t.test(e))}recordMetric(e,t,n={}){const o={name:e,value:t,timestamp:Date.now(),url:location.href,userAgent:navigator.userAgent,...n};this.metrics.set(`${e}_${Date.now()}`,o),this.isCriticalMetric(e)&&this.sendMetric(o)}isCriticalMetric(e){return["LCP","FID","CLS"].includes(e)}sendMetric(e){if("sendBeacon"in navigator){const t=JSON.stringify(e);navigator.sendBeacon("/api/metrics",t)}else fetch("/api/metrics",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"},keepalive:!0}).catch(e=>{console.warn("Failed to send metric:",e)})}sendAllMetrics(){const e=Array.from(this.metrics.values());if(0===e.length)return;const t=JSON.stringify({metrics:e,session:this.getSessionId(),page:location.href});"sendBeacon"in navigator&&navigator.sendBeacon("/api/metrics/batch",t)}getSessionId(){let e=sessionStorage.getItem("performance_session_id");return e||(e="session_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),sessionStorage.setItem("performance_session_id",e)),e}setupBeforeUnload(){window.addEventListener("beforeunload",()=>{this.sendAllMetrics()}),document.addEventListener("visibilitychange",()=>{document.hidden&&this.sendAllMetrics()})}recordPaymentMetric(e,t,n={}){this.recordMetric(`payment_${e}`,t,{...n,category:"payment"})}recordAPIMetric(e,t,n,o={}){this.recordMetric("api_call",t,{url:e,status:n,...o,category:"api"})}recordInteractionMetric(e,t,n={}){this.recordMetric(`interaction_${e}`,t,{...n,category:"interaction"})}destroy(){this.observers.forEach(e=>{e.disconnect()}),this.observers=[],this.metrics.clear()}}const i=new o;t.A=i},46838:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(90176);function i(){const e=document.createElement("a");e.href=o.A.ios()?window.$gcbk("gameinfo.appGameDeepLinkIos"):window.$gcbk("gameinfo.appGameDeepLinkAndroid"),e.style.display="none",document.body.appendChild(e),e.click(),document.body.removeChild(e)}},50361:function(e,t,n){n.d(t,{Z:function(){return s}});var o=n(21396),i=n.n(o);const r=i().enc.Utf8.parse("84uyzdgah9m#m9x4qzu&ye53"),a=r.clone();function s(e){return encodeURIComponent(i().AES.encrypt(i().enc.Utf8.parse(e),r,{iv:a,mode:i().mode.CBC,padding:i().pad.Pkcs7}).toString())}a.sigBytes=16,a.words.splice(4)},52112:function(e,t,n){n.d(t,{Ax:function(){return m},DU:function(){return p},D_:function(){return _},MH:function(){return g},S7:function(){return r},TH:function(){return c},VK:function(){return b},YK:function(){return h},Y_:function(){return u},aY:function(){return s},al:function(){return a},bn:function(){return v},jq:function(){return y},m8:function(){return w},oi:function(){return C},r9:function(){return P},uo:function(){return l},vV:function(){return f}});n(44114),n(18111),n(20116),n(61701);var o=n(21396),i=n.n(o);const r=e=>{e=e||window.location.search.slice(1);const t={},n=e.split("&");for(const o of n.values()){const e=o.indexOf("="),n=o.slice(0,e);t[n]=decodeURIComponent(o.slice(e+1))}return t},a=function(){let e={};return function(){if(location.href.includes("/order"))return{};if("{}"===JSON.stringify(e)){const t=r(),n=JSON.parse(localStorage.getItem("urlParams")||"{}"),o=JSON.parse(sessionStorage.getItem("urlParams")||"{}");"ruapp"===n.utm_campaign&&window.__GAMENAME&&window.__GAMENAME.startsWith("ss")&&(t.utm_campaign="ruapp"),"/"===location.pathname||window.__ROUTERPATH&&window.__ROUTERPATH.startsWith(location.pathname)?localStorage.setItem("urlParams",JSON.stringify(Object.assign({},n,t))):sessionStorage.setItem("urlParams",JSON.stringify(Object.assign({},o,t))),e=Object.assign({},n,o,t),"openid"in t&&localStorage.setItem("openid",t.openid),"boon"===t.event&&d()}return e}}(),s=e=>["VND"].includes(e),c=()=>{const e=window.matchMedia("(display-mode: standalone)").matches;return document.referrer.startsWith("android-app://")?"twa":navigator.standalone||e?"standalone":"browser"},u=c();var d=function(){const e=setInterval(()=>{"standalone"===c()&&(console.log("install successful!"),clearInterval(e),window.$event&&window.$event.$emit&&window.$event.$emit("installSuccessful"))},3e3)};const l=(()=>{const e=navigator.userAgent,t=e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),n=e.indexOf("Android")>-1||e.indexOf("Adr")>-1;return t?"ios":n?"android":"pc"})();function p(e){e=e||18;const t="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz123456789",n=t.length;let o="";for(let i=0;i<e;i++)o+=t.charAt(Math.floor(Math.random()*n));return o+(new Date).getTime()}const m=(e="",t="")=>{const n=(e||"").replace("_","-"),o=location.origin+location.pathname;window.location.href=`https://store-funplusid-test.funplus.com/login?refer=${encodeURIComponent(o)}&source_id=1&ng=token&lang=${n}&gid=${t}`},h=function(e,t){e=Number(e.toFixed(4));const n=["BDT","CLP","COP","CRC","DZD","HUF","IDR","INR","IQD","JPY","KES","KRW","KZT","LBP","LKR","MMK","NGN","PHP","PKR","PYG","RSD","RUB","THB","TWD","TZS","VND"];return n.includes(t)?Math.ceil(e):+e.toFixed(2)},f=function(){const e=Number.prototype.toFixed;Number.prototype.toFixed=function(t){const n=this;if(t>20||t<0)throw new RangeError("toFixed() digits argument must be between 0 and 20");if(Number.isNaN(n))throw new TypeError(n+".toFixed() is not a function");return void 0===t||0===t?Math.round(n).toString():e.call(Math.round((this+Number.EPSILON)*Math.pow(10,t))/Math.pow(10,t),t)}},_=function(e,t=[],n=!1){const o=new Date(Date.now()+(n?0:60*(new Date).getTimezoneOffset()*1e3)),i=`${o.getFullYear()}/${o.getMonth()+1}/${o.getDate()}`,r=JSON.parse(localStorage.getItem(e)||"[]"),a=!r.includes(i)&&(!t.length||t.includes(i));return a&&(r.push(i),localStorage.setItem(e,JSON.stringify(r))),a},y=e=>{const t=2,n=[{value:1,symbol:""},{value:1e3,symbol:"K"},{value:1e6,symbol:"M"},{value:1e9,symbol:"G"},{value:1e12,symbol:"T"},{value:1e15,symbol:"P"},{value:1e18,symbol:"E"}],o=/\.0+$|(\.[0-9]*[1-9])0+$/;var i=n.slice().reverse().find(function(t){return e>=t.value});return i?(e/i.value).toFixed(t).replace(o,"$1")+i.symbol:"0"};function g(e){let t="",n=!1;window.SMSdk&&window.SMSdk.ready?window.SMSdk.ready(function(){t=window.SMSdk.getDeviceId?window.SMSdk.getDeviceId():t,n||(n=!0,e&&e(t))}):(console.error("数美 sdk is not ready"),e&&e(t))}function w(){return{windowSize:"05",acceptHeader:"text/html, application/xhtml+xml, application/xml;q=0.9, image/webp, image/apng, *;q=0.8",colorDepth:screen.colorDepth,screenHeight:document.documentElement.clientHeight||document.body.clientHeight,jetLag:(new Date).getTimezoneOffset(),userAgent:navigator.userAgent,screenWidth:document.documentElement.clientWidth||document.body.clientWidth,javaEnabled:!0,javaScriptEnabled:!0}}function b(e,t){const n=t;function o(e){return e.split(" ").map(e=>String.fromCharCode(e-1)).join("")}var r=i().AES.decrypt(e,i().enc.Utf8.parse(o(n)),{iv:i().enc.Utf8.parse(o(n).substr(0,16)),mode:i().mode.CBC,padding:i().pad.Pkcs7}),a=r.toString(i().enc.Utf8);return a}function P(e){const t="110 106 111 106 38 37 112 49 112 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83";function n(e){return e.split(" ").map(e=>String.fromCharCode(e-1)).join("")}const o=i().AES.encrypt(e,i().enc.Utf8.parse(n(t)),{iv:i().enc.Utf8.parse(n(t).substr(0,16)),mode:i().mode.CBC,padding:i().pad.Pkcs7});return o.toString()}const C=(()=>{var e=navigator.userAgent.toLowerCase(),t=-1!==e.indexOf("micromessenger");return t})();async function v(e=""){const t=(await Promise.resolve().then(n.bind(n,11602))).default;if(t.state.userinfo.openid){const n=e.indexOf("?")>=0?"&":"?";e+=`${n}openid=${encodeURIComponent(t.state.userinfo.openid)}`}window.open(e,"_blank")}},58411:function(e,t,n){n.d(t,{Z3:function(){return _},a5:function(){return b},dO:function(){return h},hl:function(){return w},mK:function(){return f},p5:function(){return m},sr:function(){return y},ws:function(){return g}});var o=n(55373),i=n.n(o),r=n(17845),a=n(11602),s=n(52112);const c=a["default"].state.gameinfo,u=a["default"].state.userinfo,d=a["default"].state.urlParams;let l=localStorage.getItem("browserMark")||"";function p(e){const t={app_id:`${a["default"].state.gameinfo.gameProject.split("_")[0]}.global.prod`,data_version:"3.0",event:"third_pay",event_ts:1670812301475,fpid:u.fpid,ts_pretty:"2022/12/12 02:31:41"};t.detail={gamecode:window.$gcbk("gameinfo.gameLogCode","")||c.gameCode,gameid:+c.gameId,pay_channel:"web",tracking_id:d.utm_campaign||"",browser_id:l,opened_by:"standalone"===s.Y_?`pwa_${s.uo}`:"",...e,action:e.event},(window.__isPCSDK||window.__IS_CHECKOUT_SDK)&&(t.detail.pay_channel=d.s),"DC"===t.detail.gamecode&&(t.detail.gamecode="dc",t.app_id="dc.global.prod"),"SSV2"===t.detail.gamecode&&(t.app_id="ss.global.prod");const{uid:n,openid:o}=u;n&&!e.uid&&(t.detail.uid=n),o&&(t.detail.openid=o),Object.assign(t,(0,r.XF)()),t.openid&&!t.detail.openid&&(t.detail.openid=t.openid);const p=window.$gcbk("apiParams.biPath","/bilog"),m=`${{NODE_ENV:"production",VUE_APP_PROD_ENV:"RELEASE",VUE_APP_PREFIX_TOKEN_KOA:"https://koa-store-release.kingsgroup.cn/api",VUE_APP_PREFIX_API_KOA:"http://**************:8990/api",VUE_APP_PREFIX_TOKEN_KOACN:"http://**************:12001/api",VUE_APP_OlD_STORE_URL_KOA:"http://**************:10085",VUE_APP_CN_ADDRESS_KOA:"http://**************:10301",VUE_APP_ROM_ADDRESS_KOA:"http://**************:10305",VUE_APP_PREFIX_AME_KOA:"https://ame-test.funplus.com",VUE_APP_PREFIX_AME:"https://ame-test.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account-stage.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid-test.funplus.com/login",VUE_APP_VipIntroducePageUrl_koa:"http://vip-test.funplus.com.cn/koa",VUE_APP_VipIntroducePageUrl_aof:"http://vip-test.funplus.com.cn/vip",VUE_APP_VipIntroducePageUrl_rom:"http://vip-test.funplus.com.cn/rom",VUE_APP_VipIntroducePageUrl_koaCn:"http://vip-test.funplus.com.cn/koa-cn",BASE_URL:"/res/"}["VUE_APP_PREFIX_TOKEN_"+c.gameCode]}${p}`;if(navigator.sendBeacon)navigator.sendBeacon(m,JSON.stringify(t));else{const e=new Image;e.src=`${m}?${i().stringify(t,{allowDots:!0})}`}}function m(){const e={event:"load_done"};p(e)}function h(e){const t={event:"click_login",uid:e};p(t)}function f(){const e={event:"login_successful",level:u.level,gameserver_id:u.server,name:u.name};p(e)}function _(e){const t={event:"click_method",method:e};p(t)}function y(e){const t={event:"click_product",method:e};p(t)}function g(e){const t={event:"select_coupon",coupon_id:e.coupon_id,coupon_description:e.type};p(t)}function w(e,t,n,o){const i={status:e,order_id:t,reason:n,...o};p(i)}function b(e={}){const t={event:"open_fpstore_checkout",...e};p(t)}l||(l=(0,s.DU)(),localStorage.setItem("browserMark",l))},63118:function(e,t,n){var o=n(85471),i=n(81721),r=n(79787);window.$gcbk=r.A,o["default"].prototype.$gcbk=r.A,o["default"].prototype.$vt=e=>i.A.t((0,r.A)(`langKey.${e}`)),o["default"].prototype.$imageLoader=(e,t)=>(0,r.A)(`images.${e}`,t),o["default"].prototype.$idLoader=window.$idLoader=(e,t)=>(0,r.A)(`ids.${e}`,t),o["default"].prototype.$gameName=window.__GAMENAME;let a=null;try{a=n(Object(function(){var e=new Error("Cannot find module '@ufe/reporter'");throw e.code="MODULE_NOT_FOUND",e}()))}catch(C){console.warn("webReporter: @ufe/reporter not found, reporting disabled")}a&&new a({domain:"https://web-monitor.funplus.com",appId:window.$idLoader&&window.$idLoader("appId"),autoErr:!0,vueError:o["default"],useBeaconFirst:!0,extra:{appVersion:"0.0.2"}});var s=n(11602);function c(e,t){const n=37.7,o=e.innerWidth;if(o<576){const e=o/750;t.documentElement.style.fontSize=n*e+"px"}else t.documentElement.style.fontSize=o>940?"37.5px":"25px";s["default"].commit("resetIsXXX")}let u;c(window,document),window.addEventListener("resize",()=>{const e=()=>c(window,document);u&&clearTimeout(u),u=setTimeout(e,100)});var d=n(52112);Array.prototype._hasUnion=function(e=[]){if(!Array.isArray(e))throw Error("params `arr` must be Array!");return[...this].filter(t=>e.includes(t)).length},(0,d.al)();n(27796);var l=n(15804),p=n(40173);o["default"].use(p.Ay);const m=[{path:"/",name:"Pay",component:()=>Promise.all([n.e(1307),n.e(7996),n.e(1493),n.e(6964),n.e(8406),n.e(471),n.e(9936),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(3568)]).then(n.bind(n,78336))},{path:"/pay",name:"Pay",component:()=>Promise.all([n.e(1307),n.e(7996),n.e(1493),n.e(6964),n.e(8406),n.e(471),n.e(9936),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(3568)]).then(n.bind(n,78336))},{path:"/ad",name:"Adyen",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(513),n.e(5642),n.e(9030),n.e(9490),n.e(1054),n.e(9438)]).then(n.bind(n,25504))},{path:"/aw",name:"Airwallex",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,4898))},{path:"/pp",name:"pingpong",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,44988))},{path:"/cko",name:"checkout",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,443))},{path:"/pm",name:"payermax",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,38101))},{path:"/sp",name:"stripe",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,10638))},{path:"/fail",name:"CallbackFail",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,83450))},{path:"/completed",name:"CallbackCompleted",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,83450))},{path:"/pending",name:"CallbackPending",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,83450))},{path:"/common/fail",name:"CallbackFail",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,1571))},{path:"/common/completed",name:"CallbackCompleted",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,1571))},{path:"/common/pending",name:"CallbackPending",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,1571))},{path:"/order",name:"OrderPage",component:()=>Promise.all([n.e(3888),n.e(2035),n.e(334),n.e(5588),n.e(404),n.e(7894),n.e(3243),n.e(1307),n.e(3975),n.e(9614),n.e(3331),n.e(782),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(5455)]).then(n.bind(n,76030))},{path:"*",redirect:"/"}];s["default"].commit("gameinfo/setGameInfo"),s["default"].commit("functionSwitch/setFunctionInfo"),s["default"].state.IS_CHECKOUT_SDK_V2&&m.splice(0,2,{path:"/",name:"Pay",component:()=>Promise.all([n.e(1307),n.e(6964),n.e(8406),n.e(471),n.e(9936),n.e(8547),n.e(2707),n.e(513),n.e(5642),n.e(9030),n.e(6442)]).then(n.bind(n,64925))});const h=new p.Ay({mode:"history",base:window.__ROUTERPATH||"/",routes:m});var f=h,_=n(17318),y=n(37009),g=n(43705),w=n(56488),b=n(97073),P=n(90176);window.isMobile=P.A.mobile(),(0,d.vV)(),o["default"].config.productionTip=!1,o["default"].use(b.Ay,{config:{id:window.$idLoader("gid")}}),o["default"].use(_.Ay),o["default"].use(y.A),o["default"].use(g.A),o["default"].use(w.A),console.log("test",202507151040),new o["default"]({router:f,store:s["default"],i18n:i.A,render:e=>e(l.A)}).$mount("#app")},81721:function(e,t,n){n.d(t,{k:function(){return l}});var o=n(85471),i=n(64765),r=n(95134),a=n(11602);o["default"].use(i.A);const s=a["default"].state.urlParams.l||navigator.language||navigator.userLanguage;let c;c="zh-tw"===s.toLowerCase()||"zh_tw"===s.toLowerCase()?"zh_tw":s.startsWith("zh")?"zh_cn":s.split("-")[0];const u=["zh_cn","en","zh_tw","ar","de","es","fr","id","it","ja","ko","nl","pl","pt","ru","sv","th","tr","vi","my"];u.includes(c)||(c="en");const d=new i.A({locale:c,fallbackLocale:"en",messages:r.A.config});t.A=d;const l={en:"English",ko:"한국어",ja:"日本語",fr:"Français",de:"Deutsch",ru:"Русский"}},82851:function(e,t,n){n.d(t,{b:function(){return o}});const o=function(){let e;const t={},n=t=>e?`${e}_${t}`:t;return t.init=function(t){e=t},t.setLocalStorage=function(e,t){console.log(n(e),t),localStorage.setItem(n(e),t)},t.setSessionStorage=function(e,t){sessionStorage.setItem(n(e),t)},t.getLocalStorage=function(e){return localStorage.getItem(n(e))},t.getSessionStorage=function(e){return sessionStorage.getItem(n(e))},t}()},87007:function(e,t,n){n(44114),n(14603),n(47566),n(98721);var o=n(52112),i=n(77232),r=n.n(i),a=n(87367),s=n(58411),c=n(95353);t.A={methods:{customGetSmDeviceId(e){if("function"!==typeof e)return console.error("customGetSmDeviceId cb 必须为函数！");new Promise((e,t)=>{this.$loading.show();const n=setTimeout(()=>e(""),2500);(0,o.MH)(function(t){n&&clearTimeout(n),e(t)})}).then(t=>{t||console.error("dealSmDeviceId 失败！"),e(t)}).finally(()=>this.$loading.hide())},purchaseGoodsWithDeviceId(e,t){const{channel_id:n,channel_name:i,sub_channel_id:c}=this.chosenChannel,u=this.chosenDiamond,d={name:this.$vt("tokenName"),product_id:u.product_id,price:+(u.nowPriceWithTaxAndExtra||u.price),channel:i,channel_id:n,sub_channel_id:c,return_url:location.origin,fp_device_id:t};if("/"!==this.$router.options.base&&(d.return_url+=`/${this.$router.options.base.replace(/\//g,"")}`,this.IS_CHECKOUT_SDK)){let e=location.origin+window.__ROUTERPATH;e.endsWith("/")&&(e=e.slice(0,-1)),d.return_url=e}"aof"!==this.$gameName&&"rom"!==this.$gameName||(d.ext_info=JSON.stringify({project_code:"koa_aof_web"}));const{type:l,chosenNum:p}=this.chosenDiamond;2===l&&(d.custom_multiple=p),e&&(d.backup=1);let m=this.$store.state.urlParams.utm_campaign;if(m){if(m.includes("?")){const e=m.indexOf("?");m=m.slice(0,e)}d.tracking_id=m}"standalone"===o.Y_&&(d.tracking_id=`pwa_${o.uo}`),d.browser_id=localStorage.getItem("browserMark")||"";const h=new(r());d.browser_info={terminalType:window.innerWidth>1200?"pc":"mobile",osType:h.getOS().name,...(0,o.m8)()};const f=this.$store.getters["formdata/FinalPriceState"],_=this.chosenCoupon;switch(f.feType){case"first_pay":case"direct_first_pay":d.act_type="first_pay",d.discount=_.discount,d.discount_price=_.discount_price;break;case"first_pay_rebate":case"direct_first_pay_rebate":d.act_type="first_pay_rebate",d.discount=_.discount,d.discount_price=_.discount_price,_.act_type&&(d.act_type=_.act_type);break;case"discount_coupon":d.act_type="coupon",d.discount=_.discount,d.discount_price=_.discount_price,d.coupon_id=_.coupon_id;break;case"cash_coupon":d.act_type="deduct",d.discount=_.deduct_price,d.discount_price=_.price,d.coupon_id=_.coupon_id;break;case"rebate_coupon":d.act_type="rebate",d.discount=_.discount,d.discount_price=_.discount_price,d.coupon_id=_.coupon_id;break;case"fixed_discount_coupon":case"direct_fixed_discount":{const e=this.$store.state.formdata.defaultDiscountInfo;d.act_type="fixed_discount",d.discount=e.discount,d.discount_price=e.discount_price;break}case"fixed_rebate":case"direct_fixed_rebate":d.act_type="fixed_rebate";break;case"fixed_dynamic_rebate":d.act_type="product_fixed_rebate";break;default:{const e=this.$store.state.functionSwitch.fixedDiscountType;e&&!d.act_type&&(d.act_type=e);const t=this.$store.state.functionSwitch.smallDiamondDoubleDiscount&&u.product_id===this.$gcbk("ids.minimumDiamondId"),n=t||this.$store.getters["formdata/TWMyCard"];n&&delete d.act_type}}const y=this.chosenChannel,g=`${y.channel_id}_${y.channel_name}_${y.sub_channel_id}_dropin`;if(this.$store.state.vb.builtInCashier&&"payermax_A34_A34_dropin"===g&&(d.order_type="drop_in"),sessionStorage.removeItem("goodsName"),this.IS_CHECKOUT_SDK){const e=this.$store.state.urlParams,t=JSON.parse(e.tc||"{}");f.sdkType&&(d.act_type=f.sdkType),d.package_id=t.package_id,d.name=d.package_name=t.product_name,d.game_order_id=e.oid,sessionStorage.setItem("goodsName",t.product_name)}if(this.IS_CHECKOUT_SDK_V2){const e=window._calState();e.type&&(d.act_type=e.type),d.name||(d.name=window.defaultPackageName,sessionStorage.setItem("goodsName",window.defaultPackageName))}const w=this.$store.state,b={method:`${i}|${n}|${c}`,amount:"",country:w.country,currency:w.currency,product_info:d.product_id,event:"pay_completed",revenue:d.price};this.$loading.show(),this.requestLoading=!0,new Promise(e=>{if(this.$store.getters["formdata/TWMyCard"])return e((0,a.mt)(d));e((0,a.Rm)(d))}).then(e=>{const{data:t,code:n,message:o}=e;switch(n){case 0:new Promise((e,n)=>{"coin_debt"in t?(this.$root.$emit("showPop","ArrearsReminder",{debt:t.coin_debt||0}),this.$root.$once("arrearsReminderResult",t=>{t?e(1):n(Error("cancel pop!"))})):e(2)}).then(e=>{if((0,s.hl)("success",t.order_id,"-",b),"string"===typeof t.pay_url){const n=new URL(t.pay_url),o=new URLSearchParams(n.search);if(sessionStorage.setItem("3zRtY8vXwN",JSON.stringify(t)),sessionStorage.removeItem("7x9FkL2pQm"),t.pay_url.includes("pingpong")&&"jump_url"!==o.get("window_type")){const n={ppToken:o.get("token"),coinNums:t.coin_recv,currency:t.currency,currency_symbol:t.currency_symbol,amount:t.price};1===e&&(n.inDebt=!0),sessionStorage.setItem("ppParams",JSON.stringify(n)),this.$router.push("/pp")}else{if("jump_url"===o.get("window_type"))return location.href=t.pay_url.replace("&window_type=jump_url","").replace("?window_type=jump_url",""),null;if(t.open_with_new_window)return window.open(t.pay_url,"_blank");location.href=t.pay_url}}else t.pay_url.coinNums=t.coin_recv,t.pay_url.currency_symbol=t.currency_symbol,t.pay_url.host=t.payment_host,t.pay_url.order_id=t.payment_order_id,t.pay_url.out_trade_no=t.order_id,sessionStorage.setItem("id_sign",t.payment_order_id_sign),sessionStorage.setItem("url",t.payment_host),1===e&&(t.pay_url.inDebt=!0),"payermax"===t.pay_url.channel&&(t.pay_url.payment_order_id=t.payment_order_id,t.pay_url.name=t.name),sessionStorage.setItem("params",JSON.stringify(t.pay_url)),"payermax"===t.pay_url.channel?this.$router.push("/pm"):t.pay_url.client_secret?this.$router.push("/aw"):t.pay_url.store_card_url?this.$router.push("/cko"):t.pay_url.stripe_client_secret?this.$router.push("/sp"):this.$router.push("/ad")}).catch(e=>{console.log(e.message)});break;case 1003:{const e={1:this.$t("illegalGift"),2:this.$t("expiredPackage"),3:this.$t("InventoryShortage")};this.$toast.err(e[t.check_status]),(0,s.hl)("failed","-",e[t.check_status],b);break}default:throw this.$toast.err(this.$t("shop_fail")),Error(o)}}).catch(e=>{this.requestLoading=!1,(0,s.hl)("failed","-",e,b),console.error(`coinPlaceOrder下单失败：${e.message}`)}).finally(()=>{this.requestLoading=!1,this.$loading.hide()})},async purchaseGoods(e){if(this.requestLoading||this.$store.getters["riskPolicy/forbiddenAccess"])return null;if(!this.isLogin)return this.$root.$emit("ClickPayButNotLogin");if(!this.$store.state.agreePrivacyPolicy)return this.$root.$emit("showPop","PrivacyPolicy");if(window.__needDEPop){const e=await new Promise((e,t)=>{const n={ok:()=>e(1),no:()=>e(0)};this.$root.$emit("showPop","privateConfirmPop",n)});if(!e)return null}if("{}"===JSON.stringify(this.chosenChannel))return this.$toast.err(this.$t("ModalTIpsShopBeforeChooseChannel"));const t=this;this.customGetSmDeviceId(function(n){t.purchaseGoodsWithDeviceId(e,n)})},judgeRisk(){const{channel_id:e}=this.chosenChannel,t=this.$store.getters["riskPolicy/showTipsWhenSomeChannel"].indexOf(e);if(-1!==t){const e=`use_${this.$store.getters["riskPolicy/showTipsWhenSomeChannel"][t]}`;return this.$root.$emit("showPop","RiskControlPolicy",{key:e,cb:this.purchaseGoods})}this.purchaseGoods()}},computed:{...(0,c.aH)("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip"]),...(0,c.aH)(["isPc","isMobile","IS_CHECKOUT_SDK","IS_CHECKOUT_SDK_V2"]),...(0,c.aH)("userinfo",["isLogin"]),...(0,c.aH)("gameinfo",["gameCode","isKOA","isSS"]),...(0,c.aH)("functionSwitch",["showMobilePolicy","boon"])},data(){return{requestLoading:!1}},created(){this.$root.$on("adyenInitError",()=>this.purchaseGoods(1)),location.search&&history.pushState({},"",location.pathname)}}},87367:function(e,t,n){n.d(t,{AO:function(){return d},B9:function(){return _},D8:function(){return s},Gj:function(){return a},MN:function(){return r},OL:function(){return l},Pp:function(){return i},QC:function(){return u},Rm:function(){return v},Tu:function(){return C},UA:function(){return h},V1:function(){return p},aU:function(){return k},i9:function(){return I},iM:function(){return b},jn:function(){return y},mt:function(){return S},q7:function(){return f},qr:function(){return P},uy:function(){return c},w2:function(){return m}});var o=n(17845);const i=e=>(0,o.Jt)("/ame/do",e),r=e=>(0,o.Jt)("/token/getIpCurrency",e),a=e=>(0,o.bE)("/account/fpid2openid",e),s=e=>(0,o.bE)("/account/uid_list",e),c=e=>(0,o.bE)("/account/store/u",e),u=e=>(0,o.bE)("/token/act/init",e),d=e=>(0,o.Jt)("/ame/do",e),l=e=>(0,o.Jt)("/ame/hold",e),p=e=>(0,o.Jt)("/ameCommon/do",e),m=e=>(0,o.bE)("/token/common/info",e),h=e=>(0,o.bE)("/account/store/send_code",e),f=e=>(0,o.bE)("/account/store/check_code",e),_=e=>(0,o.bE)("/token/act/coupon/change/only_type",e),y=e=>(0,o.Jt)("/ame/do",{p0:"web",p1:20,p2:1113,p3:"api",wx_code:e}),g={productList:"/token/products",channelList:"/token/channels",placeOrder:"/token/place_order",cardPlaceOrder:"token/point_card_place_order",orderDetail:"/token/order",redirectProduct:"/token/pay_sdk/products",lastChosenChannel:"/token/user_pay_info"},w=Object.create(g);switch(window.__GAMENAME){case"koaCn":g.productList="/token/cn/products",g.channelList="/token/cn/channels";break;case"ssv":case"ssv2":g.productList="/token/store/coin_products",g.channelList="/token/store/channels",g.placeOrder="/token/store/coin_place_order",g.cardPlaceOrder="/token/store/point_card_place_order",g.orderDetail="/token/sdk/order",g.lastChosenChannel="/token/sdk/user_pay_info";break;case"foundation":case"ssRP":case"ssCP":case"stCP":case"mcCP":case"gogCP":case"romCP":case"stRP":case"ssdRP":case"moRP":case"koaRP":g.redirectProduct="/token/product"}const b=e=>(0,o.bE)(w.productList,e),P=e=>(0,o.bE)(w.redirectProduct,e),C=e=>(0,o.bE)(w.channelList,e),v=e=>(0,o.bE)(w.placeOrder,e),S=e=>(0,o.bE)(w.cardPlaceOrder,e),I=e=>(0,o.bE)(w.orderDetail,e),k=e=>(0,o.Jt)(w.lastChosenChannel,e)}}]);