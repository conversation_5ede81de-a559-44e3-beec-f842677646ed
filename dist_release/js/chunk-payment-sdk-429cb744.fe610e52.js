"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[334],{8746:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Fizetés","payButton.redirecting":"Átirányítás...","payButton.with":"%{value} fizetése a következővel: %{maskedData}","payButton.saveDetails":"Részletek mentése",close:"Bezárás",storeDetails:"Mentés a következő fizetéshez",readMore:"Bővebben","creditCard.holderName":"A kártyán szereplő név","creditCard.holderName.placeholder":"Gipsz Jakab","creditCard.holderName.invalid":"Adja meg a kárty<PERSON> szereplő nevet","creditCard.numberField.title":"Kártyaszám","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Lejárati dátum","creditCard.expiryDateField.placeholder":"HH/ÉÉ","creditCard.expiryDateField.month":"Hónap","creditCard.expiryDateField.month.placeholder":"HH","creditCard.expiryDateField.year.placeholder":"ÉÉ","creditCard.expiryDateField.year":"Év","creditCard.cvcField.title":"Biztonsági kód","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Megjegyzés a következő alkalomra","creditCard.cvcField.placeholder.4digits":"4 számjegyű","creditCard.cvcField.placeholder.3digits":"3 számjegyű","creditCard.taxNumber.placeholder":"ÉÉHHNN / 0123456789",installments:"Részletek száma",installmentOption:"%{times} x %{partialValue}",installmentOptionMonths:"%{times} hónap","installments.oneTime":"Egyösszegű fizetés","installments.installments":"Részletfizetés","installments.revolving":"Többösszegű fizetés","sepaDirectDebit.ibanField.invalid":"Érvénytelen számlaszám","sepaDirectDebit.nameField.placeholder":"Gipsz Jakab","sepa.ownerName":"Számlatulajdonos neve","sepa.ibanNumber":"Számlaszám (IBAN)","error.title":"Hiba","error.subtitle.redirect":"Sikertelen átirányítás","error.subtitle.payment":"Sikertelen fizetés","error.subtitle.refused":"A fizetés elutasítva","error.message.unknown":"Ismeretlen hiba történt","errorPanel.title":"Meglévő hibák","idealIssuer.selectField.title":"Bank","idealIssuer.selectField.placeholder":"Bank kiválasztása","creditCard.success":"Sikeres fizetés",loading:"Betöltés…",continue:"Folytatás",continueTo:"Folytatás a következővel:","wechatpay.timetopay":"Fizetéshez rendelkezésre álló idő: %@","sr.wechatpay.timetopay":"Fizetésre rendelkezésre álló idő: %#perc%# %#másodperc%#","wechatpay.scanqrcode":"QR-kód beolvasása",personalDetails:"Személyes adatok",companyDetails:"Cég adatai","companyDetails.name":"Cégnév","companyDetails.registrationNumber":"Cégjegyzékszám",socialSecurityNumber:"Személyi igazolvány száma",firstName:"Keresztnév","firstName.invalid":"Adja meg a keresztnevét",infix:"Előtag",lastName:"Vezetéknév","lastName.invalid":"Adja meg a vezetéknevét",mobileNumber:"Mobiltelefonszám","mobileNumber.invalid":"Érvénytelen mobilszám",city:"Város",postalCode:"Irányítószám","postalCode.optional":"Irányítószám (nem kötelező)",countryCode:"Országkód",telephoneNumber:"Telefonszám",dateOfBirth:"Születési dátum",shopperEmail:"E-mail-cím",gender:"Nem","gender.notselected":"Válassza ki a nemét",male:"Férfi",female:"Nő",billingAddress:"Számlázási cím",street:"Utca",stateOrProvince:"Állam vagy tartomány",country:"Ország/régió",houseNumberOrName:"Házszám",separateDeliveryAddress:"Eltérő szállítási cím megadása",deliveryAddress:"Szállítási cím","deliveryAddress.firstName":"Címzett keresztneve","deliveryAddress.lastName":"Címzett vezetékneve",zipCode:"Irányítószám",apartmentSuite:"Lakás/ajtószám",provinceOrTerritory:"Tartomány vagy terület",cityTown:"Város",address:"Cím","address.placeholder":"Keresse meg a címét","address.errors.incomplete":"A folytatáshoz írjon be egy címet","address.enterManually":"Manuálisan írjon be egy címet",state:"Állam","field.title.optional":"(nem kötelező)","creditCard.cvcField.title.optional":"Biztonsági kód (nem kötelező)","issuerList.wallet.placeholder":"Pénztárca kiválasztása",privacyPolicy:"Adatvédelmi szabályzat","afterPay.agreement":"Elfogadom a következőt: Riverty %@","riverty.termsAndConditions":"Elfogadom a Riverty fizetési módra vonatkozó %#általános szerződési feltételeket%#. A Riverty adatvédelmi szabályzata %#itt%# található.",paymentConditions:"fizetési feltételeit",openApp:"Alkalmazás megnyitása","voucher.readInstructions":"Olvassa el az utasításokat","voucher.introduction":"Köszönjük a vásárlást! Kérjük, a fizetéshez használja a következő kupont.","voucher.expirationDate":"Lejárati dátum","voucher.alternativeReference":"Alternatív hivatkozás","dragonpay.voucher.non.bank.selectField.placeholder":"Szolgáltató kiválasztása","dragonpay.voucher.bank.selectField.placeholder":"Bank kiválasztása","voucher.paymentReferenceLabel":"Fizetési referencia","voucher.surcharge":"%@ pótdíjat tartalmaz","voucher.introduction.doku":"Köszönjük a vásárlást! Kérjük, a fizetéshez használja a következő információt.","voucher.shopperName":"Vásárló neve","voucher.merchantName":"Kereskedő","voucher.introduction.econtext":"Köszönjük a vásárlást! Kérjük, a fizetéshez használja a következő információt.","voucher.telephoneNumber":"Telefonszám","voucher.shopperReference":"Vásárlói referencia","voucher.collectionInstitutionNumber":"Beszedő cég száma","voucher.econtext.telephoneNumber.invalid":"A telefonszámnak 10 vagy 11 számjegyből kell állnia","boletobancario.btnLabel":"Boleto létrehozása","boleto.sendCopyToEmail":"Másolat küldése az e-mail-címemre","button.copy":"Másolás","button.download":"Letöltés","boleto.socialSecurityNumber.invalid":"Adjon meg egy érvényes CPF-/CNPJ-számot","creditCard.storedCard.description.ariaLabel":"A tárolt kártya számának végződése: %@","voucher.entity":"Entitás",donateButton:"Adományozás",notNowButton:"Most nem",thanksForYourSupport:"Köszönjük a támogatását!","resultMessages.preauthorized":"Részletek mentve",preauthorizeWith:"Előzetes meghatalmazás a következővel:",confirmPreauthorization:"Előzetes meghatalmazás jóváhagyása",confirmPurchase:"Fizetés jóváhagyása",applyGiftcard:"Beváltás",giftcardBalance:"Ajándékkártya egyenlege",deductedBalance:"Levont egyenleg","creditCard.pin.title":"PIN-kód","creditCard.encryptedPassword.label":"Kártya jelszavának első 2 számjegye","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Érvénytelen jelszó","creditCard.taxNumber":"Kártyatulajdonos születési dátuma vagy cégjegyzékszám","creditCard.taxNumber.label":"Kártyatulajdonos születési dátuma (ÉÉHHNN) vagy cégjegyzékszám (10 számjegyű)","creditCard.taxNumber.labelAlt":"Cégjegyzékszám (10 számjegyű)","creditCard.taxNumber.invalid":"A kártyatulajdonos születési dátuma vagy a cégjegyzékszám érvénytelen","storedPaymentMethod.disable.button":"Eltávolítás","storedPaymentMethod.disable.confirmation":"Tárolt fizetési mód eltávolítása","storedPaymentMethod.disable.confirmButton":"Igen, eltávolítom","storedPaymentMethod.disable.cancelButton":"Mégse","ach.bankAccount":"Bankszámla","ach.accountHolderNameField.title":"Számlatulajdonos neve","ach.accountHolderNameField.placeholder":"Gipsz Jakab","ach.accountHolderNameField.invalid":"A számlatulajdonos neve érvénytelen","ach.accountNumberField.title":"Számlaszám","ach.accountNumberField.invalid":"Érvénytelen számlaszám","ach.accountLocationField.title":"ABA-irányítószám","ach.accountLocationField.invalid":"Érvénytelen ABA-irányítószám","ach.savedBankAccount":"Mentett bankszámla","ach.savings":"Betétszámla","ach.checking":"Folyószámla","select.state":"Állam kiválasztása","select.stateOrProvince":"Állam vagy tartomány kiválasztása","select.provinceOrTerritory":"Tartomány vagy terület kiválasztása","select.country":"Ország/régió kiválasztása","select.noOptionsFound":"Nincsenek találatok","select.filter.placeholder":"Keresés...","telephoneNumber.invalid":"Érvénytelen telefonszám",qrCodeOrApp:"vagy","paypal.processingPayment":"Fizetés feldolgozása…",generateQRCode:"QR-kód létrehozása","await.waitForConfirmation":"Várakozás a jóváhagyásra","mbway.confirmPayment":"Fizetés jóváhagyása az MB WAY alkalmazásban","shopperEmail.invalid":"Érvénytelen e-mail-cím","dateOfBirth.format":"NN/HH/ÉÉÉÉ","dateOfBirth.invalid":"Adjon meg egy érvényes születési dátumot, amelyből kiderül, hogy elmúlt 18 éves","blik.confirmPayment":"A fizetés jóváhagyásához nyissa meg a banki alkalmazást.","blik.invalid":"Adjon meg 6 számjegyet","blik.code":"6 számjegyű kód","blik.help":"Kód lekérése a banki alkalmazásból.","swish.pendingMessage":"A QR-kód beolvasását követően az állapot akár 10 percig is függőben lehet. Ha eközben újból fizetést kísérel meg, az többszöri fizetést eredményezhet.","field.valid":"Érvényes mező","field.invalid":"Érvénytelen mező","error.va.gen.01":"Hiányos mező","error.va.gen.02":"Érvénytelen mező","error.va.sf-cc-num.01":"Adjon meg egy érvényes kártyaszámot","error.va.sf-cc-num.02":"Adja meg a kártya számát","error.va.sf-cc-num.03":"Adjon meg egy elfogadott kártyakibocsátót","error.va.sf-cc-num.04":"Adja meg a teljes kártyaszámot","error.va.sf-cc-dat.01":"Adjon meg egy érvényes lejárati dátumot","error.va.sf-cc-dat.02":"Adjon meg egy érvényes lejárati dátumot","error.va.sf-cc-dat.03":"Hamarosan lejáró hitelkártya","error.va.sf-cc-dat.04":"Adja meg a lejárati dátumot","error.va.sf-cc-dat.05":"Adja meg a teljes lejárati dátumot","error.va.sf-cc-mth.01":"Adja meg a lejárati hónapot","error.va.sf-cc-yr.01":"Adja meg a lejárat évet","error.va.sf-cc-yr.02":"Adja meg a teljes lejárati évet","error.va.sf-cc-cvc.01":"Adja meg a biztonsági kódot","error.va.sf-cc-cvc.02":"Adja meg a teljes biztonsági kódot","error.va.sf-ach-num.01":"A bankszámlaszám mezője üres","error.va.sf-ach-num.02":"A bankszámlaszám nem megfelelő hosszúságú","error.va.sf-ach-loc.01":"A bankazonosító kód mezője üres","error.va.sf-ach-loc.02":"A bankazonosító kód nem megfelelő hosszúságú","error.va.sf-kcp-pwd.01":"A jelszó mezője üres","error.va.sf-kcp-pwd.02":"A jelszó nem megfelelő hosszúságú","error.giftcard.no-balance":"Az ajándékkártya egyenlege nulla","error.giftcard.card-error":"Nyilvántartásunkban nem szerepel ilyen számú ajándékkártya","error.giftcard.currency-error":"Az ajándékkártyák csak abban a pénznemben érvényesek, amelyre kiállították azokat","amazonpay.signout":"Kijelentkezés az Amazonról","amazonpay.changePaymentDetails":"Fizetési adatok módosítása","partialPayment.warning":"Másik fizetési mód választása a fennmaradó rész fizetéséhez","partialPayment.remainingBalance":"A fennmaradó egyenleg %{amount} lesz","bankTransfer.beneficiary":"Kedvezményezett","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referencia","bankTransfer.introduction":"Folytassa, hogy elindítson egy új banki átutalásos fizetést. A fizetés véglegesítéséhez felhasználhatja a következő képernyőn megjelenő adatokat.","bankTransfer.instructions":"Köszönjük a vásárlást! Kérjük, a fizetéshez használja a következő információt.","bacs.accountHolderName":"Bankszámla-tulajdonos neve","bacs.accountHolderName.invalid":"A bankszámla-tulajdonos neve érvénytelen","bacs.accountNumber":"Bankszámlaszám","bacs.accountNumber.invalid":"Érvénytelen bankszámlaszám","bacs.bankLocationId":"Banki azonosító","bacs.bankLocationId.invalid":"Érvénytelen banki azonosító","bacs.consent.amount":"Elfogadom, hogy a fenti összeget levonják a bankszámlámról.","bacs.consent.account":"Megerősítem, hogy a bankszámla az én nevemen van, és én vagyok a bankszámlát érintő beszedési megbízás jóváhagyásához szükséges egyetlen aláíró.",edit:"Szerkesztés","bacs.confirm":"Megerősítés és fizetés","bacs.result.introduction":"Beszedési megbízási utasítás (meghatalmazás) letöltése","download.pdf":"PDF letöltése","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe a kártyaszámhoz","creditCard.encryptedCardNumber.aria.label":"Kártyaszám","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe a lejárati dátumhoz","creditCard.encryptedExpiryDate.aria.label":"Lejárati dátum","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe a lejárati hónaphoz","creditCard.encryptedExpiryMonth.aria.label":"Lejárati hónap","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe a lejárati évhez","creditCard.encryptedExpiryYear.aria.label":"Lejárati év","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe a biztonsági kódhoz","creditCard.encryptedSecurityCode.aria.label":"Biztonsági kód","creditCard.encryptedPassword.aria.iframeTitle":"Iframe a jelszóhoz","creditCard.encryptedPassword.aria.label":"Kártya jelszavának első 2 számjegye","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe a kártyaszámhoz","giftcard.encryptedCardNumber.aria.label":"Kártyaszám","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe a PIN-kódhoz","giftcard.encryptedSecurityCode.aria.label":"PIN-kód",giftcardTransactionLimit:"Ezen az ajándékkártyán a tranzakciónként engedélyezett maximális összeg %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe a bankszámlaszámhoz","ach.encryptedBankAccountNumber.aria.label":"Számlaszám","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe a bankazonosító kódhoz","ach.encryptedBankLocationId.aria.label":"ABA-irányítószám","twint.saved":"mentve",orPayWith:"vagy fizessen ezzel:",invalidFormatExpects:"Érvénytelen formátum. Várt formátum: %{format}","upi.qrCodeWaitingMessage":"A fizetés befejezéséhez olvassa be a QR-kódot a kívánt UPI-alkalmazással","upi.vpaWaitingMessage":"A fizetés megerősítéséhez nyissa meg UPI-alkalmazást","upi.modeSelection":"Hogyan szeretné használni az UPI-t?","upi.completePayment":"Fizetés végrehajtása","upi.mode.enterUpiId":"UPI-azonosító megadása","upi.mode.qrCode":"QR-kód","upi.mode.payByAnyUpi":"Fizetés bármilyen UPI-alkalmazással","upi.collect.dropdown.label":"UPI-azonosító megadása","upi.collect.field.label":"UPI-azonosító/VPA megadása","onlineBanking.termsAndConditions":"A folytatással elfogadja az %#általános szerződési feltételeket%#","onlineBankingPL.termsAndConditions":"A folytatással elfogadja a Przelewy24 %#szabályait%# és %#tájékoztatási kötelezettségét%#","ctp.loading.poweredByCtp":"Szolgáltató: Click to Pay","ctp.loading.intro":"Ellenőrizzük, hogy van-e mentett Click to Pay-kártyája…","ctp.login.title":"Tovább a Click to Pay felületére","ctp.login.subtitle":"A folytatáshoz adja meg a Click to Payhez kapcsolódó e-mail-címet.","ctp.login.inputLabel":"E-mail-cím","ctp.logout.notYou":"Nem Ön az?","ctp.logout.notYourCards":"Nem az Ön kártyái?","ctp.logout.notYourCard":"Nem az Ön kártyája?","ctp.logout.notYourProfile":"Nem az Ön profilja?","ctp.otp.fieldLabel":"Egyszeri kód","ctp.otp.resendCode":"Kód újraküldése","ctp.otp.codeResent":"Kód újraküldve","ctp.otp.title":"Hozzáférés a Click to Pay-kártyáihoz","ctp.otp.subtitle":"Írja be a kódot, amit a(z) %@ küldött %@ részére ellenőrzés céljából.","ctp.otp.saveCookiesCheckbox.label":"Ellenőrzés kihagyása a következő alkalommal","ctp.otp.saveCookiesCheckbox.information":"Jelölje be, hogy a résztvevő üzletek a gyorsabb fizetés érdekében emlékezzenek az eszközre és a böngészőre. Megosztott eszközök esetén nem ajánlott.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Jelölje be, hogy a rendszer emlékezzen az eszközre és a böngészőre","ctp.emptyProfile.message":"Ebben a Click to Pay-profilban nincs regisztrált kártya","ctp.separatorText":"vagy használja a következőt","ctp.cards.title":"Fizetés Click to Pay használatával","ctp.cards.subtitle":"Válassza ki a használni kívánt kártyát.","ctp.cards.expiredCard":"Lejárt","ctp.manualCardEntry":"Kártya adatainak kézi megadása","ctp.aria.infoModalButton":"Mi az a Click to Pay","ctp.infoPopup.title":"A Click to Pay megkönnyíti az érintés nélküli online fizetést","ctp.infoPopup.subtitle":"A Mastercard, a Visa és más fizetési kártyák által támogatott gyors és biztonságos fizetési mód.","ctp.infoPopup.benefit1":"A Click to Pay az adatok biztonsága érdekében titkosítást használ","ctp.infoPopup.benefit2":"Világszerte használható a kereskedőknél","ctp.infoPopup.benefit3":"Egyszer kell beállítani, hogy a jövőben problémamentesen fizethessen","ctp.errors.AUTH_INVALID":"Érvénytelen hitelesítés","ctp.errors.NOT_FOUND":"Nem található fiók, adjon meg egy érvényes e-mail-címet, vagy folytassa a kártyaadatok manuális bevitelével","ctp.errors.ID_FORMAT_UNSUPPORTED":"Nem támogatott formátum","ctp.errors.FRAUD":"A felhasználói fiók zárolva volt vagy le volt tiltva","ctp.errors.CONSUMER_ID_MISSING":"A kérésből hiányzik az ügyfél-azonosító","ctp.errors.ACCT_INACCESSIBLE":"A fiók jelenleg nem érhető el, pl. azért, mert zárolva van","ctp.errors.CODE_INVALID":"Helytelen ellenőrző kód","ctp.errors.CODE_EXPIRED":"A kód lejárt","ctp.errors.RETRIES_EXCEEDED":"Túllépte az egyszeri jelszó létrehozására tett lehetséges próbálkozások maximális számát","ctp.errors.OTP_SEND_FAILED":"Az egyszeri jelszót nem sikerült elküldeni a címzettnek","ctp.errors.REQUEST_TIMEOUT":"Hiba történt, próbálkozzon újra, vagy manuálisan vigye be a kártyát","ctp.errors.UNKNOWN_ERROR":"Hiba történt, próbálkozzon újra, vagy manuálisan adja meg a kártyaadatokat","ctp.errors.SERVICE_ERROR":"Hiba történt, próbálkozzon újra, vagy manuálisan adja meg a kártyaadatokat","ctp.errors.SERVER_ERROR":"Hiba történt, próbálkozzon újra, vagy manuálisan adja meg a kártyaadatokat","ctp.errors.INVALID_PARAMETER":"Hiba történt, próbálkozzon újra, vagy manuálisan adja meg a kártyaadatokat","ctp.errors.AUTH_ERROR":"Hiba történt, próbálkozzon újra, vagy manuálisan adja meg a kártyaadatokat","paymentMethodsList.aria.label":"Válasszon fizetési módot","companyDetails.name.invalid":"Adja meg a cég nevét","companyDetails.registrationNumber.invalid":"Adja meg a cégjegyzékszámot","consent.checkbox.invalid":"El kell fogadnia az általános szerződési feltételeket","form.instruction":"Minden mező kitöltése kötelező, hacsak nincs másképp jelölve.","trustly.descriptor":"Azonnali banki fizetés","trustly.description1":"Fizessen közvetlenül bármelyik bankszámlájáról, banki szintű biztonság mellett","trustly.description2":"Nincs szükség kártyára, alkalmazás letöltésére és regisztrációra","ancv.input.label":"Az Ön ANCV-azonosítója","ancv.confirmPayment":"A fizetés megerősítéséhez használja az ANCV alkalmazást.","ancv.form.instruction":"A fizetés érvényesítéséhez a Cheque-Vacances alkalmazás szükséges.","ancv.beneficiaryId.invalid":"Adjon meg egy érvényes e-mail-címet vagy ANCV-azonosítót","payme.openPayMeApp":"A fizetésnek a PayMe alkalmazásban való engedélyezésével hajtsa végre a fizetést, és várja meg a visszaigazolást.","payme.redirectButtonLabel":"PayMe alkalmazás megnyitása","payme.scanQrCode":"Fizetés végrehajtása QR-kóddal","payme.timeToPay":"A QR-kód ennyi ideig érvényes: %@","payme.instructions.steps":"Nyissa meg a PayMe alkalmazást.%@A fizetés engedélyezéséhez olvassa be a QR-kódot.%@Hajtsa végre a fizetést az alkalmazásban, és várja meg a visszaigazolást.","payme.instructions.footnote":"A fizetés befejezése előtt ne zárja be ezt az oldalt","payByBankAISDD.disclaimer.header":"Ha bankszámláról szeretne azonnal fizetni, használja a Fizetés bankkal lehetőséget.","payByBankAISDD.disclaimer.body":"Bankszámlája csatlakoztatásával engedélyezi, hogy a szolgáltatásaink használatáért és/vagy termékeink megvásárlásáért levonandó összeget a számlájára terheljük egészen addig, amíg ezt az engedélyt vissza nem vonja.","paymentMethodBrand.other":"Egyéb"}},12016:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Betaal","payButton.redirecting":"U wordt doorverwezen...","payButton.with":"Betaal %{value} met %{maskedData}","payButton.saveDetails":"Gegevens opslaan",close:"Sluiten",storeDetails:"Bewaar voor mijn volgende betaling",readMore:"Verder lezen","creditCard.holderName":"Naam op kaart","creditCard.holderName.placeholder":"J. Janssen","creditCard.holderName.invalid":"Voer naam in zoals weergegeven op kaart","creditCard.numberField.title":"Kaartnummer","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Vervaldatum","creditCard.expiryDateField.placeholder":"MM/JJ","creditCard.expiryDateField.month":"Maand","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"JJ","creditCard.expiryDateField.year":"Jaar","creditCard.cvcField.title":"Beveiligingscode","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Onthouden voor de volgende keer","creditCard.cvcField.placeholder.4digits":"4 cijfers","creditCard.cvcField.placeholder.3digits":"3 cijfers","creditCard.taxNumber.placeholder":"JJMMDD / 0123456789",installments:"Aantal termijnen",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} maanden","installments.oneTime":"Eenmalige betaling","installments.installments":"Betaling termijnen","installments.revolving":"Terugkerende betaling","sepaDirectDebit.ibanField.invalid":"Ongeldig rekeningnummer","sepaDirectDebit.nameField.placeholder":"J. Janssen","sepa.ownerName":"Ten name van","sepa.ibanNumber":"Rekeningnummer (IBAN)","error.title":"Fout","error.subtitle.redirect":"Doorsturen niet gelukt","error.subtitle.payment":"Betaling is niet geslaagd","error.subtitle.refused":"Betaling geweigerd","error.message.unknown":"Er is een onbekende fout opgetreden","errorPanel.title":"Bestaande fouten","idealIssuer.selectField.title":"Bank","idealIssuer.selectField.placeholder":"Selecteer uw bank","creditCard.success":"Betaling geslaagd",loading:"Laden...",continue:"Doorgaan",continueTo:"Doorgaan naar","wechatpay.timetopay":"U heeft %@ om te betalen","sr.wechatpay.timetopay":"U hebt %#minuten%# %#seconden%# om te betalen","wechatpay.scanqrcode":"QR-code scannen",personalDetails:"Persoonlijke gegevens",companyDetails:"Bedrijfsgegevens","companyDetails.name":"Bedrijfsnaam","companyDetails.registrationNumber":"Registratienummer",socialSecurityNumber:"Burgerservicenummer",firstName:"Voornaam","firstName.invalid":"Voer je voornaam in",infix:"Voorvoegsel",lastName:"Achternaam","lastName.invalid":"Voer je achternaam in",mobileNumber:"Telefoonnummer mobiel","mobileNumber.invalid":"Ongeldig mobiel nummer",city:"Stad",postalCode:"Postcode","postalCode.optional":"Postcode (optioneel)",countryCode:"Landcode",telephoneNumber:"Telefoonnummer",dateOfBirth:"Geboortedatum",shopperEmail:"E-mailadres",gender:"Geslacht","gender.notselected":"Selecteer uw geslacht",male:"Man",female:"Vrouw",billingAddress:"Factuuradres",street:"Straatnaam",stateOrProvince:"Staat of provincie",country:"Land/regio",houseNumberOrName:"Huisnummer",separateDeliveryAddress:"Een afwijkend bezorgadres opgeven",deliveryAddress:"Bezorgadres","deliveryAddress.firstName":"Voornaam ontvanger","deliveryAddress.lastName":"Achternaam ontvanger",zipCode:"Postcode",apartmentSuite:"Appartement/Suite",provinceOrTerritory:"Provincie of territorium",cityTown:"Stad",address:"Adres","address.placeholder":"Zoek uw adres","address.errors.incomplete":"Voer een adres in om verder te gaan","address.enterManually":"Voer het adres handmatig in",state:"Staat","field.title.optional":"(optioneel)","creditCard.cvcField.title.optional":"Beveiligingscode (optioneel)","issuerList.wallet.placeholder":"Selecteer uw portemonnee",privacyPolicy:"Privacybeleid","afterPay.agreement":"Ik ga akkoord met de %@ van Riverty","riverty.termsAndConditions":"Ik ga akkoord met de %#algemene voorwaarden%# voor de betalingsmethode Riverty. Het privacybeleid van Riverty vindt u %#hier%#.",paymentConditions:"betalingsvoorwaarden",openApp:"Open de app","voucher.readInstructions":"Instructies lezen","voucher.introduction":"Bedankt voor uw aankoop. Gebruik deze coupon om uw betaling te voltooien.","voucher.expirationDate":"Vervaldatum","voucher.alternativeReference":"Alternatieve referentie","dragonpay.voucher.non.bank.selectField.placeholder":"Selecteer uw aanbieder","dragonpay.voucher.bank.selectField.placeholder":"Selecteer uw bank","voucher.paymentReferenceLabel":"Betalingsreferentie","voucher.surcharge":"Inclusief %@ toeslag","voucher.introduction.doku":"Bedankt voor uw aankoop. Gebruik de volgende informatie om uw betaling te voltooien.","voucher.shopperName":"Klantnaam","voucher.merchantName":"Verkoper","voucher.introduction.econtext":"Bedankt voor uw aankoop. Gebruik de volgende informatie om uw betaling te voltooien.","voucher.telephoneNumber":"Telefoonnummer","voucher.shopperReference":"Klant referentie","voucher.collectionInstitutionNumber":"Nummer ophaallocatie","voucher.econtext.telephoneNumber.invalid":"Het telefoonnummer moet uit 10 of 11 cijfers bestaan","boletobancario.btnLabel":"Boleto genereren","boleto.sendCopyToEmail":"Stuur een kopie naar mijn e-mailadres","button.copy":"Kopiëren","button.download":"Downloaden","boleto.socialSecurityNumber.invalid":"Voer een geldig CPF/CNPJ-nummer in","creditCard.storedCard.description.ariaLabel":"Opgeslagen kaart eindigt op %@","voucher.entity":"Entiteit",donateButton:"Doneren",notNowButton:"Niet nu",thanksForYourSupport:"Bedankt voor uw donatie!","resultMessages.preauthorized":"Gegevens opgeslagen",preauthorizeWith:"Preautorisatie uitvoeren met",confirmPreauthorization:"Preautorisatie bevestigen",confirmPurchase:"Aankoop bevestigen",applyGiftcard:"Inwisselen",giftcardBalance:"Saldo cadeaukaart",deductedBalance:"Afgetrokken bedrag","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Eerste twee cijfers van het wachtwoord van de kaart","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Ongeldig wachtwoord","creditCard.taxNumber":"Geboortedatum of bedrijfsregistratienummer van kaarthouder","creditCard.taxNumber.label":"Geboortedatum (JJ-MM-DD) of bedrijfsregistratienummer (10 cijfers) van kaarthouder","creditCard.taxNumber.labelAlt":"Bedrijfsregistratienummer (10 cijfers)","creditCard.taxNumber.invalid":"Geboortedatum of bedrijfsregistratienummer van kaarthouder is ongeldig","storedPaymentMethod.disable.button":"Verwijderen","storedPaymentMethod.disable.confirmation":"Opgeslagen betalingsmethode verwijderen","storedPaymentMethod.disable.confirmButton":"Ja, verwijderen","storedPaymentMethod.disable.cancelButton":"Annuleren","ach.bankAccount":"Bankrekening","ach.accountHolderNameField.title":"Naam rekeninghouder","ach.accountHolderNameField.placeholder":"J. Janssen","ach.accountHolderNameField.invalid":"Ongeldige naam rekeninghouder","ach.accountNumberField.title":"Rekeningnummer","ach.accountNumberField.invalid":"Ongeldig rekeningnummer","ach.accountLocationField.title":"Routingnummer (ABA)","ach.accountLocationField.invalid":"Ongeldig routingnummer (ABA)","ach.savedBankAccount":"Opgeslagen bankrekening","ach.savings":"Spaarrekening","ach.checking":"Betaalrekening","select.state":"Selecteer staat","select.stateOrProvince":"Selecteer staat of provincie","select.provinceOrTerritory":"Selecteer provincie of territorium","select.country":"Selecteer land/regio","select.noOptionsFound":"Geen opties gevonden","select.filter.placeholder":"Zoeken...","telephoneNumber.invalid":"Ongeldig telefoonnummer",qrCodeOrApp:"of","paypal.processingPayment":"Betaling wordt verwerkt...",generateQRCode:"Genereer QR-code","await.waitForConfirmation":"Wacht op bevestiging","mbway.confirmPayment":"Bevestig uw betaling via de MB WAY-app","shopperEmail.invalid":"Ongeldig e-mailadres","dateOfBirth.format":"DD/MM/JJJJ","dateOfBirth.invalid":"Voer een geldige geboortedatum in die aangeeft dat u ten minste 18 jaar oud bent","blik.confirmPayment":"Open uw bankapp om de betaling te bevestigen.","blik.invalid":"Voer 6 cijfers in","blik.code":"6-cijferige code","blik.help":"Haal de code op in uw bankapp.","swish.pendingMessage":"Nadat u hebt gescand, kan de status maximaal 10 minuten in behandeling zijn. Als u binnen deze periode opnieuw probeert te betalen, kunnen er meerdere keren kosten in rekening worden gebracht.","field.valid":"Geldig veld","field.invalid":"Veld niet geldig","error.va.gen.01":"Onvolledig veld","error.va.gen.02":"Veld niet geldig","error.va.sf-cc-num.01":"Voer een geldig kaartnummer in","error.va.sf-cc-num.02":"Voer het kaartnummer in","error.va.sf-cc-num.03":"Voer een ondersteund kaartmerk in","error.va.sf-cc-num.04":"Voer het volledige kaartnummer in","error.va.sf-cc-dat.01":"Voer een geldige vervaldatum in","error.va.sf-cc-dat.02":"Voer een geldige vervaldatum in","error.va.sf-cc-dat.03":"Creditcard vervalt bijna","error.va.sf-cc-dat.04":"Voer de vervaldatum in","error.va.sf-cc-dat.05":"Voer de volledige vervaldatum in","error.va.sf-cc-mth.01":"Voer de vervalmaand in","error.va.sf-cc-yr.01":"Voer het vervaljaar in","error.va.sf-cc-yr.02":"Voer het volledige vervaljaar in","error.va.sf-cc-cvc.01":"Voer de beveiligingscode in","error.va.sf-cc-cvc.02":"Voer de volledige beveiligingscode in","error.va.sf-ach-num.01":"Leeg veld voor bankrekeningnummer","error.va.sf-ach-num.02":"Bankrekeningnummer heeft de verkeerde lengte","error.va.sf-ach-loc.01":"Leeg veld voor swiftcode van de bank","error.va.sf-ach-loc.02":"Swiftcode van de bank heeft de verkeerde lengte","error.va.sf-kcp-pwd.01":"Leeg wachtwoordveld","error.va.sf-kcp-pwd.02":"Wachtwoord heeft de verkeerde lengte","error.giftcard.no-balance":"Deze cadeaukaart heeft geen saldo","error.giftcard.card-error":"We hebben geen cadeaukaart met dit nummer in onze administratie","error.giftcard.currency-error":"Cadeaukaarten zijn alleen geldig in de valuta waarin ze zijn uitgegeven","amazonpay.signout":"Afmelden bij Amazon","amazonpay.changePaymentDetails":"Betalingsgegevens wijzigen","partialPayment.warning":"Selecteer een andere betaalmethode om het resterende deel te betalen","partialPayment.remainingBalance":"Het resterende saldo is %{amount}","bankTransfer.beneficiary":"Begunstigde","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referentie","bankTransfer.introduction":"Ga door om een nieuwe overschrijving aan te maken. U kunt de gegevens in het volgende scherm gebruiken om deze betaling af te ronden.","bankTransfer.instructions":"Bedankt voor uw aankoop. Gebruik de volgende informatie om uw betaling te voltooien.","bacs.accountHolderName":"Naam bankrekeninghouder","bacs.accountHolderName.invalid":"Ongeldige naam bankrekeninghouder","bacs.accountNumber":"Bankrekeningnummer","bacs.accountNumber.invalid":"Ongeldig bankrekeningnummer","bacs.bankLocationId":"Bankcode","bacs.bankLocationId.invalid":"Ongeldige bankcode","bacs.consent.amount":"Ik ga ermee akkoord dat het bovengenoemde bedrag van mijn bankrekening wordt afgeschreven.","bacs.consent.account":"Ik bevestig dat de rekening op mijn naam staat en dat ik de enige ondertekenaar ben die toestemming kan geven voor automatische incasso's vanaf deze rekening.",edit:"Bewerken","bacs.confirm":"Bevestigen en betalen","bacs.result.introduction":"Download uw machtiging automatische incasso","download.pdf":"PDF downloaden","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe voor kaartnummer","creditCard.encryptedCardNumber.aria.label":"Kaartnummer","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe voor vervaldatum","creditCard.encryptedExpiryDate.aria.label":"Vervaldatum","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe voor vervalmaand","creditCard.encryptedExpiryMonth.aria.label":"Vervalmaand","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe voor vervaljaar","creditCard.encryptedExpiryYear.aria.label":"Vervaljaar","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe voor beveiligingscode","creditCard.encryptedSecurityCode.aria.label":"Beveiligingscode","creditCard.encryptedPassword.aria.iframeTitle":"Iframe voor wachtwoord","creditCard.encryptedPassword.aria.label":"Eerste twee cijfers van het wachtwoord van de kaart","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe voor kaartnummer","giftcard.encryptedCardNumber.aria.label":"Kaartnummer","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe voor PIN","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Max. %{amount} toegestaan per transactie met deze cadeaubon","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe voor bankrekeningnummer","ach.encryptedBankAccountNumber.aria.label":"Rekeningnummer","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe voor bankroutingnummer","ach.encryptedBankLocationId.aria.label":"Routingnummer (ABA)","twint.saved":"opgeslagen",orPayWith:"of betaal met",invalidFormatExpects:"Ongeldige indeling. Verwachte indeling: %{format}","upi.qrCodeWaitingMessage":"Scan de QR-code met je favoriete UPI-app om de betaling te voltooien","upi.vpaWaitingMessage":"Open je UPI-app om de betaling te bevestigen","upi.modeSelection":"Hoe wilt u UPI gebruiken?","upi.completePayment":"U betaling afronden","upi.mode.enterUpiId":"UPI-ID invoeren","upi.mode.qrCode":"QR-code","upi.mode.payByAnyUpi":"Betaal via elke UPI-app","upi.collect.dropdown.label":"UPI-ID invoeren","upi.collect.field.label":"UPI-ID/VPA invoeren","onlineBanking.termsAndConditions":"Door verder te gaan gaat u akkoord met de %#algemene voorwaarden%#","onlineBankingPL.termsAndConditions":"Door verder te gaan stemt u in met de %#richtlijnen%# en %#informatieverplichting%# van Przelewy24","ctp.loading.poweredByCtp":"Mogelijk gemaakt door Click to Pay","ctp.loading.intro":"We controleren of u opgeslagen kaarten heeft bij Click to Pay...","ctp.login.title":"Ga verder naar Click to Pay","ctp.login.subtitle":"Voer het aan Click to Pay gekoppelde e-mailadres in om verder te gaan.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Bent u dit niet?","ctp.logout.notYourCards":"Niet uw kaarten?","ctp.logout.notYourCard":"Niet uw kaart?","ctp.logout.notYourProfile":"Niet uw profiel?","ctp.otp.fieldLabel":"Eenmalige code","ctp.otp.resendCode":"Code opnieuw verzenden","ctp.otp.codeResent":"Code opnieuw verzonden","ctp.otp.title":"Ga naar uw Click to Pay-kaarten","ctp.otp.subtitle":"Voer de code %@ in die naar %@ is gestuurd, zodat we kunnen verifiëren dat u het bent.","ctp.otp.saveCookiesCheckbox.label":"Verificatie de volgende keer overslaan","ctp.otp.saveCookiesCheckbox.information":"Selecteer om te worden onthouden op uw apparaat en in uw browser bij deelnemende winkels, zodat u sneller kunt afrekenen. Niet aanbevolen voor gedeelde apparaten.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Selecteer om onthouden te worden op uw apparaat en browser","ctp.emptyProfile.message":"Geen kaarten geregistreerd voor dit Click to Pay-profiel","ctp.separatorText":"of gebruik","ctp.cards.title":"Voltooi de betaling met Click to Pay","ctp.cards.subtitle":"Selecteer een kaart om te gebruiken.","ctp.cards.expiredCard":"Verlopen","ctp.manualCardEntry":"Handmatige kaartinvoer","ctp.aria.infoModalButton":"Wat is Click to Pay?","ctp.infoPopup.title":"Met Click to Pay betaalt u nu ook contactloos online","ctp.infoPopup.subtitle":"Een snelle en veilige betaalmethode, ondersteund door Mastercard, Visa en andere betaalkaarten.","ctp.infoPopup.benefit1":"Click to Pay maakt gebruik van versleuteling om uw informatie veilig te houden","ctp.infoPopup.benefit2":"Gebruik het bij winkeliers over de hele wereld","ctp.infoPopup.benefit3":"Eén keer instellen voor probleemloze betalingen","ctp.errors.AUTH_INVALID":"Authenticatie ongeldig","ctp.errors.NOT_FOUND":"Geen account gevonden, voer een geldig e-mailadres in of ga door met het handmatig invoeren van kaarten","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formaat wordt niet ondersteund","ctp.errors.FRAUD":"Gebruikersaccount vergrendeld of uitgeschakeld","ctp.errors.CONSUMER_ID_MISSING":"Identiteit van de consument ontbreekt in het verzoek","ctp.errors.ACCT_INACCESSIBLE":"Dit account is momenteel niet beschikbaar, omdat het bijvoorbeeld is vergrendeld","ctp.errors.CODE_INVALID":"Onjuiste verificatiecode","ctp.errors.CODE_EXPIRED":"Deze code is verlopen","ctp.errors.RETRIES_EXCEEDED":"De limiet voor het aantal pogingen voor het genereren van een OTP is overschreden","ctp.errors.OTP_SEND_FAILED":"Het OTP kan niet naar de ontvanger worden verzonden","ctp.errors.REQUEST_TIMEOUT":"Er is iets fout gegaan, probeer het opnieuw of gebruik de handmatige kaartinvoer","ctp.errors.UNKNOWN_ERROR":"Er is iets fout gegaan, probeer het opnieuw of gebruik de handmatige kaartinvoer","ctp.errors.SERVICE_ERROR":"Er is iets fout gegaan, probeer het opnieuw of gebruik de handmatige kaartinvoer","ctp.errors.SERVER_ERROR":"Er is iets fout gegaan, probeer het opnieuw of gebruik de handmatige kaartinvoer","ctp.errors.INVALID_PARAMETER":"Er is iets fout gegaan, probeer het opnieuw of gebruik de handmatige kaartinvoer","ctp.errors.AUTH_ERROR":"Er is iets fout gegaan, probeer het opnieuw of gebruik de handmatige kaartinvoer","paymentMethodsList.aria.label":"Kies een betaalmethode","companyDetails.name.invalid":"Voer de bedrijfsnaam in","companyDetails.registrationNumber.invalid":"Voer het registratienummer in","consent.checkbox.invalid":"Je moet akkoord gaan met de algemene voorwaarden","form.instruction":"Alle velden zijn verplicht, tenzij anders aangegeven.","trustly.descriptor":"Directe bankbetaling","trustly.description1":"Rechtstreeks betalen vanaf een van uw bankrekeningen, ondersteund door beveiliging op bankniveau","trustly.description2":"Geen kaarten, geen app downloaden, geen registratie","ancv.input.label":"Uw ANCV-identificatie","ancv.confirmPayment":"Gebruik uw ANCV-toepassing om de betaling te bevestigen.","ancv.form.instruction":"De Cheque-Vacances applicatie is nodig om deze betaling te valideren.","ancv.beneficiaryId.invalid":"Voer een geldig e-mailadres of ANCV-id in","payme.openPayMeApp":"Voltooi uw betaling door deze te autoriseren in de PayMe-app en de bevestiging af te wachten.","payme.redirectButtonLabel":"Open de PayMe-app","payme.scanQrCode":"Voltooi uw betaling met een QR-code","payme.timeToPay":"Deze QR-code is %@ geldig","payme.instructions.steps":"Open de PayMe-app.%@Scan de QR-code om de betaling te autoriseren.%@Voltooi de betaling in de app en wacht de bevestiging af.","payme.instructions.footnote":"Sluit deze pagina niet voordat de betaling is voltooid","payByBankAISDD.disclaimer.header":"Gebruik Pay by Bank om direct vanaf elke bankrekening te betalen.","payByBankAISDD.disclaimer.body":"Door uw bankrekening te koppelen, machtigt u afschrijvingen van uw rekening voor elk bedrag dat u verschuldigd bent voor het gebruik van onze diensten en/of de aankoop van onze producten, totdat deze machtiging wordt ingetrokken.","paymentMethodBrand.other":"overige"}},20106:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Betal","payButton.redirecting":"Omdirigerer...","payButton.with":"Betal %{value} med %{maskedData}","payButton.saveDetails":"Lagre detaljer",close:"Lukk",storeDetails:"Lagre til min neste betaling",readMore:"Les mer","creditCard.holderName":"Navn på kortet","creditCard.holderName.placeholder":"O. Nordmann","creditCard.holderName.invalid":"Skriv inn navnet som vist på kortet","creditCard.numberField.title":"Kortnummer","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Utløpsdato","creditCard.expiryDateField.placeholder":"MM/ÅÅ","creditCard.expiryDateField.month":"Måned","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"ÅÅ","creditCard.expiryDateField.year":"År","creditCard.cvcField.title":"Sikkerhetskode","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Husk til neste gang","creditCard.cvcField.placeholder.4digits":"4 siffer","creditCard.cvcField.placeholder.3digits":"3 siffer","creditCard.taxNumber.placeholder":"YYMMDD / 0123456789",installments:"Antall avdrag",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} måneder","installments.oneTime":"Engangsbetaling","installments.installments":"Avdragsbetaling","installments.revolving":"Gjentakende betaling","sepaDirectDebit.ibanField.invalid":"Ugyldig kontonummer","sepaDirectDebit.nameField.placeholder":"O. Nordmann","sepa.ownerName":"Kortholders navn","sepa.ibanNumber":"Kontonummer (IBAN)","error.title":"Feil","error.subtitle.redirect":"Videresending feilet","error.subtitle.payment":"Betaling feilet","error.subtitle.refused":"Betaling avvist","error.message.unknown":"En ukjent feil oppstod","errorPanel.title":"Eksisterende feil","idealIssuer.selectField.title":"Bank","idealIssuer.selectField.placeholder":"Velg din bank","creditCard.success":"Betalingen var vellykket",loading:"Laster...",continue:"Fortsett",continueTo:"Fortsett til","wechatpay.timetopay":"Du har %@ igjen til å betale","sr.wechatpay.timetopay":"Du har %#minutter%# og %#sekunder%# på deg til å betale","wechatpay.scanqrcode":"Skann QR-kode",personalDetails:"Personopplysninger",companyDetails:"Firmadetaljer","companyDetails.name":"Firmanavn","companyDetails.registrationNumber":"Registreringsnummer",socialSecurityNumber:"Personnummer",firstName:"Fornavn","firstName.invalid":"Skriv inn fornavnet ditt",infix:"Prefiks",lastName:"Etternavn","lastName.invalid":"Skriv inn etternavnet ditt",mobileNumber:"Mobilnummer","mobileNumber.invalid":"Ugyldig mobilnummer",city:"Poststed",postalCode:"Postnummer","postalCode.optional":"Postnummer (valgfritt)",countryCode:"Landkode",telephoneNumber:"Telefonnummer",dateOfBirth:"Fødselsdato",shopperEmail:"E-postadresse",gender:"Kjønn","gender.notselected":"Velg kjønn",male:"Mann",female:"Kvinne",billingAddress:"Faktureringsadresse",street:"Gate",stateOrProvince:"Fylke",country:"Land/region",houseNumberOrName:"Husnummer",separateDeliveryAddress:"Spesifiser en separat leveringsadresse",deliveryAddress:"Leveringsadresse","deliveryAddress.firstName":"Mottakerens fornavn","deliveryAddress.lastName":"Mottakerens etternavn",zipCode:"Postnummer",apartmentSuite:"Leilighet/suite",provinceOrTerritory:"Provins eller territorium",cityTown:"By",address:"Adresse","address.placeholder":"Finn adressen din","address.errors.incomplete":"Skriv inn en adresse for å fortsette","address.enterManually":"Skriv inn adressen manuelt",state:"Delstat","field.title.optional":"(valgfritt)","creditCard.cvcField.title.optional":"Sikkerhetskode (valgfritt)","issuerList.wallet.placeholder":"Velg lommebok",privacyPolicy:"Retningslinjer for personvern","afterPay.agreement":"Jeg godtar Rivertys %@","riverty.termsAndConditions":"Jeg godtar de generelle %#vilkårene%# for Riverty-betalingsmetoden. Personvernerklæringen til Riverty finner du %#her%#.",paymentConditions:"betalingsbetingelser",openApp:"Åpne appen","voucher.readInstructions":"Les instruksjoner","voucher.introduction":"Takk for ditt kjøp. Vennligst bruk den følgende kupongen til å fullføre betalingen.","voucher.expirationDate":"Utløpsdato","voucher.alternativeReference":"Alternativ referanse","dragonpay.voucher.non.bank.selectField.placeholder":"Velg din leverandør","dragonpay.voucher.bank.selectField.placeholder":"Velg din bank","voucher.paymentReferenceLabel":"Betalingsreferanse","voucher.surcharge":"Inkl. %@ tilleggsavgift","voucher.introduction.doku":"Takk for ditt kjøp, vennligst bruk den følgende informasjonen for å fullføre betalingen.","voucher.shopperName":"Kundenavn","voucher.merchantName":"Forhandler","voucher.introduction.econtext":"Takk for ditt kjøp, vennligst bruk den følgende informasjonen for å fullføre betalingen.","voucher.telephoneNumber":"Telefonnummer","voucher.shopperReference":"Kundereferanse","voucher.collectionInstitutionNumber":"Innbetalingslokasjonsnummer","voucher.econtext.telephoneNumber.invalid":"Telefonnummeret må være 10 eller 11 sifre langt","boletobancario.btnLabel":"Generer Boleto","boleto.sendCopyToEmail":"Send meg en kopi på e-post","button.copy":"Kopier","button.download":"Last ned","boleto.socialSecurityNumber.invalid":"Skriv inn et gyldig CPF/CNPJ-nummer","creditCard.storedCard.description.ariaLabel":"Lagret kort slutter på %@","voucher.entity":"Enhet",donateButton:"Donér",notNowButton:"Ikke nå",thanksForYourSupport:"Takk for din støtte!","resultMessages.preauthorized":"Detaljer lagret",preauthorizeWith:"Forhåndsgodkjenn med",confirmPreauthorization:"Bekreft forhåndsgodkjenning",confirmPurchase:"Bekreft kjøp",applyGiftcard:"Løs inn",giftcardBalance:"Gavekortsaldo",deductedBalance:"Fratrukket beløp","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Første 2 sifre av kortpassord","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Ugyldig passord","creditCard.taxNumber":"Kortholders fødselsdato eller bedriftsregistreringsnummer","creditCard.taxNumber.label":"Kortholders fødselsdato (YYMMDD) eller bedriftsregistreringsnummer (10 siffer)","creditCard.taxNumber.labelAlt":"Bedriftsregistreringsnummer (10 siffer)","creditCard.taxNumber.invalid":"Ugyldig kortholders fødselsdato eller bedriftsregistreringsnummer","storedPaymentMethod.disable.button":"Fjern","storedPaymentMethod.disable.confirmation":"Fjern lagret betalingsmetode","storedPaymentMethod.disable.confirmButton":"Ja, fjern","storedPaymentMethod.disable.cancelButton":"Avbryt","ach.bankAccount":"Bankkonto","ach.accountHolderNameField.title":"Kontoholders navn","ach.accountHolderNameField.placeholder":"O. Nordmann","ach.accountHolderNameField.invalid":"Ugyldig navn på kontoholder","ach.accountNumberField.title":"Kontonummer","ach.accountNumberField.invalid":"Ugyldig kontonummer","ach.accountLocationField.title":"ABA-dirigeringsnummer","ach.accountLocationField.invalid":"Ugyldig ABA-dirigeringsnummer","ach.savedBankAccount":"Lagret bankkonto","ach.savings":"Sparekonto","ach.checking":"Sjekkkonto","select.state":"Velg delstat","select.stateOrProvince":"Velg delstat eller provins","select.provinceOrTerritory":"Velg provins eller territorium","select.country":"Velg land/region","select.noOptionsFound":"Ingen alternativer funnet","select.filter.placeholder":"Søk…","telephoneNumber.invalid":"Ugyldig telefonnummer",qrCodeOrApp:"eller","paypal.processingPayment":"Behandler betaling…",generateQRCode:"Generer QR-kode","await.waitForConfirmation":"Venter på bekreftelse","mbway.confirmPayment":"Bekreft betalingen din i MB WAY-appen","shopperEmail.invalid":"Ugyldig e-postadresse","dateOfBirth.format":"DD/MM/ÅÅÅÅ","dateOfBirth.invalid":"Skriv inn en gyldig fødselsdato som viser at du er minst 18 år gammel","blik.confirmPayment":"Åpne bank-appen din for å bekrefte betalingen.","blik.invalid":"Tast inn 6 tall","blik.code":"6-sifret kode","blik.help":"Hent koden fra bank-appen din.","swish.pendingMessage":"Etter at du har skannet koden kan det ta opptil 10 minutter før betalingen vises som bekreftet. Forsøk på å betale igjen kan føre til flere innbetalinger.","field.valid":"Feltet er gyldig","field.invalid":"Feltet er ikke gyldig","error.va.gen.01":"Ufullstendig felt","error.va.gen.02":"Feltet er ikke gyldig","error.va.sf-cc-num.01":"Angi et gyldig kortnummer","error.va.sf-cc-num.02":"Angi kortnummeret","error.va.sf-cc-num.03":"Angi et støttet kortmerke","error.va.sf-cc-num.04":"Angi hele kortnummeret","error.va.sf-cc-dat.01":"Angi en gyldig utløpsdato","error.va.sf-cc-dat.02":"Angi en gyldig utløpsdato","error.va.sf-cc-dat.03":"Kredittkortet er i ferd med å utløpe","error.va.sf-cc-dat.04":"Angi utløpsdatoen","error.va.sf-cc-dat.05":"Angi den fullstendige utløpsdatoen","error.va.sf-cc-mth.01":"Angi utløpsmåneden","error.va.sf-cc-yr.01":"Angi utløpsåret","error.va.sf-cc-yr.02":"Angi det fullstendige utløpsåret","error.va.sf-cc-cvc.01":"Angi sikkerhetskoden","error.va.sf-cc-cvc.02":"Angi den fullstendige sikkerhetskoden","error.va.sf-ach-num.01":"Feltet for bankkontonummer er tomt","error.va.sf-ach-num.02":"Bankkontonummeret har feil lengde","error.va.sf-ach-loc.01":"Feltet for bankens routingnummer er tomt","error.va.sf-ach-loc.02":"Bankens routingnummer har feil lengde","error.va.sf-kcp-pwd.01":"Passordfeltet er tomt","error.va.sf-kcp-pwd.02":"Passordet har feil lengde","error.giftcard.no-balance":"Dette gavekortet har en saldo på null","error.giftcard.card-error":"Vi har ikke noe gavekort med dette nummeret i registrene våre","error.giftcard.currency-error":"Gavekort er kun gyldige i den valutaen de ble utstedt i","amazonpay.signout":"Logg ut av Amazon","amazonpay.changePaymentDetails":"Endre betalingsdetaljer","partialPayment.warning":"Velg en annen betalingsmetode for å betale det gjenværende","partialPayment.remainingBalance":"Gjenværende saldo vil være %{amount}","bankTransfer.beneficiary":"Mottaker","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"referanse","bankTransfer.introduction":"Fortsett for å opprette en ny betaling via bankoverføring. Du kan bruke detaljene i det følgende skjermbildet for å fullføre betalingen.","bankTransfer.instructions":"Takk for kjøpet. Vennligst benytt den følgende informasjonen til å fullføre betalingen.","bacs.accountHolderName":"Kontoholders navn","bacs.accountHolderName.invalid":"Ugyldig kontoholdernavn","bacs.accountNumber":"Bankkontonummer","bacs.accountNumber.invalid":"Ugyldig bankkontonummer","bacs.bankLocationId":"Sorteringskode","bacs.bankLocationId.invalid":"Ugyldig sorteringskode","bacs.consent.amount":"Jeg samtykker til at beløpet ovenfor blir trukket fra bankkontoen min.","bacs.consent.account":"Jeg bekrefter at kontoen står i mitt navn, og at jeg er den eneste signataren som kreves for å autorisere direktebelastningen på denne kontoen.",edit:"Endre","bacs.confirm":"Bekreft og betal","bacs.result.introduction":"Last ned instruksjoner for direktebelastning (DDI/ mandat)","download.pdf":"Last ned PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe for kortnummer","creditCard.encryptedCardNumber.aria.label":"Kortnummer","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe for utløpsdato","creditCard.encryptedExpiryDate.aria.label":"Utløpsdato","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe for utløpsmåned","creditCard.encryptedExpiryMonth.aria.label":"Utløpsmåned","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe for utløpsår","creditCard.encryptedExpiryYear.aria.label":"Utløpsår","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe for sikkerhetskode","creditCard.encryptedSecurityCode.aria.label":"Sikkerhetskode","creditCard.encryptedPassword.aria.iframeTitle":"Iframe for passord","creditCard.encryptedPassword.aria.label":"Første 2 sifre av kortpassord","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe for kortnummer","giftcard.encryptedCardNumber.aria.label":"Kortnummer","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe for PIN-kode","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Maksimalt %{amount} per transaksjon er tillatt på dette gavekortet","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe for bankkontonummer","ach.encryptedBankAccountNumber.aria.label":"Kontonummer","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe for bankens routingnummer","ach.encryptedBankLocationId.aria.label":"ABA-dirigeringsnummer","twint.saved":"lagret",orPayWith:"eller betal med",invalidFormatExpects:"Ugyldig format. Forventet format: %{format}","upi.qrCodeWaitingMessage":"Skann QR-koden ved å bruke din foretrukne UPI-app for å fullføre betalingen","upi.vpaWaitingMessage":"Åpne UPI-appen for å bekrefte betalingen","upi.modeSelection":"Hvordan vil du bruke UPI?","upi.completePayment":"Fullfør betalingen","upi.mode.enterUpiId":"Angi UPI-ID","upi.mode.qrCode":"QR-kode","upi.mode.payByAnyUpi":"Betal med hvilken som helst UPI-app","upi.collect.dropdown.label":"Angi UPI-ID","upi.collect.field.label":"Skriv inn UPI-ID/VPA","onlineBanking.termsAndConditions":"Ved å fortsette godtar du %#terms and conditions%#","onlineBankingPL.termsAndConditions":"Ved å fortsette godtar du %#reglene%# og %#informasjonsplikten%# til Przelewy24.","ctp.loading.poweredByCtp":"Drevet av Click to Pay","ctp.loading.intro":"Vi sjekker om du har noen lagrede kort med Click to Pay…","ctp.login.title":"Fortsett til Click to Pay","ctp.login.subtitle":"Skriv inn e-postadressen som er tilknyttet Click to Pay, for å fortsette.","ctp.login.inputLabel":"E-postadresse","ctp.logout.notYou":"Ikke deg?","ctp.logout.notYourCards":"Ikke dine kort?","ctp.logout.notYourCard":"Ikke ditt kort?","ctp.logout.notYourProfile":"Ikke din profil?","ctp.otp.fieldLabel":"Engangskode","ctp.otp.resendCode":"Send kode på nytt","ctp.otp.codeResent":"Kode er sendt på nytt","ctp.otp.title":"Få tilgang til Click to Pay-kortene dine","ctp.otp.subtitle":"Tast inn koden vi sendte til %@, for å verifisere at det er deg.","ctp.otp.saveCookiesCheckbox.label":"Hopp over verifisering neste gang","ctp.otp.saveCookiesCheckbox.information":"Velg for å bli husket på enheten og i nettleseren din i deltakende butikker for raskere betaling. Anbefales ikke for delte enheter.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Velg for å bli husket på enheten og i nettleseren din","ctp.emptyProfile.message":"Ingen kort er registrert i denne Click to Pay-profilen","ctp.separatorText":"eller bruk","ctp.cards.title":"Fullfør betalingen med Click to Pay","ctp.cards.subtitle":"Velg et kort du vil bruke.","ctp.cards.expiredCard":"Utløpt","ctp.manualCardEntry":"Manuell inntasting av kort","ctp.aria.infoModalButton":"Hva er Click to Pay","ctp.infoPopup.title":"Click to Pay gjør kontaktløst mulig også på nettet","ctp.infoPopup.subtitle":"En rask, sikker betalingsmetode støttet av Mastercard, Visa og andre betalingskort.","ctp.infoPopup.benefit1":"Click to Pay bruker kryptering for å holde informasjonen din sikker","ctp.infoPopup.benefit2":"Bruk den hos forhandlere over hele verden","ctp.infoPopup.benefit3":"Konfigurer én gang for lettvinte betalinger i fremtiden","ctp.errors.AUTH_INVALID":"Autentiseringen er ugyldig","ctp.errors.NOT_FOUND":"Vi finner ingen konto. Skriv inn en gyldig e-postadresse eller fortsett ved hjelp av manuell kortinntasting","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formatet støttes ikke","ctp.errors.FRAUD":"Brukerkontoen er låst eller deaktivert","ctp.errors.CONSUMER_ID_MISSING":"Forbrukerens identitet mangler i forespørselen","ctp.errors.ACCT_INACCESSIBLE":"Denne kontoen er for øyeblikket ikke tilgjengelig, f.eks. kan den være låst","ctp.errors.CODE_INVALID":"Feil verifiseringskode","ctp.errors.CODE_EXPIRED":"Denne koden er utløpt","ctp.errors.RETRIES_EXCEEDED":"Grensen for antall forsøk på generering av engangspassord er overskredet","ctp.errors.OTP_SEND_FAILED":"Engangspassordet kunne ikke sendes til mottakeren","ctp.errors.REQUEST_TIMEOUT":"Noe gikk galt, prøv igjen eller bruk den manuelle kortinntastingen","ctp.errors.UNKNOWN_ERROR":"Noe gikk galt, prøv igjen eller bruk den manuelle kortinntastingen","ctp.errors.SERVICE_ERROR":"Noe gikk galt, prøv igjen eller bruk den manuelle kortinntastingen","ctp.errors.SERVER_ERROR":"Noe gikk galt, prøv igjen eller bruk den manuelle kortinntastingen","ctp.errors.INVALID_PARAMETER":"Noe gikk galt, prøv igjen eller bruk den manuelle kortinntastingen","ctp.errors.AUTH_ERROR":"Noe gikk galt, prøv igjen eller bruk den manuelle kortinntastingen","paymentMethodsList.aria.label":"Velg en betalingsmetode","companyDetails.name.invalid":"Skriv inn firmanavnet","companyDetails.registrationNumber.invalid":"Angi registreringsnummeret","consent.checkbox.invalid":"Du må godta vilkårene","form.instruction":"Alle felt er obligatoriske med mindre annet er angitt.","trustly.descriptor":"Øyeblikkelig bankbetaling","trustly.description1":"Betal direkte fra hvilken som helst av bankkontoene dine, med sikkerhet på banknivå","trustly.description2":"Ingen kort, ingen appnedlasting, ingen registrering","ancv.input.label":"ANCV-identifikasjonen din","ancv.confirmPayment":"Bruk ANCV-appen for å bekrefte betalingen.","ancv.form.instruction":"Cheque-Vacances-appen er nødvendig for å validere denne betalingen.","ancv.beneficiaryId.invalid":"Oppgi en gyldig e-postadresse eller ANCV-ID","payme.openPayMeApp":"Fullfør betalingen i PayMe-appen ved å godkjenne betalingen i appen og vente på bekreftelse.","payme.redirectButtonLabel":"Åpne PayMe-appen","payme.scanQrCode":"Fullfør betalingen via QR-kode","payme.timeToPay":"Denne QR-koden er gyldig i %@","payme.instructions.steps":"Åpne PayMe-appen.%@Skann QR-koden for å autorisere betalingen.%@Fullfør betalingen i appen, og vent på bekreftelsen.","payme.instructions.footnote":"Ikke lukk denne siden før betalingen er fullført","payByBankAISDD.disclaimer.header":"Bruk Pay by Bank for å betale umiddelbart fra en hvilken som helst bankkonto.","payByBankAISDD.disclaimer.body":"Ved å koble til bankkontoen din autoriserer du debiteringer av kontoen for ethvert beløp du skylder for din bruk av våre tjenester og/eller kjøp av våre produkter, inntil denne autorisasjonen blir trukket tilbake.","paymentMethodBrand.other":"annet"}},20936:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Zapłać","payButton.redirecting":"Przekierowywanie...","payButton.with":"Zapłać %{value} za pomocą %{maskedData}","payButton.saveDetails":"Zapisz dane",close:"Zamknij",storeDetails:"Zapisz na potrzeby następnej płatności",readMore:"Czytaj więcej","creditCard.holderName":"Imię i nazwisko na karcie","creditCard.holderName.placeholder":"J. Kowalski","creditCard.holderName.invalid":"Wpisz imię i nazwisko w takim formacie, jak na karcie","creditCard.numberField.title":"Numer karty ","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Data ważności","creditCard.expiryDateField.placeholder":"MM/RR","creditCard.expiryDateField.month":"Miesiąc","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"RR","creditCard.expiryDateField.year":"Rok","creditCard.cvcField.title":"Kod zabezpieczający","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Zapamiętaj na przyszłość","creditCard.cvcField.placeholder.4digits":"4 cyfry","creditCard.cvcField.placeholder.3digits":"3 cyfry","creditCard.taxNumber.placeholder":"RRMMDD / 0123456789",installments:"Liczba rat",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} miesięcy","installments.oneTime":"Płatność jednorazowa","installments.installments":"Płatność ratalna","installments.revolving":"Płatność odnawialna","sepaDirectDebit.ibanField.invalid":"Nieprawidłowy numer rachunku","sepaDirectDebit.nameField.placeholder":"J. Kowalski","sepa.ownerName":"Imię i nazwisko posiadacza karty","sepa.ibanNumber":"Numer rachunku (IBAN)","error.title":"Błąd","error.subtitle.redirect":"Przekierowanie nie powiodło się","error.subtitle.payment":"Płatność nie powiodła się","error.subtitle.refused":"Płatność została odrzucona","error.message.unknown":"Wystąpił nieoczekiwany błąd","errorPanel.title":"Istniejące błędy","idealIssuer.selectField.title":"Bank","idealIssuer.selectField.placeholder":"Wybierz swój bank","creditCard.success":"Płatność zakończona sukcesem",loading:"Ładowanie...",continue:"Kontynuuj",continueTo:"Przejdź do","wechatpay.timetopay":"Masz do zapłacenia %@","sr.wechatpay.timetopay":"Masz %#min%# %#s%#, aby zapłacić","wechatpay.scanqrcode":"Zeskanuj kod QR",personalDetails:"Dane osobowe",companyDetails:"Dane firmy","companyDetails.name":"Nazwa firmy","companyDetails.registrationNumber":"Numer w rejestrze",socialSecurityNumber:"Numer dowodu osobistego",firstName:"Imię","firstName.invalid":"Wpisz imię",infix:"Prefiks",lastName:"Nazwisko","lastName.invalid":"Wpisz nazwisko",mobileNumber:"Numer telefonu komórkowego","mobileNumber.invalid":"Nieprawidłowy numer telefonu komórkowego",city:"Miasto",postalCode:"Kod pocztowy","postalCode.optional":"Kod pocztowy (opcjonalnie)",countryCode:"Kod kraju",telephoneNumber:"Numer telefonu",dateOfBirth:"Data urodzenia",shopperEmail:"Adres e-mail",gender:"Płeć","gender.notselected":"Wybierz płeć",male:"Mężczyzna",female:"Kobieta",billingAddress:"Adres rozliczeniowy",street:"Ulica",stateOrProvince:"Województwo",country:"Kraj/Region",houseNumberOrName:"Numer domu i mieszkania",separateDeliveryAddress:"Podaj osobny adres dostawy",deliveryAddress:"Adres dostawy","deliveryAddress.firstName":"Imię odbiorcy","deliveryAddress.lastName":"Nazwisko odbiorcy",zipCode:"Kod pocztowy",apartmentSuite:"Numer domu/mieszkania",provinceOrTerritory:"Region lub terytorium",cityTown:"Miejscowość",address:"Adres","address.placeholder":"Znajdź swój adres","address.errors.incomplete":"Wprowadź adres, aby kontynuować","address.enterManually":"Wprowadź adres ręcznie",state:"Stan","field.title.optional":"(opcjonalnie)","creditCard.cvcField.title.optional":"Kod zabezpieczający (opcjonalnie)","issuerList.wallet.placeholder":"Wybierz swój portfel",privacyPolicy:"Polityka prywatności.","afterPay.agreement":"Zgadzam się z %@ Riverty","riverty.termsAndConditions":"Zgadzam się z ogólnym %#Regulaminem%# metody płatności Riverty. Politykę prywatności Riverty można znaleźć %#tutaj%#.",paymentConditions:"warunki płatności",openApp:"Otwórz aplikację","voucher.readInstructions":"Przeczytaj instrukcje","voucher.introduction":"Dziękujemy za zakup, dokończ płatność przy użyciu tego kuponu.","voucher.expirationDate":"Data ważności","voucher.alternativeReference":"Dodatkowy numer referencyjny","dragonpay.voucher.non.bank.selectField.placeholder":"Wybierz dostawcę","dragonpay.voucher.bank.selectField.placeholder":"Wybierz swój bank","voucher.paymentReferenceLabel":"Nr referencyjny płatności","voucher.surcharge":"Zawiera %@ opłaty dodatkowej","voucher.introduction.doku":"Dziękujemy za zakup. Dokończ płatność przy użyciu poniższych informacji.","voucher.shopperName":"Imię i nazwisko klienta","voucher.merchantName":"Sprzedający","voucher.introduction.econtext":"Dziękujemy za zakup. Dokończ płatność przy użyciu poniższych informacji.","voucher.telephoneNumber":"Numer telefonu","voucher.shopperReference":"Dane referencyjne kupujących","voucher.collectionInstitutionNumber":"Numer instytucji pobierającej opłatę","voucher.econtext.telephoneNumber.invalid":"Numer telefonu musi mieć 10 lub 11 cyfr","boletobancario.btnLabel":"Wygeneruj płatność Boleto","boleto.sendCopyToEmail":"Wyślij kopię na mój e-mail","button.copy":"Kopiuj","button.download":"Pobierz","boleto.socialSecurityNumber.invalid":"Wprowadź prawidłowy numer CPF/CNPJ","creditCard.storedCard.description.ariaLabel":"Zapisana karta kończy się na % @","voucher.entity":"Pozycja",donateButton:"Przekaż darowiznę",notNowButton:"Nie teraz",thanksForYourSupport:"Dziękujemy za wsparcie!","resultMessages.preauthorized":"Zapisano dane",preauthorizeWith:"Autoryzuj wstępnie za pomocą:",confirmPreauthorization:"Potwierdź autoryzację wstępną",confirmPurchase:"Potwierdź zakup",applyGiftcard:"Wykorzystaj",giftcardBalance:"Saldo karty podarunkowej",deductedBalance:"Saldo potrącone","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Pierwsze 2 cyfry hasła karty","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Nieprawidłowe hasło","creditCard.taxNumber":"Data urodzenia posiadacza karty lub firmowy numer rejestracyjny","creditCard.taxNumber.label":"Data urodzenia posiadacza karty (RRMMDD) lub firmowy numer rejestracyjny (10 cyfr)","creditCard.taxNumber.labelAlt":"Firmowy numer rejestracyjny (10 cyfr)","creditCard.taxNumber.invalid":"Nieprawidłowa data urodzenia posiadacza karty lub nieprawidłowy firmowy numer rejestracyjny","storedPaymentMethod.disable.button":"Usuń","storedPaymentMethod.disable.confirmation":"Usuń zapisaną metodę płatności","storedPaymentMethod.disable.confirmButton":"Tak, usuń","storedPaymentMethod.disable.cancelButton":"Anuluj","ach.bankAccount":"Rachunek bankowy","ach.accountHolderNameField.title":"Imię i nazwisko posiadacza rachunku","ach.accountHolderNameField.placeholder":"J. Kowalski","ach.accountHolderNameField.invalid":"Nieprawidłowe imię i nazwisko posiadacza rachunku","ach.accountNumberField.title":"Numer rachunku","ach.accountNumberField.invalid":"Nieprawidłowy numer rachunku","ach.accountLocationField.title":"Kod bankowy ABA Routing Number","ach.accountLocationField.invalid":"Nieprawidłowy kod bankowy ABA Routing Number","ach.savedBankAccount":"Zapisane konto bankowe","ach.savings":"Konto oszczędnościowe","ach.checking":"Rachunek bieżący","select.state":"Wybierz stan","select.stateOrProvince":"Wybierz stan/województwo","select.provinceOrTerritory":"Wybierz region/terytorium","select.country":"Wybierz kraj/region","select.noOptionsFound":"Nie znaleziono opcji","select.filter.placeholder":"Szukaj...","telephoneNumber.invalid":"Nieprawidłowy numer telefonu",qrCodeOrApp:"lub","paypal.processingPayment":"Przetwarzanie płatności...",generateQRCode:"Wygeneruj kod QR","await.waitForConfirmation":"Oczekiwanie na potwierdzenie","mbway.confirmPayment":"Potwierdź płatność w aplikacji MB WAY","shopperEmail.invalid":"Niepoprawny adres email","dateOfBirth.format":"DD/MM/RRRR","dateOfBirth.invalid":"Podaj prawidłową datę urodzenia, świadczącą o przekroczeniu 18 roku życia.","blik.confirmPayment":"Otwórz aplikację bankową, aby potwierdzić płatność.","blik.invalid":"Wpisz 6 cyfr","blik.code":"6-cyfrowy kod","blik.help":"Uzyskaj kod ze swojej aplikacji bankowej.","swish.pendingMessage":"Po zeskanowaniu transakcja może mieć status „Oczekująca” do 10 minut. Próba ponownego dokonania płatności w tym czasie może spowodować wielokrotne naliczenie opłaty.","field.valid":"Pole prawidłowe","field.invalid":"Dane w polu są nieprawidłowe","error.va.gen.01":"Niekompletne dane w polu","error.va.gen.02":"Dane w polu są nieprawidłowe","error.va.sf-cc-num.01":"Wprowadź prawidłowy numer karty","error.va.sf-cc-num.02":"Wprowadź numer karty","error.va.sf-cc-num.03":"Wprowadź obsługiwaną markę karty","error.va.sf-cc-num.04":"Wprowadź pełny numer karty","error.va.sf-cc-dat.01":"Wprowadź prawidłową datę ważności","error.va.sf-cc-dat.02":"Wprowadź prawidłową datę ważności","error.va.sf-cc-dat.03":"Karta kredytowa wkrótce straci ważność","error.va.sf-cc-dat.04":"Wprowadź datę ważności","error.va.sf-cc-dat.05":"Wprowadź pełną datę ważności","error.va.sf-cc-mth.01":"Wprowadź miesiąc daty ważności","error.va.sf-cc-yr.01":"Wprowadź rok daty ważności","error.va.sf-cc-yr.02":"Wprowadź pełny rok daty ważności","error.va.sf-cc-cvc.01":"Wprowadź kod zabezpieczający","error.va.sf-cc-cvc.02":"Wprowadź pełny kod zabezpieczający","error.va.sf-ach-num.01":"Pole Numer rachunku jest puste","error.va.sf-ach-num.02":"Numer rachunku ma nieprawidłową długość","error.va.sf-ach-loc.01":"Pole Numer rozliczeniowy banku jest puste","error.va.sf-ach-loc.02":"Numer rozliczeniowy banku ma nieprawidłową długość","error.va.sf-kcp-pwd.01":"Pole Hasło jest puste","error.va.sf-kcp-pwd.02":"Hasło ma nieprawidłową długość","error.giftcard.no-balance":"Saldo karty podarunkowej jest puste","error.giftcard.card-error":"W naszych rejestrach nie ma karty podarunkowej o tym numerze","error.giftcard.currency-error":"Karty podarunkowe są ważne tylko w walucie, w której zostały wydane","amazonpay.signout":"Wyloguj się z Amazon","amazonpay.changePaymentDetails":"Zmień szczegóły płatności","partialPayment.warning":"Wybierz inną metodę płatności, aby zapłacić pozostałą kwotę","partialPayment.remainingBalance":"Pozostałe saldo wynosi %{kwota}","bankTransfer.beneficiary":"Beneficjent","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Ref. sprze.","bankTransfer.introduction":"Kontynuuj tworzenie nowej płatności przelewem bankowym. Możesz użyć szczegółów na następnym ekranie, aby sfinalizować tę płatność.","bankTransfer.instructions":"Dziękujemy za zakup. Dokończ płatność przy użyciu poniższych informacji.","bacs.accountHolderName":"Imię i nazwisko posiadacza rachunku","bacs.accountHolderName.invalid":"Nieprawidłowe imię i nazwisko posiadacza rachunku","bacs.accountNumber":"Numer rachunku","bacs.accountNumber.invalid":"Nieprawidłowy numer rachunku","bacs.bankLocationId":"Numer rozliczeniowy SORT","bacs.bankLocationId.invalid":"Nieprawidłowy numer rozliczeniowy SORT","bacs.consent.amount":"Wyrażam zgodę na pobranie powyższej kwoty z mojego rachunku bankowego.","bacs.consent.account":"Potwierdzam, że konto jest zarejestrowane na moje nazwisko i jestem jedynym sygnatariuszem wymaganym do autoryzacji polecenia zapłaty na tym koncie.",edit:"Edytuj","bacs.confirm":"Potwierdź i zapłać","bacs.result.introduction":"Pobierz dyspozycję polecenia zapłaty (DDI/upoważnienie)","download.pdf":"Pobierz PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Element Iframe dla numeru karty","creditCard.encryptedCardNumber.aria.label":"Numer karty ","creditCard.encryptedExpiryDate.aria.iframeTitle":"Element Iframe dla daty ważności","creditCard.encryptedExpiryDate.aria.label":"Data ważności","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Element Iframe dla miesiąca daty ważności","creditCard.encryptedExpiryMonth.aria.label":"Data ważności - miesiąc","creditCard.encryptedExpiryYear.aria.iframeTitle":"Element Iframe dla roku daty ważności","creditCard.encryptedExpiryYear.aria.label":"Data ważności - rok","creditCard.encryptedSecurityCode.aria.iframeTitle":"Element Iframe dla kodu zabezpieczającego","creditCard.encryptedSecurityCode.aria.label":"Kod zabezpieczający","creditCard.encryptedPassword.aria.iframeTitle":"Element Iframe dla hasła","creditCard.encryptedPassword.aria.label":"Pierwsze 2 cyfry hasła karty","giftcard.encryptedCardNumber.aria.iframeTitle":"Element Iframe dla numeru karty","giftcard.encryptedCardNumber.aria.label":"Numer karty ","giftcard.encryptedSecurityCode.aria.iframeTitle":"Element Iframe dla kodu PIN","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Maks. dozwolona kwota (%{amount}) na transakcję tą kartą upominkową","ach.encryptedBankAccountNumber.aria.iframeTitle":"Element Iframe dla numeru konta bankowego","ach.encryptedBankAccountNumber.aria.label":"Numer rachunku","ach.encryptedBankLocationId.aria.iframeTitle":"Element Iframe dla numeru rozliczeniowego banku","ach.encryptedBankLocationId.aria.label":"Kod bankowy ABA Routing Number","twint.saved":"zapisano",orPayWith:"lub zapłać",invalidFormatExpects:"Niepoprawny format. Oczekiwany format: %{format}","upi.qrCodeWaitingMessage":"Zeskanuj kod QR za pomocą preferowanej aplikacji UPI, aby dokończyć płatność","upi.vpaWaitingMessage":"Otwórz aplikację UPI, aby potwierdzić płatność","upi.modeSelection":"Jak chcesz korzystać z UPI?","upi.completePayment":"Sfinalizuj płatność","upi.mode.enterUpiId":"Wprowadź identyfikator UPI","upi.mode.qrCode":"Kod QR","upi.mode.payByAnyUpi":"Płać dowolną aplikacją UPI","upi.collect.dropdown.label":"Wprowadź identyfikator UPI","upi.collect.field.label":"Wprowadź identyfikator UPI / VPA","onlineBanking.termsAndConditions":"Kontynuując, zgadzasz się z %#Warunkami świadczenia usług%#.","onlineBankingPL.termsAndConditions":"Kontynuując, akceptujesz %#przepisy%# oraz %#obowiązek informacyjny%# firmy Przelewy24","ctp.loading.poweredByCtp":"Działa w oparciu o usługę Click to Pay","ctp.loading.intro":"Sprawdzamy, czy masz zapisane karty Click to Pay...","ctp.login.title":"Przejdź do Click to Pay","ctp.login.subtitle":"Wprowadź adres e-mail połączony z Click to Pay, aby kontynuować.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"To nie Ty?","ctp.logout.notYourCards":"To nie Twoje karty?","ctp.logout.notYourCard":"To nie Twoja karta?","ctp.logout.notYourProfile":"To nie Twój profil?","ctp.otp.fieldLabel":"Kod jednorazowy","ctp.otp.resendCode":"Ponowne wysłanie kodu","ctp.otp.codeResent":"Wysłano kod ponownie","ctp.otp.title":"Przejdź do kart Click to Pay","ctp.otp.subtitle":"Wprowadź kod %@ wysłany na adres %@, aby potwierdzić swoją tożsamość.","ctp.otp.saveCookiesCheckbox.label":"Następnym razem pomiń weryfikację","ctp.otp.saveCookiesCheckbox.information":"Wybierz opcję zapamiętywania na urządzeniu i w przeglądarce w sklepach uczestniczących w programie, aby przyspieszyć realizację transakcji. Niezalecane dla współużytkowanych urządzeń.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Wybierz, aby zapamiętać dane logowania na Twoim urządzeniu i przeglądarce","ctp.emptyProfile.message":"W tym profilu Click to Pay nie zarejestrowano żadnych kart","ctp.separatorText":"lub przypadków jej użycia","ctp.cards.title":"Dokończ płatność za pomocą funkcji Click to Pay","ctp.cards.subtitle":"Wybierz kartę, której chcesz użyć.","ctp.cards.expiredCard":"Okres ważności minął","ctp.manualCardEntry":"Ręczne wprowadzanie kart","ctp.aria.infoModalButton":"Co to jest Click to Pay","ctp.infoPopup.title":"Click to Pay zapewnia wygodę płatności zbliżeniowych w Internecie","ctp.infoPopup.subtitle":"Szybka i bezpieczna metoda płatności obsługiwana przez karty płatnicze Mastercard, Visa i inne.","ctp.infoPopup.benefit1":"Click to Pay zabezpiecza Twoje informacje za pomocą szyfrowania","ctp.infoPopup.benefit2":"Używaj tej funkcji u sprzedawców na całym świecie","ctp.infoPopup.benefit3":"Skonfiguruj raz i płać bezproblemowo w przyszłości","ctp.errors.AUTH_INVALID":"Nieprawidłowe dane uwierzytelniające","ctp.errors.NOT_FOUND":"Nie znaleziono konta, wprowadź prawidłowy adres e-mail lub kontynuuj, ręcznie wprowadzając kartę","ctp.errors.ID_FORMAT_UNSUPPORTED":"Format nie jest obsługiwany","ctp.errors.FRAUD":"Konto użytkownika zostało zablokowane lub wyłączone","ctp.errors.CONSUMER_ID_MISSING":"We wniosku brakuje danych dotyczących tożsamości konsumenta","ctp.errors.ACCT_INACCESSIBLE":"To konto jest obecnie niedostępne, np. jest zablokowane","ctp.errors.CODE_INVALID":"Nieprawidłowy kod weryfikacyjny","ctp.errors.CODE_EXPIRED":"Kod wygasł","ctp.errors.RETRIES_EXCEEDED":"Przekroczono limit prób generowania hasła jednorazowego","ctp.errors.OTP_SEND_FAILED":"Nie udało się wysłać hasła jednorazowego do odbiorcy","ctp.errors.REQUEST_TIMEOUT":"Coś poszło nie tak, spróbuj ponownie lub skorzystaj z ręcznego wprowadzania karty","ctp.errors.UNKNOWN_ERROR":"Coś poszło nie tak, spróbuj ponownie lub skorzystaj z ręcznego wprowadzania karty","ctp.errors.SERVICE_ERROR":"Coś poszło nie tak, spróbuj ponownie lub skorzystaj z ręcznego wprowadzania karty","ctp.errors.SERVER_ERROR":"Coś poszło nie tak, spróbuj ponownie lub skorzystaj z ręcznego wprowadzania karty","ctp.errors.INVALID_PARAMETER":"Coś poszło nie tak, spróbuj ponownie lub skorzystaj z ręcznego wprowadzania karty","ctp.errors.AUTH_ERROR":"Coś poszło nie tak, spróbuj ponownie lub skorzystaj z ręcznego wprowadzania karty","paymentMethodsList.aria.label":"Wybierz metodę płatności","companyDetails.name.invalid":"Wpisz nazwę firmy","companyDetails.registrationNumber.invalid":"Wprowadź numer w rejestrze","consent.checkbox.invalid":"Musisz zaakceptować Warunki i postanowienia","form.instruction":"Wszystkie pola są wymagane, chyba że zaznaczono inaczej.","trustly.descriptor":"Błyskawiczna płatność bankowa","trustly.description1":"Płać bezpośrednio z dowolnego rachunku bankowego, korzystając z zabezpieczeń na poziomie bankowym","trustly.description2":"Bez kart, bez pobierania aplikacji, bez rejestracji","ancv.input.label":"Twój identyfikator ANCV","ancv.confirmPayment":"Użyj aplikacji ANCV, aby potwierdzić płatność.","ancv.form.instruction":"Do zatwierdzenia tej płatności konieczna jest aplikacja Cheque-Vacances.","ancv.beneficiaryId.invalid":"Wprowadź poprawny adres e-mail lub identyfikator ANCV","payme.openPayMeApp":"Dokończ płatność w aplikacji PayMe, autoryzując płatność w aplikacji, i poczekaj na potwierdzenie.","payme.redirectButtonLabel":"Otwórz aplikację PayMe","payme.scanQrCode":"Dokończ płatność za pomocą kodu QR","payme.timeToPay":"Okres ważności kodu QR: %@","payme.instructions.steps":"Otwórz aplikację PayMe.%@Zeskanuj kod QR, aby autoryzować płatność.%@Dokończ płatność w aplikacji i poczekaj na potwierdzenie.","payme.instructions.footnote":"Nie zamykaj tej strony, dopóki płatność nie zostanie zakończona","payByBankAISDD.disclaimer.header":"Użyj Pay by Bank, aby zapłacić natychmiast z dowolnego konta bankowego.","payByBankAISDD.disclaimer.body":"Podłączając swoje konto bankowe, zezwalasz na obciążanie swojego konta wszelkimi kwotami należnymi za korzystanie z naszych usług i/lub zakup naszych produktów, do czasu odwołania tego upoważnienia.","paymentMethodBrand.other":"inne"}},22824:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"결제","payButton.redirecting":"리디렉션 중...","payButton.with":"%{maskedData}(으)로 %{value} 결제","payButton.saveDetails":"세부 정보 저장",close:"닫기",storeDetails:"다음 결제를 위해 이 수단 저장",readMore:"자세히 보기","creditCard.holderName":"카드상의 이름","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"카드에 표시된 대로 이름을 입력합니다.","creditCard.numberField.title":"카드 번호","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"만료일","creditCard.expiryDateField.placeholder":"MM/YY","creditCard.expiryDateField.month":"월","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"YY","creditCard.expiryDateField.year":"연도","creditCard.cvcField.title":"보안 코드","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"다음을 위해 저장","creditCard.cvcField.placeholder.4digits":"4자리","creditCard.cvcField.placeholder.3digits":"3자리","creditCard.taxNumber.placeholder":"YYMMDD / 0123456789",installments:"할부 개월 수",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times}개월","installments.oneTime":"일시불 결제","installments.installments":"할부 결제","installments.revolving":"리볼빙 결제","sepaDirectDebit.ibanField.invalid":"유효하지 않은 계좌 번호","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"소유자 이름","sepa.ibanNumber":"계좌 번호(IBAN)","error.title":"오류","error.subtitle.redirect":"리디렉션 실패","error.subtitle.payment":"결제 실패","error.subtitle.refused":"결제 거부","error.message.unknown":"알 수 없는 오류 발생","errorPanel.title":"기존 오류","idealIssuer.selectField.title":"은행","idealIssuer.selectField.placeholder":"은행 선택","creditCard.success":"결제 성공",loading:"로드 중…",continue:"계속",continueTo:"다음으로 계속:","wechatpay.timetopay":"남은 결제 시한: %@","sr.wechatpay.timetopay":"%#분%# %#초%# 내로 결제를 완료하세요.","wechatpay.scanqrcode":"QR 코드 스캔",personalDetails:"개인 정보",companyDetails:"회사 정보","companyDetails.name":"회사명","companyDetails.registrationNumber":"등록 번호",socialSecurityNumber:"사회 보장 번호(주민등록번호)",firstName:"이름","firstName.invalid":"이름을 입력하세요.",infix:"명칭",lastName:"성","lastName.invalid":"성을 입력하세요.",mobileNumber:"휴대폰 번호","mobileNumber.invalid":"유효하지 않은 휴대폰 번호",city:"시",postalCode:"우편번호","postalCode.optional":"우편번호(선택 사항)",countryCode:"국가 코드",telephoneNumber:"전화번호",dateOfBirth:"생년월일",shopperEmail:"이메일 주소",gender:"성별","gender.notselected":"성별 선택",male:"남성",female:"여성",billingAddress:"청구지 주소",street:"도로명",stateOrProvince:"주/도",country:"국가/지역",houseNumberOrName:"집 전화번호",separateDeliveryAddress:"배송 주소 별도 지정",deliveryAddress:"배송 주소","deliveryAddress.firstName":"받는 사람 이름","deliveryAddress.lastName":"받는 사람 성",zipCode:"우편번호",apartmentSuite:"아파트/건물",provinceOrTerritory:"도",cityTown:"시/구",address:"주소","address.placeholder":"주소 찾기","address.errors.incomplete":"계속 진행하려면 주소 입력","address.enterManually":"수동으로 주소 입력",state:"주","field.title.optional":"(선택 사항)","creditCard.cvcField.title.optional":"보안 코드(선택 사항)","issuerList.wallet.placeholder":"전자 지갑 선택",privacyPolicy:"개인정보 보호정책","afterPay.agreement":"Riverty의 %@에 동의합니다.","riverty.termsAndConditions":"Riverty 결제 수단에 대한 일반 %#이용 약관%#에 동의합니다. Riverty의 개인정보 보호정책은 %#여기%#에서 확인할 수 있습니다.",paymentConditions:"결제 조건",openApp:"앱 열기","voucher.readInstructions":"안내 읽기","voucher.introduction":"구매해 주셔서 감사합니다. 다음 쿠폰을 사용하여 결제를 완료하십시오.","voucher.expirationDate":"만료일","voucher.alternativeReference":"대체 참조번호","dragonpay.voucher.non.bank.selectField.placeholder":"제공 업체 선택","dragonpay.voucher.bank.selectField.placeholder":"은행 선택","voucher.paymentReferenceLabel":"결제 참조번호","voucher.surcharge":"%@ 할증료 포함","voucher.introduction.doku":"구매해 주셔서 감사합니다. 다음 정보를 이용하여 결제를 완료하십시오.","voucher.shopperName":"구매자 이름","voucher.merchantName":"가맹점","voucher.introduction.econtext":"구매해 주셔서 감사합니다. 다음 정보를 이용하여 결제를 완료하십시오.","voucher.telephoneNumber":"전화번호","voucher.shopperReference":"구매자 참조번호","voucher.collectionInstitutionNumber":"수금 기관 번호","voucher.econtext.telephoneNumber.invalid":"전화번호는 10자리 또는 11자리 숫자여야 합니다","boletobancario.btnLabel":"Boleto 생성","boleto.sendCopyToEmail":"내 이메일로 사본 보내기","button.copy":"복사","button.download":"다운로드","boleto.socialSecurityNumber.invalid":"올바른 CPF/CNPJ 번호를 입력하세요.","creditCard.storedCard.description.ariaLabel":"저장된 카드는 %@ 후 종료됩니다.","voucher.entity":"발급사",donateButton:"기부하기",notNowButton:"다음에 하기",thanksForYourSupport:"도와주셔서 감사합니다!","resultMessages.preauthorized":"세부 정보 저장됨",preauthorizeWith:"사전 승인 방법:",confirmPreauthorization:"사전 승인 확인",confirmPurchase:"구매 확인",applyGiftcard:"기프트 카드로 결제",giftcardBalance:"기프트 카드 잔액",deductedBalance:"공제 잔액","creditCard.pin.title":"비밀번호","creditCard.encryptedPassword.label":"카드 비밀번호 첫 2자리","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"유효하지 않은 번호","creditCard.taxNumber":"카드 소유자 생년월일 또는 법인 등록 번호","creditCard.taxNumber.label":"카드 소유자 생년월일(예: 870130) 또는 법인 등록 번호(10자리)","creditCard.taxNumber.labelAlt":"법인 등록 번호(10자리)","creditCard.taxNumber.invalid":"유효하지 않은 카드 소유자 생년월일 또는 법인 등록 번호","storedPaymentMethod.disable.button":"삭제","storedPaymentMethod.disable.confirmation":"저장된 결제 수단 삭제","storedPaymentMethod.disable.confirmButton":"예, 삭제합니다","storedPaymentMethod.disable.cancelButton":"취소","ach.bankAccount":"은행 계좌","ach.accountHolderNameField.title":"계좌 소유자 이름","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"유효하지 않은 계좌 소유자 이름","ach.accountNumberField.title":"계좌 번호","ach.accountNumberField.invalid":"유효하지 않은 계좌 번호","ach.accountLocationField.title":"ABA 라우팅 번호","ach.accountLocationField.invalid":"유효하지 않은 ABA 라우팅 번호","ach.savedBankAccount":"저장된 은행 계좌","ach.savings":"저축 계좌","ach.checking":"당좌 예금 계좌","select.state":"주 선택","select.stateOrProvince":"주/도 선택","select.provinceOrTerritory":"도 선택","select.country":"국가/지역 선택","select.noOptionsFound":"검색된 옵션 없음","select.filter.placeholder":"검색...","telephoneNumber.invalid":"유효하지 않은 전화번호",qrCodeOrApp:"또는","paypal.processingPayment":"결제 처리 중...",generateQRCode:"QR 코드 생성","await.waitForConfirmation":"확인 대기중","mbway.confirmPayment":"MB WAY 앱에서 결제를 확인하십시오","shopperEmail.invalid":"유효하지 않은 이메일 주소","dateOfBirth.format":"DD(일)/MM(월)/YYYY(연도)","dateOfBirth.invalid":"만 18세 이상임을 나타내는 유효한 생년월일을 입력합니다.","blik.confirmPayment":"뱅킹 앱을 열어서 결제를 확인하세요.","blik.invalid":"6자리 숫자 입력","blik.code":"6자리 코드","blik.help":"뱅킹 앱에서 코드를 가져오세요.","swish.pendingMessage":"스캔 후 최대 10분 동안 상태가 보류될 수 있습니다. 10분 내에 다시 시도할 경우 금액이 여러 번 청구될 수 있습니다.","field.valid":"필드가 유효합니다","field.invalid":"필드가 유효하지 않습니다","error.va.gen.01":"미완료 필드","error.va.gen.02":"필드가 유효하지 않습니다","error.va.sf-cc-num.01":"올바른 카드 번호를 입력하세요.","error.va.sf-cc-num.02":"카드 번호를 입력하세요.","error.va.sf-cc-num.03":"지원되는 카드 브랜드를 하세요.","error.va.sf-cc-num.04":"전체 카드 번호를 입력하세요.","error.va.sf-cc-dat.01":"올바른 만료일을 입력하세요.","error.va.sf-cc-dat.02":"올바른 만료일을 입력하세요.","error.va.sf-cc-dat.03":"신용 카드가 곧 만료됩니다.","error.va.sf-cc-dat.04":"만료일을 입력하세요.","error.va.sf-cc-dat.05":"전체 만료일을 입력하세요.","error.va.sf-cc-mth.01":"만료 월을 입력하세요.","error.va.sf-cc-yr.01":"만료 연도를 입력하세요.","error.va.sf-cc-yr.02":"전체 만료 연도를 입력하세요.","error.va.sf-cc-cvc.01":"보안 코드를 입력하세요.","error.va.sf-cc-cvc.02":"전체 보안 코드를 입력하세요.","error.va.sf-ach-num.01":"계좌 번호 입력란이 비어 있습니다","error.va.sf-ach-num.02":"계좌 번호 길이가 정확하지 않습니다","error.va.sf-ach-loc.01":"은행 라우팅 번호 입력란이 비어 있습니다","error.va.sf-ach-loc.02":"은행 라우팅 번호 길이가 정확하지 않습니다","error.va.sf-kcp-pwd.01":"비밀번호 입력란이 비어 있습니다","error.va.sf-kcp-pwd.02":"비밀번호 길이가 잘못되었습니다","error.giftcard.no-balance":"이 기프트 카드에는 잔액이 없습니다","error.giftcard.card-error":"이 기프트 카드 번호는 당사에 등록되어 있지 않습니다","error.giftcard.currency-error":"기프트 카드는 발급된 통화로만 사용하실 수 있습니다","amazonpay.signout":"Amazon에서 로그아웃","amazonpay.changePaymentDetails":"결제 정보 변경","partialPayment.warning":"나머지 비용 지불에 사용할 다른 결제 수단 선택하기","partialPayment.remainingBalance":"남은 잔액은 %{amount}입니다","bankTransfer.beneficiary":"수령인","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"참조 번호","bankTransfer.introduction":"새 계좌 이체 건을 계속 진행합니다. 다음 화면에 나오는 정보를 이용해 이체를 완료하실 수 있습니다.","bankTransfer.instructions":"구매해 주셔서 감사합니다. 다음 정보를 이용하여 결제를 완료하십시오.","bacs.accountHolderName":"예금주 이름","bacs.accountHolderName.invalid":"예금주 이름이 유효하지 않습니다","bacs.accountNumber":"계좌 번호","bacs.accountNumber.invalid":"계좌 번호가 유효하지 않습니다","bacs.bankLocationId":"은행 식별 코드","bacs.bankLocationId.invalid":"은행 식별 코드가 유효하지 않습니다","bacs.consent.amount":"계좌에서 위의 금액을 이체하는 것에 동의합니다.","bacs.consent.account":"본인이 이 계좌의 예금주이며, 이 계좌에서의 자동 이체를 승인하기 위해 필요한 유일한 서명인입니다.",edit:"수정","bacs.confirm":"확인 및 결제","bacs.result.introduction":"자동 이체 안내(DDI/필수) 다운로드","download.pdf":"PDF 다운로드","creditCard.encryptedCardNumber.aria.iframeTitle":"카드 번호용 Iframe","creditCard.encryptedCardNumber.aria.label":"카드 번호","creditCard.encryptedExpiryDate.aria.iframeTitle":"만료 날짜용 Iframe","creditCard.encryptedExpiryDate.aria.label":"만료일","creditCard.encryptedExpiryMonth.aria.iframeTitle":"만료 월용 Iframe","creditCard.encryptedExpiryMonth.aria.label":"만료 월","creditCard.encryptedExpiryYear.aria.iframeTitle":"만료 연도용 Iframe","creditCard.encryptedExpiryYear.aria.label":"만료 연도","creditCard.encryptedSecurityCode.aria.iframeTitle":"보안 코드용 Iframe","creditCard.encryptedSecurityCode.aria.label":"보안 코드","creditCard.encryptedPassword.aria.iframeTitle":"비밀번호용 Iframe","creditCard.encryptedPassword.aria.label":"카드 비밀번호 첫 2자리","giftcard.encryptedCardNumber.aria.iframeTitle":"카드 번호용 Iframe","giftcard.encryptedCardNumber.aria.label":"카드 번호","giftcard.encryptedSecurityCode.aria.iframeTitle":"핀용 Iframe","giftcard.encryptedSecurityCode.aria.label":"비밀번호",giftcardTransactionLimit:"이 기프트카드로 건당 결제 가능한 최고 액수는 %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"은행 계좌 번호용 Iframe","ach.encryptedBankAccountNumber.aria.label":"계좌 번호","ach.encryptedBankLocationId.aria.iframeTitle":"은행 라우팅 번호용 Iframe","ach.encryptedBankLocationId.aria.label":"ABA 라우팅 번호","twint.saved":"저장됨",orPayWith:"기타 결제 수단:",invalidFormatExpects:"유효하지 않은 형식입니다. 예상 형식: %{format}","upi.qrCodeWaitingMessage":"즐겨 쓰는 UPI 앱으로 QR 코드를 스캔하여 결제하세요","upi.vpaWaitingMessage":"UPI 앱을 열어 결제를 확인하세요","upi.modeSelection":"UPI를 어떻게 사용하시겠습니까?","upi.completePayment":"결제를 완료하세요","upi.mode.enterUpiId":"UPI ID 입력","upi.mode.qrCode":"QR 코드","upi.mode.payByAnyUpi":"아무 UPI 앱으로 결제하기","upi.collect.dropdown.label":"UPI ID 입력","upi.collect.field.label":"UPI ID / VPA 입력","onlineBanking.termsAndConditions":"계속 진행하는 경우 %#이용약관%#에 동의하게 됩니다.","onlineBankingPL.termsAndConditions":"계속 진행함으로써 귀하는 Przelewy24의 %#규정%# 및 %#정보 의무%#에 동의합니다.","ctp.loading.poweredByCtp":"Click to Pay 제공","ctp.loading.intro":"Click to Pay로 저장된 카드가 있는지 확인 중입니다...","ctp.login.title":"Click to Pay 계속 진행","ctp.login.subtitle":"계속하려면 Click to Pay에 연결된 이메일 주소를 입력하세요.","ctp.login.inputLabel":"이메일","ctp.logout.notYou":"본인이 아니신가요?","ctp.logout.notYourCards":"본인의 카드가 아닌가요?","ctp.logout.notYourCard":"본인의 카드가 아닌가요?","ctp.logout.notYourProfile":"본인의 프로필이 아닌가요?","ctp.otp.fieldLabel":"일회성 코드","ctp.otp.resendCode":"코드 재전송","ctp.otp.codeResent":"코드 재전송됨","ctp.otp.title":"Click to Pay 카드에 접근","ctp.otp.subtitle":"%@로 전송된 %@ 코드를 입력하여 본인 인증을 진행합니다.","ctp.otp.saveCookiesCheckbox.label":"다음 번에는 인증 건너뛰기","ctp.otp.saveCookiesCheckbox.information":"더 빠른 결제를 위해 사용자 기기와 참여 매장의 브라우저에서 정보를 기억하도록 선택하세요. 공유 기기에는 권장하지 않습니다.","ctp.otp.saveCookiesCheckbox.shorterInfo":"장치와 브라우저에서 기억되도록 선택하기","ctp.emptyProfile.message":"해당 Click to Pay 프로필에 등록된 카드가 없습니다","ctp.separatorText":"또는 사용","ctp.cards.title":"Click to Pay로 결제 완료","ctp.cards.subtitle":"사용할 카드를 선택하세요.","ctp.cards.expiredCard":"만료됨","ctp.manualCardEntry":"카드 직접 입력","ctp.aria.infoModalButton":"Click to Pay란?","ctp.infoPopup.title":"Click to Pay는 비접촉 결제의 편리함을 온라인으로 가져온 서비스입니다","ctp.infoPopup.subtitle":"Mastercard, Visa, 기타 결제 카드를 지원하는 빠르고 안전한 결제 수단입니다.","ctp.infoPopup.benefit1":"Click to Pay는 암호화를 통해 정보를 안전하게 보호합니다","ctp.infoPopup.benefit2":"전 세계 가맹점에서 사용하세요","ctp.infoPopup.benefit3":"한 번만 설정하면 앞으로의 결제가 간편해집니다","ctp.errors.AUTH_INVALID":"유효하지 않은 인증입니다","ctp.errors.NOT_FOUND":"계정을 찾을 수 없습니다. 유효한 이메일을 입력하거나 카드 정보를 직접 입력하여 진행하세요.","ctp.errors.ID_FORMAT_UNSUPPORTED":"지원되지 않는 형식입니다","ctp.errors.FRAUD":"사용자 계정이 잠겨 있거나 비활성화되었습니다","ctp.errors.CONSUMER_ID_MISSING":"요청에 소비자 ID가 누락되어 있습니다","ctp.errors.ACCT_INACCESSIBLE":"이 계정은 현재 사용할 수 없습니다(예: 잠겨 있음).","ctp.errors.CODE_INVALID":"잘못된 인증 코드입니다","ctp.errors.CODE_EXPIRED":"만료된 코드입니다","ctp.errors.RETRIES_EXCEEDED":"OTP 생성 재시도 횟수 제한을 초과했습니다","ctp.errors.OTP_SEND_FAILED":"OTP를 수신인에게 전송할 수 없습니다","ctp.errors.REQUEST_TIMEOUT":"문제가 발생했습니다. 다시 시도하거나 카드 정보를 직접 입력하세요.","ctp.errors.UNKNOWN_ERROR":"문제가 발생했습니다. 다시 시도하거나 카드 정보를 직접 입력하세요.","ctp.errors.SERVICE_ERROR":"문제가 발생했습니다. 다시 시도하거나 카드 정보를 직접 입력하세요.","ctp.errors.SERVER_ERROR":"문제가 발생했습니다. 다시 시도하거나 카드 정보를 직접 입력하세요.","ctp.errors.INVALID_PARAMETER":"문제가 발생했습니다. 다시 시도하거나 카드 정보를 직접 입력하세요.","ctp.errors.AUTH_ERROR":"문제가 발생했습니다. 다시 시도하거나 카드 정보를 직접 입력하세요.","paymentMethodsList.aria.label":"결제 수단을 선택하세요","companyDetails.name.invalid":"회사 이름을 입력하세요.","companyDetails.registrationNumber.invalid":"등록 번호를 입력하세요.","consent.checkbox.invalid":"이용 약관에 동의해야 합니다.","form.instruction":"별도로 표시되어 있지 않는 한 모든 필드는 필수입니다.","trustly.descriptor":"즉시 은행 결제","trustly.description1":"은행 수준의 보안이 지원되는 은행 계좌에서 직접 결제하세요.","trustly.description2":"카드, 앱 다운로드, 등록 필요 없음","ancv.input.label":"나의 ANCV ID","ancv.confirmPayment":"ANCV 애플리케이션을 사용해 결제를 확인하세요.","ancv.form.instruction":"이 결제를 인증하려면 Cheque-Vacances 애플리케이션이 필요합니다.","ancv.beneficiaryId.invalid":"유효한 이메일 주소나 ANCV ID를 입력하세요.","payme.openPayMeApp":"PayMe 앱에서 결제를 승인하여 결제를 완료하고 확인을 기다립니다.","payme.redirectButtonLabel":"PayMe 앱 열기","payme.scanQrCode":"QR 코드로 결제 완료","payme.timeToPay":"이 QR 코드는 %@ 동안 유효합니다","payme.instructions.steps":"PayMe 앱을 엽니다.%@QR 코드를 스캔하여 결제를 승인합니다.%@앱에서 결제를 완료하고 확인을 기다립니다.","payme.instructions.footnote":"결제가 완료되기 전에 이 페이지를 닫지 마세요.","payByBankAISDD.disclaimer.header":"Pay by Bank를 사용하면 어떤 은행 계좌에서든 즉시 결제할 수 있습니다.","payByBankAISDD.disclaimer.body":"은행 계좌를 연결함으로써, 이 승인이 취소될 때까지 당사의 서비스 이용 및/또는 제품 구매에 대해 지불해야 할 모든 금액이 귀하의 계좌에서 인출되는 것을 승인하게 됩니다.","paymentMethodBrand.other":"기타"}},25404:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Payer","payButton.redirecting":"Redirection...","payButton.with":"Payer %{value} avec %{maskedData}","payButton.saveDetails":"Enregistrer les détails",close:"Fermer",storeDetails:"Sauvegarder pour mon prochain paiement",readMore:"Lire la suite","creditCard.holderName":"Nom sur la carte","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"Entrez le nom tel qu'il apparaît sur la carte","creditCard.numberField.title":"Numéro de la carte","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Date d'expiration","creditCard.expiryDateField.placeholder":"MM/AA","creditCard.expiryDateField.month":"Mois","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"AA","creditCard.expiryDateField.year":"Année","creditCard.cvcField.title":"Code de sécurité","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Enregistrer pour la prochaine fois","creditCard.cvcField.placeholder.4digits":"4 chiffres","creditCard.cvcField.placeholder.3digits":"3 chiffres","creditCard.taxNumber.placeholder":"AAMMJJ / 0123456789",installments:"Nombre de versements",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} mois","installments.oneTime":"Paiement unique","installments.installments":"Paiement échelonné","installments.revolving":"Paiement en plusieurs fois","sepaDirectDebit.ibanField.invalid":"Numéro de compte non valide","sepaDirectDebit.nameField.placeholder":"N. Bernard","sepa.ownerName":"Au nom de","sepa.ibanNumber":"Numéro de compte (IBAN)","error.title":"Erreur","error.subtitle.redirect":"Échec de la redirection","error.subtitle.payment":"Échec du paiement","error.subtitle.refused":"Paiement refusé","error.message.unknown":"Une erreur inconnue s'est produite","errorPanel.title":"Erreurs existantes","idealIssuer.selectField.title":"Banque","idealIssuer.selectField.placeholder":"Sélectionnez votre banque","creditCard.success":"Paiement réussi",loading:"Chargement en cours...",continue:"Continuer",continueTo:"Poursuivre vers","wechatpay.timetopay":"Vous avez %@ pour payer cette somme","sr.wechatpay.timetopay":"Vous disposez de %# minutes%# et %# secondes%# pour effectuer le paiement","wechatpay.scanqrcode":"Scanner le code QR",personalDetails:"Informations personnelles",companyDetails:"Coordonnées de l'entreprise","companyDetails.name":"Nom de l'entreprise","companyDetails.registrationNumber":"Numéro d'enregistrement",socialSecurityNumber:"Numéro de sécurité sociale",firstName:"Prénom","firstName.invalid":"Entrez votre prénom",infix:"Préfixe",lastName:"Nom de famille","lastName.invalid":"Entrez votre nom",mobileNumber:"Numéro de portable","mobileNumber.invalid":"Numéro de portable non valide",city:"Ville",postalCode:"Code postal","postalCode.optional":"Code postal (facultatif)",countryCode:"Code pays",telephoneNumber:"Numéro de téléphone",dateOfBirth:"Date de naissance",shopperEmail:"Adresse e-mail",gender:"Sexe","gender.notselected":"Sélectionnez votre sexe",male:"Homme",female:"Femme",billingAddress:"Adresse de facturation",street:"Rue",stateOrProvince:"État ou province",country:"Pays/Région",houseNumberOrName:"Numéro de rue",separateDeliveryAddress:"Indiquer une adresse de livraison distincte",deliveryAddress:"Adresse de livraison","deliveryAddress.firstName":"Prénom du destinataire","deliveryAddress.lastName":"Nom du destinataire",zipCode:"Code postal",apartmentSuite:"Appartement",provinceOrTerritory:"Province ou territoire",cityTown:"Ville",address:"Adresse","address.placeholder":"Trouvez votre adresse","address.errors.incomplete":"Saisissez une adresse pour continuer","address.enterManually":"Saisissez l'adresse manuellement",state:"État","field.title.optional":"(facultatif)","creditCard.cvcField.title.optional":"Code de sécurité (facultatif)","issuerList.wallet.placeholder":"Sélectionnez votre portefeuille",privacyPolicy:"Politique de confidentialité","afterPay.agreement":"J'accepte les %@ de Riverty","riverty.termsAndConditions":"J'accepte les %#Conditions générales%# du mode de paiement Riverty. La Politique de confidentialité de Riverty peut être consultée %#ici%#.",paymentConditions:"conditions de paiement",openApp:"Ouvrir l'application","voucher.readInstructions":"Lire les instructions","voucher.introduction":"Merci pour votre achat, veuillez utiliser le coupon suivant pour finaliser votre paiement.","voucher.expirationDate":"Date d'expiration","voucher.alternativeReference":"Autre référence","dragonpay.voucher.non.bank.selectField.placeholder":"Sélectionnez votre fournisseur","dragonpay.voucher.bank.selectField.placeholder":"Sélectionnez votre banque","voucher.paymentReferenceLabel":"Référence du paiement","voucher.surcharge":"Comprend une surcharge de %@","voucher.introduction.doku":"Nous vous remercions de votre achat. Veuillez finaliser votre paiement à l'aide des informations suivantes.","voucher.shopperName":"Nom de l'acheteur","voucher.merchantName":"Marchand","voucher.introduction.econtext":"Nous vous remercions de votre achat. Veuillez finaliser votre paiement à l'aide des informations suivantes.","voucher.telephoneNumber":"Numéro de téléphone","voucher.shopperReference":"Référence client","voucher.collectionInstitutionNumber":"Numéro du point de paiement","voucher.econtext.telephoneNumber.invalid":"Le numéro de téléphone doit comporter 10 ou 11 chiffres","boletobancario.btnLabel":"Générer un Boleto","boleto.sendCopyToEmail":"Envoyer une copie à mon adresse e-mail","button.copy":"Copier","button.download":"Télécharger","boleto.socialSecurityNumber.invalid":"Saisissez un numéro CPF/CNPJ valide","creditCard.storedCard.description.ariaLabel":"La carte enregistrée se termine en %@","voucher.entity":"Entité",donateButton:"Faire un don",notNowButton:"Pas maintenant",thanksForYourSupport:"Merci de votre soutien !","resultMessages.preauthorized":"Détails enregistrés",preauthorizeWith:"Pré-autoriser avec",confirmPreauthorization:"Confirmer la pré-autorisation",confirmPurchase:"Confirmer l'achat",applyGiftcard:"Utiliser",giftcardBalance:"Solde de la carte cadeau",deductedBalance:"Solde déduit","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Les deux premiers chiffres du code de votre carte","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Code incorrect","creditCard.taxNumber":"Date de naissance du porteur de carte ou numéro d'identification de l'entreprise","creditCard.taxNumber.label":"Date de naissance du porteur de carte (au format AAMMJJ) ou numéro d'identification de l'entreprise (à 10 chiffres)","creditCard.taxNumber.labelAlt":"Numéro d'identification de l'entreprise (à 10 chiffres)","creditCard.taxNumber.invalid":"Date de naissance du porteur de carte ou numéro d'identification de l'entreprise incorrect(e)","storedPaymentMethod.disable.button":"Supprimer","storedPaymentMethod.disable.confirmation":"Supprimer le mode de paiement enregistré","storedPaymentMethod.disable.confirmButton":"Oui, supprimer","storedPaymentMethod.disable.cancelButton":"Annuler","ach.bankAccount":"Compte bancaire","ach.accountHolderNameField.title":"Nom du titulaire du compte","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Nom du titulaire du compte incorrect","ach.accountNumberField.title":"Numéro du compte","ach.accountNumberField.invalid":"Numéro du compte incorrect","ach.accountLocationField.title":"Code ABA","ach.accountLocationField.invalid":"Code ABA incorrect","ach.savedBankAccount":"Compte bancaire enregistré","ach.savings":"Compte d’épargne","ach.checking":"Compte courant","select.state":"Sélectionnez l'État","select.stateOrProvince":"Sélectionnez l'État ou la province","select.provinceOrTerritory":"Sélectionnez la province ou le territoire","select.country":"Sélectionnez le pays/la région","select.noOptionsFound":"Aucune option trouvée","select.filter.placeholder":"Recherche...","telephoneNumber.invalid":"Numéro de téléphone incorrect",qrCodeOrApp:"ou","paypal.processingPayment":"Traitement du paiement en cours...",generateQRCode:"Générer un code QR","await.waitForConfirmation":"En attente de confirmation","mbway.confirmPayment":"Confirmez votre paiement sur l'application MB WAY","shopperEmail.invalid":"Adresse e-mail incorrecte","dateOfBirth.format":"JJ/MM/AAAA","dateOfBirth.invalid":"Saisissez une date de naissance valide indiquant que vous avez au moins 18 ans","blik.confirmPayment":"Ouvrez votre application bancaire pour confirmer le paiement.","blik.invalid":"Saisissez les 6 chiffres","blik.code":"Code à 6 chiffres","blik.help":"Ouvrez votre application bancaire pour obtenir le code.","swish.pendingMessage":"Après avoir scanné le code QR, la mise à jour du statut de paiement peut prendre jusqu'à 10 minutes. Si vous effectuez une nouvelle tentative de paiement dans ce délai, cela pourrait occasionner plusieurs débits.","field.valid":"Champ valide","field.invalid":"Champ non valide","error.va.gen.01":"Champ incomplet","error.va.gen.02":"Champ non valide","error.va.sf-cc-num.01":"Entrez un numéro de carte valide","error.va.sf-cc-num.02":"Entrez le numéro de la carte","error.va.sf-cc-num.03":"Entrez un émetteur de carte pris en charge","error.va.sf-cc-num.04":"Entrez le numéro de carte complet","error.va.sf-cc-dat.01":"Entrez une date d'expiration valide","error.va.sf-cc-dat.02":"Entrez une date d'expiration valide","error.va.sf-cc-dat.03":"La carte de crédit arrive à expiration","error.va.sf-cc-dat.04":"Entrez la date d'expiration","error.va.sf-cc-dat.05":"Entrez la date d'expiration complète","error.va.sf-cc-mth.01":"Entrez le mois d'expiration","error.va.sf-cc-yr.01":"Entrez l'année d'expiration","error.va.sf-cc-yr.02":"Entrez l'année d'expiration complète","error.va.sf-cc-cvc.01":"Entrez le code de sécurité","error.va.sf-cc-cvc.02":"Entrez le code de sécurité complet","error.va.sf-ach-num.01":"Veuillez renseigner un numéro de compte bancaire","error.va.sf-ach-num.02":"Longueur du numéro de compte bancaire incorrecte","error.va.sf-ach-loc.01":"Veuillez renseigner un numéro de routage","error.va.sf-ach-loc.02":"Longueur du numéro de routage incorrecte","error.va.sf-kcp-pwd.01":"Veuillez renseigner un mot de passe","error.va.sf-kcp-pwd.02":"Longueur du mot de passe incorrecte","error.giftcard.no-balance":"Aucun solde n'est disponible sur cette carte cadeau","error.giftcard.card-error":"Aucune carte cadeau ne correspond à ce numéro","error.giftcard.currency-error":"Les cartes cadeaux sont valables uniquement dans la devise dans laquelle elles ont été émises","amazonpay.signout":"Se déconnecter d'Amazon","amazonpay.changePaymentDetails":"Modifier les informations de paiement","partialPayment.warning":"Sélectionnez un autre mode de paiement pour régler le solde","partialPayment.remainingBalance":"Le solde restant sera de %{amount}","bankTransfer.beneficiary":"Bénéficiaire","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Référence","bankTransfer.introduction":"Continuez à créer un nouveau paiement par virement bancaire. Utilisez les informations de l'écran suivant pour finaliser ce paiement.","bankTransfer.instructions":"Merci pour votre achat ! Veuillez utiliser les informations suivantes pour finaliser votre paiement.","bacs.accountHolderName":"Nom du titulaire du compte bancaire","bacs.accountHolderName.invalid":"Nom du titulaire du compte bancaire incorrect","bacs.accountNumber":"Numéro du compte bancaire","bacs.accountNumber.invalid":"Numéro du compte bancaire incorrect","bacs.bankLocationId":"Code de tri (sort code)","bacs.bankLocationId.invalid":"Code de tri (sort code) non valide","bacs.consent.amount":"J'accepte que le montant ci-dessus soit déduit de mon compte bancaire.","bacs.consent.account":"Je confirme être le titulaire du compte et qu'aucune autre signature que la mienne n'est requise pour autoriser un prélèvement sur ce compte.",edit:"Modifier","bacs.confirm":"Confirmer et payer","bacs.result.introduction":"Téléchargez votre mandat de prélèvement (DDI)","download.pdf":"Télécharger le PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe pour le numéro de carte","creditCard.encryptedCardNumber.aria.label":"Numéro de la carte","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe pour la date d'expiration","creditCard.encryptedExpiryDate.aria.label":"Date d'expiration","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe pour le mois d'expiration","creditCard.encryptedExpiryMonth.aria.label":"Mois d'expiration","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe pour l'année d'expiration","creditCard.encryptedExpiryYear.aria.label":"Année d'expiration","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe pour le code de sécurité","creditCard.encryptedSecurityCode.aria.label":"Code de sécurité","creditCard.encryptedPassword.aria.iframeTitle":"Iframe pour le mot de passe","creditCard.encryptedPassword.aria.label":"Les deux premiers chiffres du code de votre carte","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe pour le numéro de carte","giftcard.encryptedCardNumber.aria.label":"Numéro de la carte","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe pour le code secret","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Montant maximum autorisé par transaction avec cette carte cadeau : %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe pour le numéro de compte bancaire","ach.encryptedBankAccountNumber.aria.label":"Numéro du compte","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe pour le numéro de routage bancaire","ach.encryptedBankLocationId.aria.label":"Code ABA","twint.saved":"stocké",orPayWith:"ou payez avec",invalidFormatExpects:"Format non valide. Format requis : %{format}","upi.qrCodeWaitingMessage":"Scannez le code QR à l'aide de votre application UPI préférée pour effectuer le paiement","upi.vpaWaitingMessage":"Ouvrez votre application UPI pour confirmer le paiement","upi.modeSelection":"Comment souhaitez-vous utiliser UPI ?","upi.completePayment":"Terminez votre paiement","upi.mode.enterUpiId":"Entrez l'identifiant UPI","upi.mode.qrCode":"Code QR","upi.mode.payByAnyUpi":"Payez avec n'importe quelle application UPI","upi.collect.dropdown.label":"Entrez l'identifiant UPI","upi.collect.field.label":"Entrez l'identifiant UPI / VPA","onlineBanking.termsAndConditions":"En continuant, vous acceptez les %#conditions générales%#","onlineBankingPL.termsAndConditions":"En continuant, vous acceptez les %#réglementations%# et l'%#obligation d'information%# de Przelewy24","ctp.loading.poweredByCtp":"Avec la technologie Click to Pay","ctp.loading.intro":"Nous vérifions si vous avez des cartes enregistrées avec Click to Pay...","ctp.login.title":"Accéder à Click to Pay","ctp.login.subtitle":"Saisissez l'adresse e-mail de votre profil Click to Pay pour continuer.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Ce n'est pas votre compte ?","ctp.logout.notYourCards":"Ce ne sont pas vos cartes ?","ctp.logout.notYourCard":"Ce n'est pas votre carte ?","ctp.logout.notYourProfile":"Ce n'est pas votre profil ?","ctp.otp.fieldLabel":"Code à usage unique","ctp.otp.resendCode":"Renvoyer le code","ctp.otp.codeResent":"Code renvoyé","ctp.otp.title":"Accédez à vos cartes Click to Pay","ctp.otp.subtitle":"Saisissez le code %@ envoyé à %@ pour vérifier votre identité.","ctp.otp.saveCookiesCheckbox.label":"Ignorer la vérification la prochaine fois","ctp.otp.saveCookiesCheckbox.information":"Sélectionnez cette option pour mémoriser les informations sur votre appareil et votre navigateur afin d'accélérer le paiement dans les magasins participants. Non recommandé pour les appareils partagés.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Sélectionner cette option pour mémoriser les informations sur votre appareil et votre navigateur","ctp.emptyProfile.message":"Aucune carte enregistrée dans ce profil Click to Pay","ctp.separatorText":"ou utilisez","ctp.cards.title":"Terminez le paiement avec Click to Pay","ctp.cards.subtitle":"Sélectionnez la carte à utiliser.","ctp.cards.expiredCard":"Expiré","ctp.manualCardEntry":"Saisie manuelle de la carte","ctp.aria.infoModalButton":"Qu'est-ce que Click to Pay ?","ctp.infoPopup.title":"Click to Pay offre la simplicité du sans contact sur Internet","ctp.infoPopup.subtitle":"Un mode de paiement rapide et sécurisé compatible avec les cartes Mastercard, Visa et autres fournisseurs.","ctp.infoPopup.benefit1":"Click to Pay utilise le chiffrement pour assurer la sécurité de vos informations","ctp.infoPopup.benefit2":"Utilisez-le pour vos achats dans le monde entier","ctp.infoPopup.benefit3":"Enregistrez votre carte une fois et simplifiez vos paiements pour toujours","ctp.errors.AUTH_INVALID":"Authentification non valide","ctp.errors.NOT_FOUND":"Aucun compte trouvé, vérifiez l'adresse e-mail ou saisissez manuellement les informations de votre carte","ctp.errors.ID_FORMAT_UNSUPPORTED":"Format non pris en charge","ctp.errors.FRAUD":"Le compte utilisateur a été verrouillé ou désactivé","ctp.errors.CONSUMER_ID_MISSING":"L'identité du client n'est pas indiquée dans la demande","ctp.errors.ACCT_INACCESSIBLE":"Ce compte n'est pas disponible actuellement car il est verrouillé","ctp.errors.CODE_INVALID":"Code de vérification incorrect","ctp.errors.CODE_EXPIRED":"Ce code a expiré","ctp.errors.RETRIES_EXCEEDED":"La limite du nombre de tentatives pour la génération de l'OTP a été dépassée","ctp.errors.OTP_SEND_FAILED":"Impossible d'envoyer l'OTP au destinataire","ctp.errors.REQUEST_TIMEOUT":"Une erreur s'est produite, réessayez ou saisissez manuellement les informations de votre carte","ctp.errors.UNKNOWN_ERROR":"Une erreur s'est produite, réessayez ou saisissez manuellement les informations de votre carte","ctp.errors.SERVICE_ERROR":"Une erreur s'est produite, réessayez ou saisissez manuellement les informations de votre carte","ctp.errors.SERVER_ERROR":"Une erreur s'est produite, réessayez ou saisissez manuellement les informations de votre carte","ctp.errors.INVALID_PARAMETER":"Une erreur s'est produite, réessayez ou saisissez manuellement les informations de votre carte","ctp.errors.AUTH_ERROR":"Une erreur s'est produite, réessayez ou saisissez manuellement les informations de votre carte","paymentMethodsList.aria.label":"Choisissez un mode de paiement","companyDetails.name.invalid":"Entrez le nom de l'entreprise","companyDetails.registrationNumber.invalid":"Entrez le numéro d'identification","consent.checkbox.invalid":"Vous devez accepter les conditions générales","form.instruction":"Tous les champs sont obligatoires, sauf indication contraire.","trustly.descriptor":"Paiement bancaire instantané","trustly.description1":"Payez directement à partir de n'importe lequel de vos comptes bancaires, avec une sécurité de niveau bancaire","trustly.description2":"Aucune carte, aucun téléchargement d'application, aucune inscription","ancv.input.label":"Votre identification ANCV","ancv.confirmPayment":"Utilisez votre application ANCV pour confirmer le paiement.","ancv.form.instruction":"L'application Chèque-Vacances est nécessaire pour valider ce paiement.","ancv.beneficiaryId.invalid":"Saisissez une adresse e-mail ou un identifiant ANCV valide","payme.openPayMeApp":"Finalisez votre paiement dans l'application PayMe en autorisant le paiement dans l'application, puis attendez la confirmation.","payme.redirectButtonLabel":"Ouvrir l'application PayMe","payme.scanQrCode":"Effectuez votre paiement avec un code QR","payme.timeToPay":"Ce code QR est valide pendant %@","payme.instructions.steps":"Ouvrez l'application PayMe.%@Scannez le code QR pour autoriser le paiement.%@Effectuez le paiement dans l'application et attendez la confirmation.","payme.instructions.footnote":"Veuillez ne pas fermer cette page avant que le paiement ne soit terminé.","payByBankAISDD.disclaimer.header":"Utilisez Pay by Bank pour payer instantanément depuis n'importe quel compte bancaire.","payByBankAISDD.disclaimer.body":"En connectant votre compte bancaire, vous autorisez le débit de tous les montants dus pour l'utilisation de nos services et l'achat de nos produits, jusqu'à révocation de votre autorisation.","paymentMethodBrand.other":"autre"}},41810:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Paga","payButton.redirecting":"Reindirizzamento...","payButton.with":"Paga %{value} con %{maskedData}","payButton.saveDetails":"Salva dettagli",close:"Chiudi",storeDetails:"Salva per il prossimo pagamento",readMore:"Leggi di più","creditCard.holderName":"Titolare carta","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"Immetti il nome riportato sulla carta","creditCard.numberField.title":"Numero carta","creditCard.expiryDateField.title":"Data di scadenza","creditCard.expiryDateField.placeholder":"MM/AA","creditCard.expiryDateField.month":"Mese","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"AA","creditCard.expiryDateField.year":"Anno","creditCard.cvcField.title":"Codice di sicurezza","creditCard.storeDetailsButton":"Ricorda per la prossima volta","creditCard.cvcField.placeholder.4digits":"4 cifre","creditCard.cvcField.placeholder.3digits":"3 cifre","creditCard.taxNumber.placeholder":"AAMMGG/0123456789",installments:"Numero di rate",installmentOption:"%{partialValue} x%{times}",installmentOptionMonths:"%{times} mesi","installments.oneTime":"Pagamento una tantum","installments.installments":"Pagamento rateale","installments.revolving":"Pagamento ricorrente","sepaDirectDebit.ibanField.invalid":"Numero di conto non valido","sepaDirectDebit.nameField.placeholder":"A. Bianchi","sepa.ownerName":"Nome Intestatario Conto","sepa.ibanNumber":"Numero di conto (IBAN)","error.title":"Errore","error.subtitle.redirect":"Reindirizzamento non riuscito","error.subtitle.payment":"Pagamento non riuscito","error.subtitle.refused":"Pagamento respinto","error.message.unknown":"Si è verificato un errore sconosciuto","errorPanel.title":"Errori presenti","idealIssuer.selectField.title":"Banca","idealIssuer.selectField.placeholder":"Seleziona la banca","creditCard.success":"Pagamento riuscito",loading:"Caricamento in corso...",continue:"Continua",continueTo:"Procedi verso","wechatpay.timetopay":"Devi pagare %@","sr.wechatpay.timetopay":"Hai %#minuti%# %#secondi%# per pagare","wechatpay.scanqrcode":"Scansiona il codice QR",personalDetails:"Dati personali",companyDetails:"Informazioni dell'azienda","companyDetails.name":"Ragione sociale","companyDetails.registrationNumber":"Numero di registrazione",socialSecurityNumber:"Numero di previdenza sociale",firstName:"Nome","firstName.invalid":"Immetti il tuo nome",infix:"Prefisso",lastName:"Cognome","lastName.invalid":"Immetti il tuo cognome",mobileNumber:"Numero di cellulare","mobileNumber.invalid":"Numero di cellulare non valido",city:"Città",postalCode:"Codice postale","postalCode.optional":"Codice postale (facoltativo)",countryCode:"Codice nazionale",telephoneNumber:"Numero di telefono",dateOfBirth:"Data di nascita",shopperEmail:"Indirizzo e-mail",gender:"Sesso","gender.notselected":"Seleziona il tuo sesso",male:"Uomo",female:"Donna",billingAddress:"Indirizzo di fatturazione",street:"Via",stateOrProvince:"Stato o provincia",country:"Paese/Regione",houseNumberOrName:"Numero civico",separateDeliveryAddress:"Specifica un indirizzo di consegna alternativo",deliveryAddress:"Indirizzo di consegna","deliveryAddress.firstName":"Nome del destinatario","deliveryAddress.lastName":"Cognome del destinatario",zipCode:"CAP",apartmentSuite:"Appartamento/suite",provinceOrTerritory:"Provincia o territorio",cityTown:"Città",address:"Indirizzo","address.placeholder":"Trova il tuo indirizzo","address.errors.incomplete":"Inserisci un indirizzo per continuare","address.enterManually":"Inserisci l'indirizzo manualmente",state:"Stato","field.title.optional":"(facoltativo)","creditCard.cvcField.title.optional":"Codice di sicurezza (facoltativo)","issuerList.wallet.placeholder":"Seleziona il tuo portafoglio",privacyPolicy:"Informativa sulla privacy","afterPay.agreement":"Accetto i %@ di Riverty","riverty.termsAndConditions":"Accetto con i %#Termini e le condizioni%# generali per il metodo di Pagamento Riverty. L'informativa sulla privacy di Riverty è disponibile %#qui%#.",paymentConditions:"termini di pagamento",openApp:"Apri l'app","voucher.readInstructions":"Leggi le istruzioni","voucher.introduction":"Grazie per il tuo acquisto, utilizza il seguente coupon per completare il pagamento.","voucher.expirationDate":"Data di scadenza","voucher.alternativeReference":"Riferimento alternativo","dragonpay.voucher.non.bank.selectField.placeholder":"Seleziona il tuo fornitore","dragonpay.voucher.bank.selectField.placeholder":"Seleziona la banca","voucher.paymentReferenceLabel":"Riferimento del pagamento","voucher.surcharge":"Include un sovrapprezzo di %@","voucher.introduction.doku":"Grazie per il tuo acquisto, utilizza i seguenti dati per completare il pagamento.","voucher.shopperName":"Nome dell'acquirente","voucher.merchantName":"Esercente","voucher.introduction.econtext":"Grazie per il tuo acquisto, utilizza i seguenti dati per completare il pagamento.","voucher.telephoneNumber":"Numero di telefono","voucher.shopperReference":"Riferimento dell'acquirente","voucher.collectionInstitutionNumber":"Codice identificativo del negozio","voucher.econtext.telephoneNumber.invalid":"Il numero di telefono deve essere di 10 o 11 cifre","boletobancario.btnLabel":"Genera Boleto","boleto.sendCopyToEmail":"Invia una copia alla mia e-mail","button.copy":"Copia","button.download":"Scarica","boleto.socialSecurityNumber.invalid":"Immetti un numero CPF/CNPJ valido","creditCard.storedCard.description.ariaLabel":"La carta memorizzata termina in %@","voucher.entity":"Entità",donateButton:"Dona",notNowButton:"Non ora",thanksForYourSupport:"Grazie per il tuo sostegno!","resultMessages.preauthorized":"Dettagli salvati",preauthorizeWith:"Preautorizza con",confirmPreauthorization:"Conferma preautorizzazione",confirmPurchase:"Conferma acquisto",applyGiftcard:"Riscatta",giftcardBalance:"Saldo del buono",deductedBalance:"Importo detratto","creditCard.pin.title":"Pin","creditCard.encryptedPassword.label":"Prime 2 cifre della password della carta","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Password non valida","creditCard.taxNumber":"Data di nascita del titolare della carta o numero di registrazione aziendale","creditCard.taxNumber.label":"Data di nascita del titolare della carta (AAMMGG) o numero di registrazione aziendale (10 cifre)","creditCard.taxNumber.labelAlt":"Numero di registrazione aziendale (10 cifre)","creditCard.taxNumber.invalid":"Data di nascita del titolare della carta o numero di registrazione aziendale non validi","storedPaymentMethod.disable.button":"Rimuovi","storedPaymentMethod.disable.confirmation":"Rimuovi il metodo di pagamento archiviato","storedPaymentMethod.disable.confirmButton":"Sì, rimuoverli","storedPaymentMethod.disable.cancelButton":"Cancella","ach.bankAccount":"Conto bancario","ach.accountHolderNameField.title":"Nome del titolare del conto","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Nome del titolare del conto non valido","ach.accountNumberField.title":"Numero di conto","ach.accountNumberField.invalid":"Numero di conto non valido","ach.accountLocationField.title":"Codice ABA","ach.accountLocationField.invalid":"Codice ABA non valido","ach.savedBankAccount":"Conto corrente salvato","ach.savings":"Conto di risparmio","ach.checking":"Conto corrente","select.state":"Seleziona stato","select.stateOrProvince":"Seleziona stato o provincia","select.provinceOrTerritory":"Seleziona provincia o territorio","select.country":"Seleziona paese/regione","select.noOptionsFound":"Nessuna opzione trovata","select.filter.placeholder":"Cerca...","telephoneNumber.invalid":"Numero di telefono non valido",qrCodeOrApp:"o","paypal.processingPayment":"Elaborazione del pagamento in corso...",generateQRCode:"Genera codice QR","await.waitForConfirmation":"In attesa di conferma","mbway.confirmPayment":"Conferma il pagamento con l'app MB WAY","shopperEmail.invalid":"Indirizzo e-mail non valido","dateOfBirth.format":"GG/MM/AAAA","dateOfBirth.invalid":"Inserisci una data di nascita valida in cui risulti che hai almeno 18 anni.","blik.confirmPayment":"Apri l'app della tua banca per confermare il pagamento.","blik.invalid":"Inserisci 6 numeri","blik.code":"Codice a 6 cifre","blik.help":"Ottieni il codice dalla app della tua banca.","swish.pendingMessage":"In seguito alla scansione, lo stato della transazione può rimanere in sospeso per un massimo di 10 minuti. Un nuovo tentativo di pagamento durante questo lasso di tempo può comportare pagamenti multipli.","field.valid":"Campo valido","field.invalid":"Campo non valido","error.va.gen.01":"Campo incompleto","error.va.gen.02":"Campo non valido","error.va.sf-cc-num.01":"Immetti un numero di carta valido","error.va.sf-cc-num.02":"Immetti il numero della carta","error.va.sf-cc-num.03":"Immetti un marchio di carta supportato","error.va.sf-cc-num.04":"Immetti il numero completo della carta","error.va.sf-cc-dat.01":"Immetti una data di scadenza valida","error.va.sf-cc-dat.02":"Immetti una data di scadenza valida","error.va.sf-cc-dat.03":"Carta di credito in scadenza","error.va.sf-cc-dat.04":"Immetti la data di scadenza","error.va.sf-cc-dat.05":"Immetti la data di scadenza completa","error.va.sf-cc-mth.01":"Immetti il mese di scadenza","error.va.sf-cc-yr.01":"Immetti l'anno di scadenza","error.va.sf-cc-yr.02":"Immetti l'anno di scadenza completo","error.va.sf-cc-cvc.01":"Immetti il codice di sicurezza","error.va.sf-cc-cvc.02":"Immetti il codice di sicurezza completo","error.va.sf-ach-num.01":"Il campo Numero di conto corrente bancario è vuoto","error.va.sf-ach-num.02":"La lunghezza del numero di conto corrente bancario non è corretta","error.va.sf-ach-loc.01":"Il campo Numero di routing bancario è vuoto","error.va.sf-ach-loc.02":"La lunghezza del numero di routing bancario non è corretta","error.va.sf-kcp-pwd.01":"Il campo Password è vuoto","error.va.sf-kcp-pwd.02":"La lunghezza della password non è corretta","error.giftcard.no-balance":"Questo buono regalo ha un saldo pari a zero","error.giftcard.card-error":"Non abbiamo alcun buono regalo con questo numero nei nostri archivi","error.giftcard.currency-error":"I buono regalo sono validi solo nella valuta in cui sono state emessi","amazonpay.signout":"Esci da Amazon","amazonpay.changePaymentDetails":"Modifica i dettagli di pagamento","partialPayment.warning":"Seleziona un altro metodo di pagamento per pagare l'importo rimanente","partialPayment.remainingBalance":"Il saldo rimanente sarà di %{amount}","bankTransfer.beneficiary":"Beneficiario","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Riferimento","bankTransfer.introduction":"Continua per creare un nuovo pagamento tramite bonifico bancario. Puoi utilizzare i dettagli nella schermata seguente per completare l'operazione.","bankTransfer.instructions":"Grazie per il tuo acquisto, utilizza i seguenti dati per completare il pagamento.","bacs.accountHolderName":"Nome del titolare del conto bancario","bacs.accountHolderName.invalid":"Nome del titolare del conto bancario non valido","bacs.accountNumber":"Numero di conto bancario","bacs.accountNumber.invalid":"Numero di conto bancario non valido","bacs.bankLocationId":"Sort code","bacs.bankLocationId.invalid":"Sort code non valido","bacs.consent.amount":"Accetto che l'importo sopra indicato venga detratto dal mio conto bancario.","bacs.consent.account":"Confermo che il conto è intestato al sottoscritto e che sono l'unico firmatario a dover autorizzare l'addebito diretto su questo conto.",edit:"Modifica","bacs.confirm":"Conferma e paga","bacs.result.introduction":"Scarica le Istruzioni per l'addebito diretto (DDI / Mandato)","download.pdf":"Scarica PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe per il numero di carta","creditCard.encryptedCardNumber.aria.label":"Numero carta","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe per data di scadenza","creditCard.encryptedExpiryDate.aria.label":"Data di scadenza","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe per il mese di scadenza","creditCard.encryptedExpiryMonth.aria.label":"Mese di scadenza","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe per l'anno di scadenza","creditCard.encryptedExpiryYear.aria.label":"Anno di scadenza","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe per il codice di sicurezza","creditCard.encryptedSecurityCode.aria.label":"Codice di sicurezza","creditCard.encryptedPassword.aria.iframeTitle":"Iframe per la password","creditCard.encryptedPassword.aria.label":"Prime 2 cifre della password della carta","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe per il numero di carta","giftcard.encryptedCardNumber.aria.label":"Numero carta","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe per il pin","giftcard.encryptedSecurityCode.aria.label":"Pin",giftcardTransactionLimit:"Importo massimo di %{amount} per transazione su questo buono regalo","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe per il numero di conto bancario","ach.encryptedBankAccountNumber.aria.label":"Numero di conto","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe per il numero di routing bancario","ach.encryptedBankLocationId.aria.label":"Codice ABA","twint.saved":"memorizzato",orPayWith:"oppure paga con",invalidFormatExpects:"Formato non valido. Formato previsto: %{format}","upi.qrCodeWaitingMessage":"Per completare il pagamento, scansiona il codice QR utilizzando la tua app UPI preferita","upi.vpaWaitingMessage":"Apri l'app UPI per confermare il pagamento","upi.modeSelection":"Come vuoi utilizzare UPI?","upi.completePayment":"Completa il pagamento","upi.mode.enterUpiId":"Immetti l'ID UPI","upi.mode.qrCode":"Codice QR","upi.mode.payByAnyUpi":"Paga con qualsiasi app UPI","upi.collect.dropdown.label":"Immetti l'ID UPI","upi.collect.field.label":"Immetti l'ID UPI / VPA","onlineBanking.termsAndConditions":"Facendo clic, accetti i %#termini e le condizioni%#","onlineBankingPL.termsAndConditions":"Continuando accetti il %#regolamento%# e l'%#obbligo di informativa%# di Przelewy24","ctp.loading.poweredByCtp":"Realizzato da Click to Pay","ctp.loading.intro":"Stiamo verificando se hai delle carte salvate con Click to Pay...","ctp.login.title":"Continua su Click to Pay","ctp.login.subtitle":"Per continuare, inserisci l'indirizzo e-mail collegato a Click to Pay.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Non sei tu?","ctp.logout.notYourCards":"Non sono le tue carte?","ctp.logout.notYourCard":"Non è la tua carta?","ctp.logout.notYourProfile":"Non è il tuo profilo?","ctp.otp.fieldLabel":"Codice monouso","ctp.otp.resendCode":"Invia nuovamente il codice","ctp.otp.codeResent":"Codice inviato","ctp.otp.title":"Accedi alle tue carte Click to Pay","ctp.otp.subtitle":"Inserisci il codice che %@ ha inviato a %@ per verificare la tua identità.","ctp.otp.saveCookiesCheckbox.label":"Salta la verifica la prossima volta","ctp.otp.saveCookiesCheckbox.information":"Scegli di essere ricordato sul dispositivo e il browser che utilizzi per velocizzare il pagamento nei negozi partecipanti. Non consigliato per dispositivi condivisi.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Seleziona per essere ricordato sul tuo dispositivo e sul browser","ctp.emptyProfile.message":"Non esistono carte registrate in questo profilo Click to Pay","ctp.separatorText":"o utilizza","ctp.cards.title":"Completa il pagamento con Click to Pay","ctp.cards.subtitle":"Seleziona una carta da utilizzare.","ctp.cards.expiredCard":"Scaduta","ctp.manualCardEntry":"Inserimento manuale della carta","ctp.aria.infoModalButton":"Che cos'è Click to Pay","ctp.infoPopup.title":"Click to Pay offre la facilità del contactless online","ctp.infoPopup.subtitle":"Un metodo di pagamento veloce e sicuro supportato da Mastercard, Visa e altre carte di pagamento.","ctp.infoPopup.benefit1":"Click to Pay si avvale della crittografia per garantire la sicurezza delle tue informazioni","ctp.infoPopup.benefit2":"Usa questo strumento con i commercianti di tutto il mondo","ctp.infoPopup.benefit3":"Effettua una sola configurazione per avere la possibilità di effettuare pagamenti più agevoli in futuro","ctp.errors.AUTH_INVALID":"Autenticazione non valida","ctp.errors.NOT_FOUND":"Nessun account trovato. Inserisci un'e-mail valida o procedi con l'inserimento manuale della carta.","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formato non supportato","ctp.errors.FRAUD":"L'account utente è stato bloccato o disattivato","ctp.errors.CONSUMER_ID_MISSING":"L'identità del consumatore non è presente nella richiesta","ctp.errors.ACCT_INACCESSIBLE":"Questo account non è attualmente disponibile, ad esempio perché è bloccato","ctp.errors.CODE_INVALID":"Codice di verifica non corretto","ctp.errors.CODE_EXPIRED":"Questo codice è scaduto","ctp.errors.RETRIES_EXCEEDED":"È stato superato il limite di tentativi per la generazione del codice OTP","ctp.errors.OTP_SEND_FAILED":"Impossibile inviare il codice OTP al destinatario","ctp.errors.REQUEST_TIMEOUT":"Si è verificato un errore. Riprova o inserisci manualmente la carta","ctp.errors.UNKNOWN_ERROR":"Si è verificato un errore. Riprova o inserisci manualmente la carta","ctp.errors.SERVICE_ERROR":"Si è verificato un errore. Riprova o inserisci manualmente la carta","ctp.errors.SERVER_ERROR":"Si è verificato un errore. Riprova o inserisci manualmente la carta","ctp.errors.INVALID_PARAMETER":"Si è verificato un errore. Riprova o inserisci manualmente la carta","ctp.errors.AUTH_ERROR":"Si è verificato un errore. Riprova o inserisci manualmente la carta","paymentMethodsList.aria.label":"Scegli un metodo di pagamento","companyDetails.name.invalid":"Immetti il nome dell'azienda","companyDetails.registrationNumber.invalid":"Immetti il numero di registrazione","consent.checkbox.invalid":"È necessario accettare i termini e le condizioni","form.instruction":"Se non diversamente indicato, tutti i campi sono obbligatori.","trustly.descriptor":"Pagamento bancario istantaneo","trustly.description1":"Paga direttamente da uno qualsiasi dei tuoi conti bancari, garantito da una sicurezza di livello bancario","trustly.description2":"Nessuna carta, nessun download di app, nessuna registrazione","ancv.input.label":"Il tuo identificativo ANCV","ancv.confirmPayment":"Utilizza la tua richiesta ANCV per confermare il pagamento.","ancv.form.instruction":"Per confermare il pagamento è necessario utilizzare la richiesta Cheque-Vacances.","ancv.beneficiaryId.invalid":"Inserisci un indirizzo e-mail o un identificativo ANCV valido","payme.openPayMeApp":"Completa il pagamento nell'app PayMe concedendo l'autorizzazione e attendendo il messaggio di conferma.","payme.redirectButtonLabel":"Apri l'app PayMe","payme.scanQrCode":"Completa il pagamento con il codice QR","payme.timeToPay":"Questo codice QR è valido per %@","payme.instructions.steps":"Apri l'app PayMe.%@Scansiona il codice QR per autorizzare il pagamento.%@Completa il pagamento nell'app e attendi la conferma.","payme.instructions.footnote":"Rimani su questa pagina fino a quando il pagamento sarà completato","payByBankAISDD.disclaimer.header":"Utilizza Pay by Bank per pagare istantaneamente da qualsiasi conto bancario.","payByBankAISDD.disclaimer.body":"Collegando il tuo conto bancario, autorizzi l'addebito su di esso di qualsiasi importo dovuto per l'utilizzo dei nostri servizi e/o l'acquisto dei nostri prodotti, fino alla revoca di tale autorizzazione.","paymentMethodBrand.other":"altro"}},53329:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"支払う","payButton.redirecting":"リダイレクトしています...","payButton.with":"%{value}を%{maskedData}で支払う","payButton.saveDetails":"詳細を保存",close:"終了",storeDetails:"次回のお支払いのため詳細を保存",readMore:"詳細を確認","creditCard.holderName":"カード上の名前","creditCard.holderName.placeholder":"Taro Yamada","creditCard.holderName.invalid":"カードに記載されている名前を入力してください","creditCard.numberField.title":"カード番号","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"有効期限","creditCard.expiryDateField.placeholder":"MM/YY","creditCard.expiryDateField.month":"月","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"YY","creditCard.expiryDateField.year":"年","creditCard.cvcField.title":"セキュリティコード","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"次回のために保存します","creditCard.cvcField.placeholder.4digits":"4桁","creditCard.cvcField.placeholder.3digits":"3桁","creditCard.taxNumber.placeholder":"年月日（YYMMDD）/ 0123456789",installments:"分割回数",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times}か月","installments.oneTime":"一括払い","installments.installments":"分割払い","installments.revolving":"リボ払い","sepaDirectDebit.ibanField.invalid":"口座番号の入力間違い","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"名義","sepa.ibanNumber":"口座番号 (IBAN)","error.title":"エラー","error.subtitle.redirect":"画面の切り替え失敗にしました","error.subtitle.payment":"支払できませんでした","error.subtitle.refused":"カード会社で取引が承認されませんでした。","error.message.unknown":"不明なエラーが発生しました","errorPanel.title":"既存のエラー","idealIssuer.selectField.title":"銀行","idealIssuer.selectField.placeholder":"銀行を選択してください","creditCard.success":"認証が成功しました",loading:"読み込んでいます...",continue:"続ける",continueTo:"次へ進む：","wechatpay.timetopay":"%@をお支払い下さい。","sr.wechatpay.timetopay":"支払いまで残り%#分%# %#秒%#","wechatpay.scanqrcode":"QR コードをスキャンする",personalDetails:"個人情報",companyDetails:"会社情報","companyDetails.name":"会社名","companyDetails.registrationNumber":"登録番号",socialSecurityNumber:"ソーシャルセキュリティー番号",firstName:"名","firstName.invalid":"名を入力してください",infix:"敬称",lastName:"姓","lastName.invalid":"姓を入力してください",mobileNumber:"携帯番号","mobileNumber.invalid":"無効な携帯電話番号",city:"市区",postalCode:"郵便番号","postalCode.optional":"郵便番号 (任意)",countryCode:"国コード",telephoneNumber:"電話番号",dateOfBirth:"生年月日",shopperEmail:"Eメールアドレス",gender:"性別","gender.notselected":"性別を選択してください",male:"男性",female:"女性",billingAddress:"ご請求住所",street:"番地",stateOrProvince:"都道府県",country:"国/地域",houseNumberOrName:"部屋番号",separateDeliveryAddress:"別の配送先住所を指定してください",deliveryAddress:"配送先住所","deliveryAddress.firstName":"受信者の名","deliveryAddress.lastName":"受信者の姓",zipCode:"郵便番号",apartmentSuite:"アパート名/部屋名",provinceOrTerritory:"州または準州",cityTown:"市区町村",address:"住所","address.placeholder":"住所を検索してください","address.errors.incomplete":"続行するには、住所を入力してください","address.enterManually":"住所を手動で入力してください",state:"都道府県","field.title.optional":"（任意）","creditCard.cvcField.title.optional":"セキュリティコード（任意）","issuerList.wallet.placeholder":"ウォレットを選択してください",privacyPolicy:"プライバシーポリシー","afterPay.agreement":"Rivertyの%@で同意","riverty.termsAndConditions":"Rivertyの支払方法の一般的な%#利用規約%#に同意します。Rivertyのプライバシーポリシーについては、%#こちら%#をご覧ください。",paymentConditions:"支払条件",openApp:"アプリを開く","voucher.readInstructions":"手順を参照してください","voucher.introduction":"お買い上げありがとうございます。以下のクーポンを使用して、お支払いを完了してください。","voucher.expirationDate":"有効期限","voucher.alternativeReference":"別の参照","dragonpay.voucher.non.bank.selectField.placeholder":"プロバイダーを選択してください","dragonpay.voucher.bank.selectField.placeholder":"銀行を選択してください","voucher.paymentReferenceLabel":"確認番号","voucher.surcharge":"%@ の追加料金を含む","voucher.introduction.doku":"お買い上げありがとうございます。以下の情報を使用して、お支払いを完了してください。","voucher.shopperName":"購入者氏名","voucher.merchantName":"業者","voucher.introduction.econtext":"お買い上げありがとうございます。以下の情報を使用して、お支払いを完了してください。","voucher.telephoneNumber":"電話番号（お客様番号）","voucher.shopperReference":"購入者向け参考情報","voucher.collectionInstitutionNumber":"収納機関番号","voucher.econtext.telephoneNumber.invalid":"電話番号は10桁または11桁にしてください","boletobancario.btnLabel":"Boletoを生成する","boleto.sendCopyToEmail":"自分のメールアドレスにコピーを送信する","button.copy":"コピー","button.download":"ダウンロード","boleto.socialSecurityNumber.invalid":"有効なCPF/CNPJ番号を入力してください","creditCard.storedCard.description.ariaLabel":"保存されたカードは％@に終了します","voucher.entity":"エンティティ",donateButton:"寄付する",notNowButton:"今はしない",thanksForYourSupport:"ご支援いただきありがとうございます。","resultMessages.preauthorized":"詳細が保存されました",preauthorizeWith:"次で事前認証する：",confirmPreauthorization:"事前承認を確認する",confirmPurchase:"購入を確認する",applyGiftcard:"利用する",giftcardBalance:"ギフトカードの残高",deductedBalance:"差し引き後残高","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"カードのパスワードの最初の2桁","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"パスワードが無効です","creditCard.taxNumber":"カード所有者の生年月日または法人登録番号","creditCard.taxNumber.label":"カード所有者の生年月日（YYMMDD）または法人登録番号（10桁）","creditCard.taxNumber.labelAlt":"法人登録番号（10桁）","creditCard.taxNumber.invalid":"カード所有者の生年月日または法人登録番号が無効です","storedPaymentMethod.disable.button":"削除","storedPaymentMethod.disable.confirmation":"保存されている支払方法を削除する","storedPaymentMethod.disable.confirmButton":"はい、削除します","storedPaymentMethod.disable.cancelButton":"キャンセル","ach.bankAccount":"銀行口座","ach.accountHolderNameField.title":"口座名義","ach.accountHolderNameField.placeholder":"Taro Yamada","ach.accountHolderNameField.invalid":"無効な口座名義","ach.accountNumberField.title":"口座番号","ach.accountNumberField.invalid":"口座番号の入力間違い","ach.accountLocationField.title":"ABAナンバー","ach.accountLocationField.invalid":"無効なABAナンバー","ach.savedBankAccount":"保存済みの銀行口座","ach.savings":"普通預金口座","ach.checking":"当座預金口座","select.state":"都道府県を選択してください","select.stateOrProvince":"都道府県を選択してください","select.provinceOrTerritory":"州または準州を選択してください","select.country":"国/地域を選択してください","select.noOptionsFound":"オプションが見つかりませんでした","select.filter.placeholder":"検索...","telephoneNumber.invalid":"無効な電話番号",qrCodeOrApp:"または","paypal.processingPayment":"支払いを処理しています...",generateQRCode:"QRコードを生成する","await.waitForConfirmation":"確認を待っています","mbway.confirmPayment":"MB WAYアプリで支払を確認する","shopperEmail.invalid":"Eメールアドレスが無効です","dateOfBirth.format":"DD/MM/YYYY","dateOfBirth.invalid":"18歳以上であることを示す有効な生年月日を入力してください","blik.confirmPayment":"バンキングアプリを開いて、支払を確認してください。","blik.invalid":"6つの数字を入力してください","blik.code":"6桁のコード","blik.help":"バンキングアプリからコードを取得してください。","swish.pendingMessage":"スキャン後、ステータスは最大10分間保留状態になります。この間に再度お支払いいただこうとすると、複数の請求が発生する場合があります。","field.valid":"フィールドが有効です","field.invalid":"フィールドが無効です","error.va.gen.01":"不完全なフィールド","error.va.gen.02":"フィールドが無効です","error.va.sf-cc-num.01":"有効なカード番号を入力してください","error.va.sf-cc-num.02":"カード番号を入力してください","error.va.sf-cc-num.03":"サポートされているカードブランドを入力してください","error.va.sf-cc-num.04":"完全なカード番号を入力してください","error.va.sf-cc-dat.01":"有効な有効期限を入力してください","error.va.sf-cc-dat.02":"有効な有効期限を入力してください","error.va.sf-cc-dat.03":"クレジットカードの有効期限が近づいています","error.va.sf-cc-dat.04":"有効期限を入力してください","error.va.sf-cc-dat.05":"完全な有効期限を入力してください","error.va.sf-cc-mth.01":"有効期限月を入力してください","error.va.sf-cc-yr.01":"有効期限年を入力してください","error.va.sf-cc-yr.02":"完全な有効期限年を入力してください","error.va.sf-cc-cvc.01":"セキュリティコードを入力してください","error.va.sf-cc-cvc.02":"完全なセキュリティコードを入力してください","error.va.sf-ach-num.01":"銀行口座番号フィールドが空です","error.va.sf-ach-num.02":"銀行口座番号の長さが不正です","error.va.sf-ach-loc.01":"銀行支店番号フィールドが空です","error.va.sf-ach-loc.02":"銀行支店番号の長さが不正です","error.va.sf-kcp-pwd.01":"パスワードフィールドが空です","error.va.sf-kcp-pwd.02":"パスワードの長さが不正です","error.giftcard.no-balance":"このギフトカードの残高はゼロです","error.giftcard.card-error":"記録では、この番号のギフトカードはありません","error.giftcard.currency-error":"ギフトカードは、発行された通貨でのみ有効です。","amazonpay.signout":"Amazonからサインアウトする","amazonpay.changePaymentDetails":"支払明細を変更する","partialPayment.warning":"残金を支払う別の支払方法を選択してください","partialPayment.remainingBalance":"残りの残高は%{amount}になります","bankTransfer.beneficiary":"受益者","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"参照","bankTransfer.introduction":"新しい銀行振込によるお支払の作成を続行します。次の画面の詳細を使用して、このお支払いを確定できます。","bankTransfer.instructions":"お買い上げありがとうございます。以下の情報を使用して、お支払いを完了してください。","bacs.accountHolderName":"銀行口座名義","bacs.accountHolderName.invalid":"銀行口座名義が無効です","bacs.accountNumber":"銀行口座番号","bacs.accountNumber.invalid":"銀行口座番号が無効です","bacs.bankLocationId":"ソートコード","bacs.bankLocationId.invalid":"ソートコードが無効です","bacs.consent.amount":"上記の金額が私の銀行口座から引き落とされることに同意します。","bacs.consent.account":"口座が私の名義であることを確認し、この口座からの自動引き落としを承認するために必要な唯一の署名者であることを確認します。",edit:"編集","bacs.confirm":"確認して支払う","bacs.result.introduction":"自動引き落としの説明 (DDI/委任状) をダウンロードする","download.pdf":"PDFをダウンロード","creditCard.encryptedCardNumber.aria.iframeTitle":"カード番号のiframe","creditCard.encryptedCardNumber.aria.label":"カード番号","creditCard.encryptedExpiryDate.aria.iframeTitle":"有効期限のiframe","creditCard.encryptedExpiryDate.aria.label":"有効期限","creditCard.encryptedExpiryMonth.aria.iframeTitle":"有効期限月のiframe","creditCard.encryptedExpiryMonth.aria.label":"有効期限月","creditCard.encryptedExpiryYear.aria.iframeTitle":"有効期限年のiframe","creditCard.encryptedExpiryYear.aria.label":"有効期限年","creditCard.encryptedSecurityCode.aria.iframeTitle":"セキュリティコードのiframe","creditCard.encryptedSecurityCode.aria.label":"セキュリティコード","creditCard.encryptedPassword.aria.iframeTitle":"パスワードのiframe","creditCard.encryptedPassword.aria.label":"カードのパスワードの最初の2桁","giftcard.encryptedCardNumber.aria.iframeTitle":"カード番号のiframe","giftcard.encryptedCardNumber.aria.label":"カード番号","giftcard.encryptedSecurityCode.aria.iframeTitle":"PINのiframe","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"このギフトカードでの取引ごとに許可される最大%{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"銀行口座番号のiframe","ach.encryptedBankAccountNumber.aria.label":"口座番号","ach.encryptedBankLocationId.aria.iframeTitle":"銀行支店番号のiframe","ach.encryptedBankLocationId.aria.label":"ABAナンバー","twint.saved":"を保存しました",orPayWith:"または次の方法で支払う",invalidFormatExpects:"無効な形式です。期待される形式： %{format}","upi.qrCodeWaitingMessage":"お好みのUPIアプリを使用してQRコードをスキャンし、支払を完了してください","upi.vpaWaitingMessage":"UPIアプリを開いて、支払を確認してください","upi.modeSelection":"UPIをどのように使用しますか？","upi.completePayment":"支払いを完了する","upi.mode.enterUpiId":"UPI IDを入力してください","upi.mode.qrCode":"QRコード","upi.mode.payByAnyUpi":"UPIアプリで支払う","upi.collect.dropdown.label":"UPI IDを入力してください","upi.collect.field.label":"UPI ID/VPAを入力してください","onlineBanking.termsAndConditions":"続行すると、%#利用規約%#に同意したことになります","onlineBankingPL.termsAndConditions":"続行すると、Przelewy24の%#規制%#と%#情報提供義務%#に同意したことになります","ctp.loading.poweredByCtp":"Click to Pay提供","ctp.loading.intro":"Click to Payにカードが保存されているかどうかを確認しています...","ctp.login.title":"Click to Payに進む","ctp.login.subtitle":"続行するには、Click to Payに連携されているメールアドレスを入力してください。","ctp.login.inputLabel":"メールアドレス","ctp.logout.notYou":"ご自分ではありませんか？","ctp.logout.notYourCards":"ご自分のカードではありませんか？","ctp.logout.notYourCard":"ご自分のカードではありませんか？","ctp.logout.notYourProfile":"ご自分のプロフィールではありませんか？","ctp.otp.fieldLabel":"ワンタイムコード","ctp.otp.resendCode":"コードを再送信する","ctp.otp.codeResent":"コードが再送信されました","ctp.otp.title":"Click to Payカードにアクセスする","ctp.otp.subtitle":"%@から%@に送信されたコードを入力して、ご本人であることを確認してください。","ctp.otp.saveCookiesCheckbox.label":"次回は検証をスキップする","ctp.otp.saveCookiesCheckbox.information":"対象店舗で、お使いのデバイスとブラウザで記憶させるように選択すると、より迅速なチェックアウトが可能になります。共有デバイスでは推奨されません。","ctp.otp.saveCookiesCheckbox.shorterInfo":"選択すると、お使いのデバイスとブラウザに記憶されます","ctp.emptyProfile.message":"このClick to Payプロフィールに登録されているカードはありません。","ctp.separatorText":"または以下を使用","ctp.cards.title":"Click to Payで支払いを完了する","ctp.cards.subtitle":"使用するカードを選択してください。","ctp.cards.expiredCard":"期限切れ","ctp.manualCardEntry":"手動カード入力","ctp.aria.infoModalButton":"Click to Payとは","ctp.infoPopup.title":"Click to Payはオンラインで非接触型カードの使いやすさを実現します","ctp.infoPopup.subtitle":"Mastercard、Visa、その他の支払カードでサポートされている高速で安全な支払方法。","ctp.infoPopup.benefit1":"Click to Payは暗号化を使用して情報を安全に保護します","ctp.infoPopup.benefit2":"世界中の業者で使用できます","ctp.infoPopup.benefit3":"一度設定するだけで、今後の支払いが簡単になります","ctp.errors.AUTH_INVALID":"認証が無効です","ctp.errors.NOT_FOUND":"アカウントが見つかりません。有効なメールアドレスを入力するか、カード情報を手動で入力してください","ctp.errors.ID_FORMAT_UNSUPPORTED":"サポートされてない形式です","ctp.errors.FRAUD":"ユーザーアカウントがロックされているか無効になっています","ctp.errors.CONSUMER_ID_MISSING":"リクエストにコンシューマーIDがありません","ctp.errors.ACCT_INACCESSIBLE":"ロックされているなどの理由により、このアカウントは現在利用できません。","ctp.errors.CODE_INVALID":"確認コードが正しくありません","ctp.errors.CODE_EXPIRED":"このコードの有効期限が切れています","ctp.errors.RETRIES_EXCEEDED":"ワンタイムパスワード生成のリトライ回数が上限を超えました","ctp.errors.OTP_SEND_FAILED":"ワンタイムパスワードを受信者に送信できませんでした","ctp.errors.REQUEST_TIMEOUT":"問題が発生しました。再度お試しいただくか、カード情報を手動で入力してください。","ctp.errors.UNKNOWN_ERROR":"問題が発生しました。再度お試しいただくか、カード情報を手動で入力してください。","ctp.errors.SERVICE_ERROR":"問題が発生しました。再度お試しいただくか、カード情報を手動で入力してください。","ctp.errors.SERVER_ERROR":"問題が発生しました。再度お試しいただくか、カード情報を手動で入力してください。","ctp.errors.INVALID_PARAMETER":"問題が発生しました。再度お試しいただくか、カード情報を手動で入力してください。","ctp.errors.AUTH_ERROR":"問題が発生しました。再度お試しいただくか、カード情報を手動で入力してください。","paymentMethodsList.aria.label":"お支払い方法を選択してください","companyDetails.name.invalid":"会社名を入力してください","companyDetails.registrationNumber.invalid":"登録番号を入力してください","consent.checkbox.invalid":"利用規約に同意する必要があります","form.instruction":"特に明記されていない限り、すべてのフィールドは必須です。","trustly.descriptor":"即時銀行支払","trustly.description1":"ご利用の銀行口座から直接お支払い可能（銀行レベルのセキュリティで保護されています）","trustly.description2":"カードなし、アプリのダウンロードなし、登録なし","ancv.input.label":"ANCV ID","ancv.confirmPayment":"ANCVアプリケーションを使用して、支払を確認してください。","ancv.form.instruction":"この支払を検証するには、Cheque-Vacancesアプリケーションが必要です。","ancv.beneficiaryId.invalid":"有効なメールアドレスまたはANCV IDを入力してください","payme.openPayMeApp":"PayMeアプリで承認して支払いを完了し、確認を待ちます。","payme.redirectButtonLabel":"PayMeアプリを開く","payme.scanQrCode":"QRコードで支払いを完了する","payme.timeToPay":"このQRコードは%@有効です","payme.instructions.steps":"PayMeアプリを開きます。%@QRコードをスキャンして支払いを承認します。%@アプリで支払いを完了し、確認を待ちます。","payme.instructions.footnote":"支払いが完了するまでこのページを閉じないでください。","payByBankAISDD.disclaimer.header":"Pay by Bankを使用すると、どの銀行口座からでも即座に支払いができます。","payByBankAISDD.disclaimer.body":"銀行口座を接続することで、お客様は、この承認が取り消されるまで、当社のサービスの利用および/または当社製品の購入のために支払うべき金額をお客様の口座から引き落とすことを承認したことになります。","paymentMethodBrand.other":"その他"}},75884:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Platiti","payButton.redirecting":"Preusmjeravanje...","payButton.with":"Platite iznos od %{value} uporabom stavke %{maskedData}","payButton.saveDetails":"Spremi pojedinosti",close:"Zatvori",storeDetails:"Pohrani za moje sljedeće plaćanje",readMore:"Opširnije","creditCard.holderName":"Ime na kartici","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"Unesite ime kako je prikazano na kartici","creditCard.numberField.title":"Broj kartice","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Datum isteka","creditCard.expiryDateField.placeholder":"MM/GG","creditCard.expiryDateField.month":"Mjesec","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"GG","creditCard.expiryDateField.year":"Godina","creditCard.cvcField.title":"Sigurnosni kôd","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Zapamtiti za sljedeći put","creditCard.cvcField.placeholder.4digits":"4 znamenke","creditCard.cvcField.placeholder.3digits":"3 znamenke","creditCard.taxNumber.placeholder":"YYMMDD / 0123456789",installments:"Broj rata",installmentOption:"%{times} x %{partialValue}",installmentOptionMonths:"Mjeseci: %{times}","installments.oneTime":"Jednokratno plaćanje","installments.installments":"Plaćanje na rate","installments.revolving":"Obnovljivo plaćanje","sepaDirectDebit.ibanField.invalid":"Nevažeći broj računa","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"Ime vlasnika","sepa.ibanNumber":"Broj računa (IBAN)","error.title":"Greška","error.subtitle.redirect":"Preusmjeravanje nije uspjelo","error.subtitle.payment":"Plaćanje nije uspjelo","error.subtitle.refused":"Plaćanje odbijeno","error.message.unknown":"Dogodila se nepoznata greška","errorPanel.title":"Postojeće greške","idealIssuer.selectField.title":"Banka","idealIssuer.selectField.placeholder":"Odaberite banku","creditCard.success":"Plaćanje uspješno",loading:"Učitavanje…",continue:"Nastavi",continueTo:"Nastavi na","wechatpay.timetopay":"Imate %@ za plaćanje","sr.wechatpay.timetopay":"Imate %#minuta%# i %#sekundi%# za plaćanje","wechatpay.scanqrcode":"Skeniraj QR kôd",personalDetails:"Osobni podatci",companyDetails:"Detalji tvrtke","companyDetails.name":"Naziv tvrtke","companyDetails.registrationNumber":"Matični broj",socialSecurityNumber:"Broj socijalnog osiguranja",firstName:"Ime","firstName.invalid":"Unesite svoje ime",infix:"Prefiks",lastName:"Prezime","lastName.invalid":"Unesite svoje prezime",mobileNumber:"Broj mobilnog telefona","mobileNumber.invalid":"Nevažeći broj mobilnog telefona",city:"Grad",postalCode:"Poštanski broj","postalCode.optional":"Poštanski broj (nije obvezno)",countryCode:"Pozivni broj države",telephoneNumber:"Telefonski broj",dateOfBirth:"Datum rođenja",shopperEmail:"Adresa e-pošte",gender:"Spol","gender.notselected":"Odaberite svoj spol",male:"Muškarac",female:"Žena",billingAddress:"Adresa za račun",street:"Ulica",stateOrProvince:"Država ili pokrajina",country:"Zemlja/regija",houseNumberOrName:"Kućni broj",separateDeliveryAddress:"Navedite zasebnu adresu za dostavu",deliveryAddress:"Adresa za dostavu","deliveryAddress.firstName":"Ime primatelja","deliveryAddress.lastName":"Prezime primatelja",zipCode:"Poštanski broj",apartmentSuite:"Stan/apartman",provinceOrTerritory:"Pokrajina ili teritorij",cityTown:"Grad",address:"Adresa","address.placeholder":"Pronađite adresu","address.errors.incomplete":"Unesite adresu za nastavak","address.enterManually":"Ručno unesite adresu",state:"Savezna država","field.title.optional":"(neobavezno)","creditCard.cvcField.title.optional":"Sigurnosni kôd (neobvezno)","issuerList.wallet.placeholder":"Odaberite svoju novčanik",privacyPolicy:"Politika privatnosti","afterPay.agreement":"Slažem se s %@ Rivertyja","riverty.termsAndConditions":"Slažem se s općim %#Uvjetima i odredbama%# za način plaćanja Riverty. Pravila privatnosti tvrtke Riverty nalaze se %#ovdje%#.",paymentConditions:"uvjetima plaćanja",openApp:"Otvorite aplikaciju","voucher.readInstructions":"Pročitajte upute","voucher.introduction":"Zahvaljujemo na kupnji, upotrijebite sljedeći kupon za dovršetak plaćanja.","voucher.expirationDate":"Datum isteka","voucher.alternativeReference":"Alternativna referenca","dragonpay.voucher.non.bank.selectField.placeholder":"Odaberite davatelja usluge","dragonpay.voucher.bank.selectField.placeholder":"Odaberite banku","voucher.paymentReferenceLabel":"Referenca za plaćanje","voucher.surcharge":"Uključuje %@ nadoplate","voucher.introduction.doku":"Zahvaljujemo na kupnji, upotrijebite sljedeće podatke za dovršetak plaćanja.","voucher.shopperName":"Ime kupca","voucher.merchantName":"Trgovac","voucher.introduction.econtext":"Zahvaljujemo na kupnji, upotrijebite sljedeće podatke za dovršetak plaćanja.","voucher.telephoneNumber":"Broj telefona","voucher.shopperReference":"Referenca kupca","voucher.collectionInstitutionNumber":"Broj ustanove za prikupljanje","voucher.econtext.telephoneNumber.invalid":"Telefonski broj mora imati 10 ili 11 znamenki","boletobancario.btnLabel":"Generiraj Boleto","boleto.sendCopyToEmail":"Pošalji kopiju na moju e-poštu","button.copy":"Kopiraj","button.download":"Preuzmi","boleto.socialSecurityNumber.invalid":"Unesite valjani CPF/CNPJ broj","creditCard.storedCard.description.ariaLabel":"Pohranjena kartica završava na %@","voucher.entity":"Entitet",donateButton:"Doniraj",notNowButton:"Ne sada",thanksForYourSupport:"Hvala na podršci!","resultMessages.preauthorized":"Spremljeni podatci",preauthorizeWith:"Prethodno odobri s",confirmPreauthorization:"Potvrdite prethodno odobrenje",confirmPurchase:"Potvrdite kupnju",applyGiftcard:"Iskoristite",giftcardBalance:"Stanje na poklon-kartici",deductedBalance:"Potrošeni iznos","creditCard.pin.title":"Pin","creditCard.encryptedPassword.label":"Prve 2 znamenke lozinke kartice","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Lozinka je netočna","creditCard.taxNumber":"Datum rođenja vlasnika kartice ili registracijski broj tvrtke","creditCard.taxNumber.label":"Datum rođenja vlasnika kartice (GGMMDD) ili registracijski broj tvrtke (10 znamenki)","creditCard.taxNumber.labelAlt":"Registracijski broj tvrtke (10 znamenki)","creditCard.taxNumber.invalid":"Netočan datum rođenja vlasnika kartice (GGMMDD) ili registracijski broj tvrtke (10 znamenki)","storedPaymentMethod.disable.button":"Ukloni","storedPaymentMethod.disable.confirmation":"Uklonite pohranjeni način plaćanja","storedPaymentMethod.disable.confirmButton":"Da, ukloni","storedPaymentMethod.disable.cancelButton":"Otkaži","ach.bankAccount":"Bankovni račun","ach.accountHolderNameField.title":"Ime vlasnika računa","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Nevažeće ime vlasnika računa","ach.accountNumberField.title":"Broj računa","ach.accountNumberField.invalid":"Nevažeći broj računa","ach.accountLocationField.title":"ABA identifikacijski broj","ach.accountLocationField.invalid":"Nevažeći ABA identifikacijski broj","ach.savedBankAccount":"Spremljeni bankovni račun","ach.savings":"Štedni račun","ach.checking":"Tekući račun","select.state":"Odaberite saveznu državu","select.stateOrProvince":"Odaberite državu ili provinciju","select.provinceOrTerritory":"Odaberite provinciju ili teritorij","select.country":"Odaberite zemlju/regiju","select.noOptionsFound":"Nije pronađena nijedna opcija","select.filter.placeholder":"Traži...","telephoneNumber.invalid":"Nevažeći telefonski broj",qrCodeOrApp:"ili","paypal.processingPayment":"Obrada plaćanja u tijeku...",generateQRCode:"Generirajte QR kôd","await.waitForConfirmation":"Čeka se potvrda","mbway.confirmPayment":"Potvrdite uplatu u aplikaciji MB WAY","shopperEmail.invalid":"Nevažeća adresa e-pošte","dateOfBirth.format":"DD/MM/GGGG","dateOfBirth.invalid":"Unesite važeći datum rođenja koji pokazuje da imate najmanje 18 godina","blik.confirmPayment":"Otvorite svoju bankovnu aplikaciju kako biste potvrdili plaćanje.","blik.invalid":"Unesite 6 znamenki","blik.code":"6-znamenkasti kôd","blik.help":"Preuzmite kôd iz bankovne aplikacije.","swish.pendingMessage":"Nakon skeniranja status može biti na čekanju do 10 minuta. Pokušaj ponovnog plaćanja u istom periodu može rezultirati višestrukim naplatama.","field.valid":"Valjano polje","field.invalid":"Nevažeće polje","error.va.gen.01":"Nepotpuno polje","error.va.gen.02":"Nevažeće polje","error.va.sf-cc-num.01":"Unesite valjani broj kartice","error.va.sf-cc-num.02":"Unesite broj kartice","error.va.sf-cc-num.03":"Unesite podržanu marku kartice","error.va.sf-cc-num.04":"Unesite potpuni broj kartice","error.va.sf-cc-dat.01":"Unesite valjani datum isteka","error.va.sf-cc-dat.02":"Unesite valjani datum isteka","error.va.sf-cc-dat.03":"Kreditna kartica uskoro istječe","error.va.sf-cc-dat.04":"Unesite datum isteka","error.va.sf-cc-dat.05":"Unesite potpuni datum isteka","error.va.sf-cc-mth.01":"Unesite mjesec isteka","error.va.sf-cc-yr.01":"Unesite godinu isteka","error.va.sf-cc-yr.02":"Unesite potpunu godinu isteka","error.va.sf-cc-cvc.01":"Unesite sigurnosni kôd","error.va.sf-cc-cvc.02":"Unesite potpuni sigurnosni kôd","error.va.sf-ach-num.01":"Polje za broj bankovnog računa je prazno","error.va.sf-ach-num.02":"Broj je bankovnog računa pogrešne duljine","error.va.sf-ach-loc.01":"Polje je broja usmjeravanja prazno","error.va.sf-ach-loc.02":"Broj je usmjeravanja pogrešne duljine","error.va.sf-kcp-pwd.01":"Polje je lozinke prazno","error.va.sf-kcp-pwd.02":"Lozinka je pogrešne duljine","error.giftcard.no-balance":"Stanje na ovoj poklon-kartici iznosi nula","error.giftcard.card-error":"U našoj evidenciji nemamo poklon-karticu s ovim brojem","error.giftcard.currency-error":"Poklon-kartice vrijede samo u valuti u kojoj su izdane","amazonpay.signout":"Odjava iz Amazona","amazonpay.changePaymentDetails":"Promijenite pojedinosti o plaćanju","partialPayment.warning":"Odaberite drugi način plaćanja da biste platili preostali iznos","partialPayment.remainingBalance":"Preostalo stanje na računu iznosit će %{amount}","bankTransfer.beneficiary":"Primatelj","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referenca","bankTransfer.introduction":"Nastavite da biste kreirali novu uplatu putem bankovne transakcije. Za finaliziranje ovog plaćanja možete se koristiti pojedinostima na sljedećem zaslonu.","bankTransfer.instructions":"Zahvaljujemo na kupnji, upotrijebite sljedeće podatke za dovršetak plaćanja.","bacs.accountHolderName":"Ime vlasnika bankovnog računa","bacs.accountHolderName.invalid":"Nevažeće ime vlasnika bankovnog računa","bacs.accountNumber":"Broj bankovnog računa","bacs.accountNumber.invalid":"Nevažeći broj bankovnog računa","bacs.bankLocationId":"Identifikacijski kôd banke (UK)","bacs.bankLocationId.invalid":"Nevažeći identifikacijski kôd banke (UK)","bacs.consent.amount":"Slažem se da se gore navedeni iznos oduzme s mog bankovnog računa.","bacs.consent.account":"Potvrđujem da je račun na moje ime i da sam jedini potpisnik potreban za autorizaciju izravnog terećenja na ovom računu.",edit:"Uredi","bacs.confirm":"Potvrdi i plati","bacs.result.introduction":"Preuzmite upute za izravno terećenje (DDI / mandat)","download.pdf":"Preuzmite PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe za broj kartice","creditCard.encryptedCardNumber.aria.label":"Broj kartice","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe za datum isteka","creditCard.encryptedExpiryDate.aria.label":"Datum isteka","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe za mjesec isteka","creditCard.encryptedExpiryMonth.aria.label":"Mjesec isteka","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe za godinu isteka","creditCard.encryptedExpiryYear.aria.label":"Godina isteka","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe za sigurnosni kôd","creditCard.encryptedSecurityCode.aria.label":"Sigurnosni kôd","creditCard.encryptedPassword.aria.iframeTitle":"Iframe za lozinku","creditCard.encryptedPassword.aria.label":"Prve 2 znamenke lozinke kartice","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe za broj kartice","giftcard.encryptedCardNumber.aria.label":"Broj kartice","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe za pin","giftcard.encryptedSecurityCode.aria.label":"Pin",giftcardTransactionLimit:"Na ovoj je poklon-kartici maks. dopušteno %{amount} po transakciji","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe za broj bankovnog računa","ach.encryptedBankAccountNumber.aria.label":"Broj računa","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe za bankovni broj usmjeravanja","ach.encryptedBankLocationId.aria.label":"ABA identifikacijski broj","twint.saved":"spremljeno",orPayWith:"ili platite s",invalidFormatExpects:"Nevažeći format. Očekivani format: %{format}","upi.qrCodeWaitingMessage":"Skenirajte QR kôd pomoću željene UPI aplikacije kako biste dovršili plaćanje","upi.vpaWaitingMessage":"Otvorite svoju UPI aplikaciju kako biste potvrdili plaćanje","upi.modeSelection":"Kako biste željeli koristiti UPI?","upi.completePayment":"Završite plaćanje","upi.mode.enterUpiId":"Unesite UPI ID","upi.mode.qrCode":"QR kôd","upi.mode.payByAnyUpi":"Platite bilo kojom UPI aplikacijom","upi.collect.dropdown.label":"Unesite UPI ID","upi.collect.field.label":"Unesite UPI ID/VPA","onlineBanking.termsAndConditions":"Nastavkom prihvaćate %#uvjete i odredbe%#","onlineBankingPL.termsAndConditions":"Nastavljanjem prihvaćate %#propise%# i %#obvezu informiranja%# tvrtke Przelewy24","ctp.loading.poweredByCtp":"Pokreće se uslugom Click to Pay","ctp.loading.intro":"Provjeravamo imate li spremljenih kartica u sklopu usluge Click to Pay...","ctp.login.title":"Nastavite na uslugu Click to Pay","ctp.login.subtitle":"Za nastavak upišite adresu e-pošte koja je povezana s uslugom Click to Pay.","ctp.login.inputLabel":"E-pošta","ctp.logout.notYou":"Niste vi?","ctp.logout.notYourCards":"Nisu vaše kartice?","ctp.logout.notYourCard":"Nije vaša kartica?","ctp.logout.notYourProfile":"Nije vaš profil?","ctp.otp.fieldLabel":"Jednokratni kôd","ctp.otp.resendCode":"Ponovno pošalji kôd","ctp.otp.codeResent":"Kôd je ponovno poslan","ctp.otp.title":"Pristupite svojim karticama za uslugu Click to Pay","ctp.otp.subtitle":"Upišite kôd %@ koji smo poslali na %@ da bismo potvrdili da ste to vi.","ctp.otp.saveCookiesCheckbox.label":"Sljedeći puta preskoči provjeru","ctp.otp.saveCookiesCheckbox.information":"Odaberite kako biste bili zapamćeni na svom uređaju i pregledniku u uključenim trgovinama za bržu naplatu. Ne preporučuje se za zajedničke uređaje.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Odaberite ako želite da vas se zapamti na vašem uređaju i pregledniku","ctp.emptyProfile.message":"Nema kartica registriranih u sklopu ovog profila usluge Click to Pay","ctp.separatorText":"ili upotrijebite","ctp.cards.title":"Dovrši plaćanje uslugom Click to Pay","ctp.cards.subtitle":"Odaberite karticu za upotrebu.","ctp.cards.expiredCard":"Istekla","ctp.manualCardEntry":"Ručni upis kartice","ctp.aria.infoModalButton":"Što je usluga Click to Pay","ctp.infoPopup.title":"Uslugom Click to Pay nudi se jednostavnost mrežnog beskontaktnog plaćanja","ctp.infoPopup.subtitle":"Brza i sigurna metoda plaćanja koja se podržava platnim karticama Mastercard, Visa i drugim.","ctp.infoPopup.benefit1":"Uslugom Click to Pay upotrebljava se šifriranje da bi vaši podatci bili sigurni i zaštićeni","ctp.infoPopup.benefit2":"Upotrebljavajte je za trgovce diljem svijeta","ctp.infoPopup.benefit3":"Postavljanje obavite samo jednom za nesmetana plaćanja u budućnosti","ctp.errors.AUTH_INVALID":"Nevažeća provjera autentičnosti","ctp.errors.NOT_FOUND":"Račun nije pronađen, upišite valjanu adresu e-pošte ili nastavite s ručnim upisom kartice","ctp.errors.ID_FORMAT_UNSUPPORTED":"Nepodržan format","ctp.errors.FRAUD":"Korisnički je račun zaključan ili onemogućen","ctp.errors.CONSUMER_ID_MISSING":"Identitet potrošača nedostaje u zahtjevu","ctp.errors.ACCT_INACCESSIBLE":"Ovaj račun trenutačno nije dostupan, npr. zaključan je","ctp.errors.CODE_INVALID":"Neispravan kontrolni kôd","ctp.errors.CODE_EXPIRED":"Ovaj je kôd istekao","ctp.errors.RETRIES_EXCEEDED":"Prekoračeno je ograničenje broja ponovnih pokušaja za stvaranje jednokratne lozinke","ctp.errors.OTP_SEND_FAILED":"Jednokratna se lozinka nije mogla poslati primatelju","ctp.errors.REQUEST_TIMEOUT":"Nešto nije u redu, pokušajte ponovno ili upotrijebite ručni upis kartice","ctp.errors.UNKNOWN_ERROR":"Nešto nije u redu, pokušajte ponovno ili upotrijebite ručni upis kartice","ctp.errors.SERVICE_ERROR":"Nešto nije u redu, pokušajte ponovno ili upotrijebite ručni upis kartice","ctp.errors.SERVER_ERROR":"Nešto nije u redu, pokušajte ponovno ili upotrijebite ručni upis kartice","ctp.errors.INVALID_PARAMETER":"Nešto nije u redu, pokušajte ponovno ili upotrijebite ručni upis kartice","ctp.errors.AUTH_ERROR":"Nešto nije u redu, pokušajte ponovno ili upotrijebite ručni upis kartice","paymentMethodsList.aria.label":"Odaberi način plaćanja","companyDetails.name.invalid":"Unesite naziv tvrtke","companyDetails.registrationNumber.invalid":"Unesite registracijski broj","consent.checkbox.invalid":"Morate se složiti s odredbama i uvjetima","form.instruction":"Sva su polja obavezna, osim ako nije drugačije označeno.","trustly.descriptor":"Trenutačno bankovno plaćanje","trustly.description1":"Plaćajte izravno s bilo kojeg od svojih bankovnih računa, uz sigurnost na razini banke","trustly.description2":"Bez kartica, bez preuzimanja aplikacija, bez registracije","ancv.input.label":"Vaša ANCV identifikacija","ancv.confirmPayment":"Koristite svoju ANCV aplikaciju za potvrdu plaćanja.","ancv.form.instruction":"Za potvrdu ove uplate neophodna je aplikacija Cheque-Vacances.","ancv.beneficiaryId.invalid":"Unesite valjanu adresu e-pošte ili ANCV ID","payme.openPayMeApp":"Dovršite plaćanje u aplikaciji PayMe: autorizirajte plaćanje u aplikaciji i pričekajte potvrdu.","payme.redirectButtonLabel":"Otvaranje aplikacije PayMe","payme.scanQrCode":"Dovršite svoje plaćanja QR kodom","payme.timeToPay":"Ovaj QR kôd vrijedi za %@","payme.instructions.steps":"Otvorite aplikaciju PayMe.%@Skenirajte QR kod za autorizaciju plaćanja.%@Dovršite plaćanje u aplikaciji i pričekajte potvrdu.","payme.instructions.footnote":"Ne zatvarajte ovu stranicu prije nego što se plaćanje završi","payByBankAISDD.disclaimer.header":"Koristite Pay by Bank za trenutačno plaćanje s bilo kojeg bankovnog računa.","payByBankAISDD.disclaimer.body":"Povezivanjem svog bankovnog računa autorizirate zaduženja na svoj račun za bilo koji iznos koji se duguje za upotrebu naših usluga i/ili kupnju naših proizvoda, sve dok se autorizacija ne opozove.","paymentMethodBrand.other":"Drugo"}}}]);