"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[7894],{27687:function(e,r,a){a.r(r),a.d(r,{default:function(){return t}});var t={payButton:"支付","payButton.redirecting":"正在重定向...","payButton.with":"使用 %{maskedData} 支付 %{value}","payButton.saveDetails":"保存详情",close:"关闭",storeDetails:"保存以便下次支付使用",readMore:"阅读更多","creditCard.holderName":"卡片上的姓名","creditCard.holderName.placeholder":"<PERSON><PERSON>","creditCard.holderName.invalid":"输入卡片上显示的姓名","creditCard.numberField.title":"卡号","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"有效期","creditCard.expiryDateField.placeholder":"月月/年年","creditCard.expiryDateField.month":"月","creditCard.expiryDateField.month.placeholder":"月月","creditCard.expiryDateField.year.placeholder":"年年","creditCard.expiryDateField.year":"年","creditCard.cvcField.title":"安全码","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"记住以便下次使用","creditCard.cvcField.placeholder.4digits":"4 位数","creditCard.cvcField.placeholder.3digits":"3 位数","creditCard.taxNumber.placeholder":"年月日 / 0123456789",installments:"分期付款期数",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} 个月","installments.oneTime":"全款支付","installments.installments":"分期支付","installments.revolving":"循环支付","sepaDirectDebit.ibanField.invalid":"无效的账号","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"持卡人姓名","sepa.ibanNumber":"账号 (IBAN)","error.title":"错误","error.subtitle.redirect":"重定向失败","error.subtitle.payment":"支付失败","error.subtitle.refused":"支付被拒","error.message.unknown":"发生未知错误","errorPanel.title":"现有错误","idealIssuer.selectField.title":"银行","idealIssuer.selectField.placeholder":"选择您的银行","creditCard.success":"支付成功",loading:"正在加载...",continue:"继续",continueTo:"继续至","wechatpay.timetopay":"您有 %@ 可以支付","sr.wechatpay.timetopay":"您有 %#分%# %#秒%# 可以支付","wechatpay.scanqrcode":"扫描二维码",personalDetails:"个人详细信息",companyDetails:"公司详情","companyDetails.name":"公司名称","companyDetails.registrationNumber":"注册号",socialSecurityNumber:"社会保险号码",firstName:"名字","firstName.invalid":"输入您的名字",infix:"前缀",lastName:"姓氏","lastName.invalid":"输入您的姓氏",mobileNumber:"手机号","mobileNumber.invalid":"无效的手机号码",city:"城市",postalCode:"邮政编码","postalCode.optional":"邮政编码（可选）",countryCode:"国家代码",telephoneNumber:"电话号码",dateOfBirth:"出生日期",shopperEmail:"电子邮件地址",gender:"性别","gender.notselected":"选择您的性别",male:"男",female:"女",billingAddress:"账单地址",street:"街道",stateOrProvince:"州或省",country:"国家/地区",houseNumberOrName:"门牌号",separateDeliveryAddress:"指定一个单独的寄送地址",deliveryAddress:"寄送地址","deliveryAddress.firstName":"收件人名字","deliveryAddress.lastName":"收件人姓氏",zipCode:"邮政编码",apartmentSuite:"公寓 / 套房",provinceOrTerritory:"省或地区",cityTown:"市 / 镇",address:"地址","address.placeholder":"查找您的地址","address.errors.incomplete":"输入地址以继续","address.enterManually":"手动输入地址",state:"州","field.title.optional":"（可选）","creditCard.cvcField.title.optional":"安全码（可选）","issuerList.wallet.placeholder":"选择您的钱包",privacyPolicy:"隐私政策","afterPay.agreement":"我同意 Riverty 的 %@","riverty.termsAndConditions":"我同意 Riverty 支付方式的一般%#条款和细则%#。可在%#此处%#查阅 Riverty 的隐私政策。",paymentConditions:"支付条件",openApp:"打开应用","voucher.readInstructions":"阅读说明","voucher.introduction":"感谢您的购买，请使用以下优惠券完成支付。","voucher.expirationDate":"有效期","voucher.alternativeReference":"备选代码","dragonpay.voucher.non.bank.selectField.placeholder":"选择您的提供商","dragonpay.voucher.bank.selectField.placeholder":"选择您的银行","voucher.paymentReferenceLabel":"交易号","voucher.surcharge":"包括 %@ 的附加费","voucher.introduction.doku":"感谢您的购买，请使用以下信息完成支付。","voucher.shopperName":"顾客姓名","voucher.merchantName":"商户","voucher.introduction.econtext":"感谢您的购买，请使用以下信息完成支付。","voucher.telephoneNumber":"电话号码","voucher.shopperReference":"顾客参考","voucher.collectionInstitutionNumber":"收款机构编号","voucher.econtext.telephoneNumber.invalid":"电话号码必须为 10 或 11 位数字","boletobancario.btnLabel":"生成 Boleto","boleto.sendCopyToEmail":"将副本发送到我的电子邮箱","button.copy":"复制","button.download":"下载","boleto.socialSecurityNumber.invalid":"输入有效的 CPF/CNPJ 号码","creditCard.storedCard.description.ariaLabel":"存储的卡片以 ％@ 结尾","voucher.entity":"实体",donateButton:"捐赠",notNowButton:"暂不",thanksForYourSupport:"感谢您的支持！","resultMessages.preauthorized":"详情已保存",preauthorizeWith:"预先授权",confirmPreauthorization:"确认预先授权",confirmPurchase:"确认购买",applyGiftcard:"兑换",giftcardBalance:"礼品卡余额",deductedBalance:"扣减余额","creditCard.pin.title":"Pin","creditCard.encryptedPassword.label":"卡片密码的前 2 位数","creditCard.encryptedPassword.invalid":"无效的密码","creditCard.taxNumber":"持卡人生日或公司注册号","creditCard.taxNumber.label":"持卡人生日 (YYMMDD) 或公司注册号（10 位数）","creditCard.taxNumber.labelAlt":"公司注册号（10 位数）","creditCard.taxNumber.invalid":"无效的持卡人生日或公司注册号","storedPaymentMethod.disable.button":"删除","storedPaymentMethod.disable.confirmation":"删除存储的支付方式","storedPaymentMethod.disable.confirmButton":"是，删除","storedPaymentMethod.disable.cancelButton":"取消","ach.bankAccount":"银行账户","ach.accountHolderNameField.title":"账户持有人姓名","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"无效的账户持有人姓名","ach.accountNumberField.title":"账号","ach.accountNumberField.invalid":"无效的账号","ach.accountLocationField.title":"ABA 路由电汇编码","ach.accountLocationField.invalid":"无效的 ABA 路由电汇编码","ach.savedBankAccount":"已保存的银行账户","ach.savings":"储蓄账户","ach.checking":"支票账户","select.state":"选择州","select.stateOrProvince":"选择州或省","select.provinceOrTerritory":"选择省或地区","select.country":"选择国家/地区","select.noOptionsFound":"未找到任何选项","select.filter.placeholder":"搜索……","telephoneNumber.invalid":"无效的电话号码",qrCodeOrApp:"或者","paypal.processingPayment":"正在处理付款...",generateQRCode:"生成二维码","await.waitForConfirmation":"等待确认","mbway.confirmPayment":"在 MB WAY 应用上确认您的付款","shopperEmail.invalid":"无效的邮件地址","dateOfBirth.format":"DD/MM/YYYY","dateOfBirth.invalid":"请输入有效出生日期，表明您已年满 18 岁","blik.confirmPayment":"打开您的银行应用以确认支付。","blik.invalid":"输入 6 位数","blik.code":"6 位数代码","blik.help":"从您的银行应用中获取代码。","swish.pendingMessage":"扫描后，状态可能会保持最多 10 分钟。在此时间内再次尝试付款可能会导致多次收费。","field.valid":"字段有效","field.invalid":"无效字段","error.va.gen.01":"不完整字段","error.va.gen.02":"无效字段","error.va.sf-cc-num.01":"输入有效卡号","error.va.sf-cc-num.02":"输入卡号","error.va.sf-cc-num.03":"输入支持的卡片品牌","error.va.sf-cc-num.04":"输入完整卡号","error.va.sf-cc-dat.01":"输入有效到期日","error.va.sf-cc-dat.02":"输入有效到期日期","error.va.sf-cc-dat.03":"信用卡即将过期","error.va.sf-cc-dat.04":"输入到期日期","error.va.sf-cc-dat.05":"输入完整到期日期","error.va.sf-cc-mth.01":"输入到期月份","error.va.sf-cc-yr.01":"输入到期年份","error.va.sf-cc-yr.02":"输入完整到期年份","error.va.sf-cc-cvc.01":"输入安全码","error.va.sf-cc-cvc.02":"输入完整安全码","error.va.sf-ach-num.01":"银行账号字段为空","error.va.sf-ach-num.02":"银行账号长度不正确","error.va.sf-ach-loc.01":"银行路由号码字段为空","error.va.sf-ach-loc.02":"银行路由号码长度不正确","error.va.sf-kcp-pwd.01":"密码字段为空","error.va.sf-kcp-pwd.02":"密码长度不正确","error.giftcard.no-balance":"礼品卡余额为零","error.giftcard.card-error":"我们的数据库中没有这个号码的礼品卡","error.giftcard.currency-error":"礼品卡仅以其发行的货币为有效货币","amazonpay.signout":"退出 Amazon","amazonpay.changePaymentDetails":"更改支付详情","partialPayment.warning":"请选择其他支付方式支付剩余款项","partialPayment.remainingBalance":"剩余额度为 %{amount}","bankTransfer.beneficiary":"收款人","bankTransfer.iban":"IBAN","bankTransfer.bic":"中国工商银行","bankTransfer.reference":"参考","bankTransfer.introduction":"继续新建银行转账付款。您可以使用以下屏幕中的详细信息来完成这笔付款。","bankTransfer.instructions":"感谢您的购买，请使用以下信息完成支付。","bacs.accountHolderName":"银行账户持有人姓名","bacs.accountHolderName.invalid":"无效的银行账户持有人姓名","bacs.accountNumber":"银行账号","bacs.accountNumber.invalid":"无效的银行账号","bacs.bankLocationId":"分类代码","bacs.bankLocationId.invalid":"无效的分类代码","bacs.consent.amount":"我同意从银行账户中扣除上述金额。","bacs.consent.account":"我确认该账户为我名下的账户，并且我是授权该账户直接借记的唯一签署人。",edit:"编辑","bacs.confirm":"确认并支付","bacs.result.introduction":"下载您的直接借记指示（DDI/委托）","download.pdf":"下载 PDF 文件","creditCard.encryptedCardNumber.aria.iframeTitle":"卡号 Iframe","creditCard.encryptedCardNumber.aria.label":"卡号","creditCard.encryptedExpiryDate.aria.iframeTitle":"到期日期 Iframe","creditCard.encryptedExpiryDate.aria.label":"有效期","creditCard.encryptedExpiryMonth.aria.iframeTitle":"到期月份 Iframe","creditCard.encryptedExpiryMonth.aria.label":"过期月份","creditCard.encryptedExpiryYear.aria.iframeTitle":"到期年份 Iframe","creditCard.encryptedExpiryYear.aria.label":"过期年份","creditCard.encryptedSecurityCode.aria.iframeTitle":"安全码 Iframe","creditCard.encryptedSecurityCode.aria.label":"安全码","creditCard.encryptedPassword.aria.iframeTitle":"密码 Iframe","creditCard.encryptedPassword.aria.label":"卡片密码的前 2 位数","giftcard.encryptedCardNumber.aria.iframeTitle":"卡号 Iframe","giftcard.encryptedCardNumber.aria.label":"卡号","giftcard.encryptedSecurityCode.aria.iframeTitle":"PIN 码 Iframe","giftcard.encryptedSecurityCode.aria.label":"Pin",giftcardTransactionLimit:"此礼品卡上每笔交易允许的最大金额为 %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"银行账号 Iframe","ach.encryptedBankAccountNumber.aria.label":"账号","ach.encryptedBankLocationId.aria.iframeTitle":"银行路由电汇编码 Iframe","ach.encryptedBankLocationId.aria.label":"ABA 路由电汇编码","twint.saved":"已保存",orPayWith:"或使用以下方式支付",invalidFormatExpects:"无效的格式。预期格式：%{format}","upi.qrCodeWaitingMessage":"使用您首选的 UPI 应用扫描二维码以完成付款","upi.vpaWaitingMessage":"打开您的 UPI 应用以确认付款","upi.modeSelection":"您打算如何使用 UPI？","upi.completePayment":"完成付款","upi.mode.enterUpiId":"输入 UPI ID","upi.mode.qrCode":"二维码","upi.mode.payByAnyUpi":"使用任意 UPI 应用付款","upi.collect.dropdown.label":"输入 UPI ID","upi.collect.field.label":"输入 UPI ID/VPA","onlineBanking.termsAndConditions":"继续，即表示您同意%#条款和细则%#","onlineBankingPL.termsAndConditions":"继续即表示您同意 Przelewy24 的%#条例%#和%#信息义务%#","ctp.loading.poweredByCtp":"由 Click to Pay 提供技术支持","ctp.loading.intro":"我们正在检查您是否拥有任何通过 Click to Pay 保存的卡片…","ctp.login.title":"继续使用 Click to Pay 功能","ctp.login.subtitle":"输入关联至 Click to Pay 的电子邮件地址以继续。","ctp.login.inputLabel":"电子邮件","ctp.logout.notYou":"不是您本人？","ctp.logout.notYourCards":"不是您的卡？","ctp.logout.notYourCard":"不是您的卡片？","ctp.logout.notYourProfile":"不是您的个人资料？","ctp.otp.fieldLabel":"一次性代码","ctp.otp.resendCode":"重新发送代码","ctp.otp.codeResent":"代码已重新发送","ctp.otp.title":"访问您的 Click to Pay 卡","ctp.otp.subtitle":"请输入我们发送至 %@ 的代码 (%@)，以验证是您本人。","ctp.otp.saveCookiesCheckbox.label":"下次跳过验证","ctp.otp.saveCookiesCheckbox.information":"在您的设备和浏览器上，在参与活动的店铺中选择记住该选项，以便更快地结账。不建议用于共享设备。","ctp.otp.saveCookiesCheckbox.shorterInfo":"选择在您的设备和浏览器上记住","ctp.emptyProfile.message":"此 Click to Pay 个人资料中尚未注册任何卡片","ctp.separatorText":"或使用","ctp.cards.title":"借助 Click to Pay 完成付款","ctp.cards.subtitle":"选择要使用的卡片。","ctp.cards.expiredCard":"已过期","ctp.manualCardEntry":"手动卡片录入","ctp.aria.infoModalButton":"什么是 Click to Pay","ctp.infoPopup.title":"Click to Pay 带来非接触式的在线便捷体验","ctp.infoPopup.subtitle":"Mastercard、Visa 和其他支付卡支持的快速、安全的支付方式。","ctp.infoPopup.benefit1":"Click to Pay 使用加密技术，确保您的信息安全","ctp.infoPopup.benefit2":"与全球商户一起使用","ctp.infoPopup.benefit3":"设置一次，支付无忧","ctp.errors.AUTH_INVALID":"身份验证无效","ctp.errors.NOT_FOUND":"未找到帐户，请输入有效的电子邮件或继续使用手动卡片输入","ctp.errors.ID_FORMAT_UNSUPPORTED":"不支持格式","ctp.errors.FRAUD":"用户账户已锁定或禁用","ctp.errors.CONSUMER_ID_MISSING":"请求中缺少使用者标识","ctp.errors.ACCT_INACCESSIBLE":"此帐户当前不可用（例如，已锁定）","ctp.errors.CODE_INVALID":"验证码不正确","ctp.errors.CODE_EXPIRED":"此代码已过期","ctp.errors.RETRIES_EXCEEDED":"已超出 OTP 生成的重试次数限制","ctp.errors.OTP_SEND_FAILED":"OTP 无法发送至收件人","ctp.errors.REQUEST_TIMEOUT":"发生错误，请重试或使用手动卡片输入","ctp.errors.UNKNOWN_ERROR":"发生错误，请重试或使用手动卡片输入","ctp.errors.SERVICE_ERROR":"发生错误，请重试或使用手动卡片输入","ctp.errors.SERVER_ERROR":"发生错误，请重试或使用手动卡片输入","ctp.errors.INVALID_PARAMETER":"发生错误，请重试或使用手动卡片输入","ctp.errors.AUTH_ERROR":"发生错误，请重试或使用手动卡片输入","paymentMethodsList.aria.label":"选择支付方式","companyDetails.name.invalid":"输入公司名称","companyDetails.registrationNumber.invalid":"输入注册号","consent.checkbox.invalid":"您必须同意条款和细则","form.instruction":"除非另有标记，否则所有字段均为必填项。","trustly.descriptor":"即时银行付款","trustly.description1":"直接从您的任何银行账户付款，并配备银行级安全保障","trustly.description2":"无需卡片，无需下载应用，无需注册","ancv.input.label":"您的 ANCV 身份证明","ancv.confirmPayment":"使用您的 ANCV 应用以确认付款。","ancv.form.instruction":"要验证这笔付款，必须使用 Checke-Vacances 应用。","ancv.beneficiaryId.invalid":"输入有效的电子邮件地址或 ANCV ID","payme.openPayMeApp":"在 PayMe 应用中授权付款并等待确认，即可完成您在该应用中的付款。","payme.redirectButtonLabel":"打开 PayMe 应用","payme.scanQrCode":"通过二维码完成付款","payme.timeToPay":"此二维码有效期为 %@","payme.instructions.steps":"打开 PayMe 应用。%@扫描二维码即可授权付款。%@在应用中完成支付并等待确认。","payme.instructions.footnote":"付款完成前，请勿关闭此页面","payByBankAISDD.disclaimer.header":"使用 Pay by Bank 从任何银行账户即时付款。","payByBankAISDD.disclaimer.body":"关联您的银行账户，即表示您授权从您的账户中扣除因使用我们的服务和/或购买我们的产品而需支付的任何金额，直至该授权被撤销。","paymentMethodBrand.other":"其他"}},31983:function(e,r,a){a.r(r),a.d(r,{default:function(){return t}});var t={payButton:"支付","payButton.redirecting":"重新導向中......","payButton.with":"用 %{maskedData} 支付 %{value}","payButton.saveDetails":"儲存詳細資料",close:"關閉",storeDetails:"儲存以供下次付款使用",readMore:"閱讀全文","creditCard.holderName":"信用卡上的姓名","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"輸入卡上所示的姓名","creditCard.numberField.title":"信用卡號碼","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"到期日期","creditCard.expiryDateField.placeholder":"MM/YY","creditCard.expiryDateField.month":"月份","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"YY","creditCard.expiryDateField.year":"年份","creditCard.cvcField.title":"安全碼","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"記住供下次使用","creditCard.cvcField.placeholder.4digits":"4 位數","creditCard.cvcField.placeholder.3digits":"3 位數","creditCard.taxNumber.placeholder":"年月日／0123456789",installments:"分期付款的期數",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} 個月","installments.oneTime":"一次性付款","installments.installments":"分期付款","installments.revolving":"延期付款","sepaDirectDebit.ibanField.invalid":"帳戶號碼無效","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"持有人名稱","sepa.ibanNumber":"帳戶號碼 (IBAN)","error.title":"錯誤","error.subtitle.redirect":"無法重新導向","error.subtitle.payment":"付款失敗","error.subtitle.refused":"付款遭拒絕","error.message.unknown":"發生未知錯誤","errorPanel.title":"現有錯誤","idealIssuer.selectField.title":"銀行","idealIssuer.selectField.placeholder":"選取您的銀行","creditCard.success":"付款成功",loading:"正在載入...",continue:"繼續",continueTo:"繼續前往","wechatpay.timetopay":"您有 %@ 可以支付","sr.wechatpay.timetopay":"您付款的時間還剩 %#分%# %#秒%#","wechatpay.scanqrcode":"掃描 QR 代碼",personalDetails:"個人詳細資料",companyDetails:"公司詳情","companyDetails.name":"公司名稱","companyDetails.registrationNumber":"註冊號碼",socialSecurityNumber:"社會安全碼",firstName:"名字","firstName.invalid":"輸入您的名字",infix:"前綴",lastName:"姓氏","lastName.invalid":"輸入您的姓氏",mobileNumber:"行動電話號碼","mobileNumber.invalid":"手機號碼無效",city:"城市",postalCode:"郵遞區號","postalCode.optional":"郵遞區號（選用）",countryCode:"國家代碼",telephoneNumber:"電話號碼",dateOfBirth:"出生日期",shopperEmail:"電子郵件地址",gender:"性別","gender.notselected":"選擇您的性別",male:"男",female:"女",billingAddress:"帳單地址",street:"街道",stateOrProvince:"州/縣/市",country:"國家／地區",houseNumberOrName:"門牌號",separateDeliveryAddress:"指定另一個派送地址",deliveryAddress:"派送地址","deliveryAddress.firstName":"收件者名字","deliveryAddress.lastName":"收件者姓氏",zipCode:"郵遞區號",apartmentSuite:"公寓／套房",provinceOrTerritory:"省或地區",cityTown:"市／鎮",address:"地址","address.placeholder":"查找您的地址","address.errors.incomplete":"輸入地址以繼續","address.enterManually":"手動輸入地址",state:"州","field.title.optional":"（選用）","creditCard.cvcField.title.optional":"安全碼（選填）","issuerList.wallet.placeholder":"選取您的電子錢包",privacyPolicy:"隱私權政策","afterPay.agreement":"我同意 Riverty 的%@","riverty.termsAndConditions":"我已閱讀並同意 Riverty 付款方式的一般%#《條款及細則》%#。您可以在%#此處%#查閱 Riverty 的《隱私權政策》。",paymentConditions:"付款細則",openApp:"開啟應用程式","voucher.readInstructions":"閱覽說明","voucher.introduction":"多謝惠顧，請使用以下優惠券完成付款。","voucher.expirationDate":"到期日期","voucher.alternativeReference":"備選參照","dragonpay.voucher.non.bank.selectField.placeholder":"選擇您的供應商","dragonpay.voucher.bank.selectField.placeholder":"選取您的銀行","voucher.paymentReferenceLabel":"付款參照號碼","voucher.surcharge":"包含 %@ 附加費","voucher.introduction.doku":"多謝惠顧，請使用以下資訊完成付款。","voucher.shopperName":"購物者姓名","voucher.merchantName":"商家","voucher.introduction.econtext":"多謝惠顧，請使用以下資訊完成付款。","voucher.telephoneNumber":"電話號碼","voucher.shopperReference":"購物者參考","voucher.collectionInstitutionNumber":"收款機構編號","voucher.econtext.telephoneNumber.invalid":"電話號碼的長度必須為 10 或 11 位數","boletobancario.btnLabel":"產生 Boleto","boleto.sendCopyToEmail":"將複本傳送至我的電子郵件","button.copy":"複製","button.download":"下載","boleto.socialSecurityNumber.invalid":"輸入有效的 CPF/CNPJ 號碼","creditCard.storedCard.description.ariaLabel":"已儲存以 %@ 結尾的信用卡","voucher.entity":"實體",donateButton:"捐贈",notNowButton:"稍後再說",thanksForYourSupport:"感謝您的支持！","resultMessages.preauthorized":"已儲存詳細資料",preauthorizeWith:"透過以下方式進行預先授權：",confirmPreauthorization:"確認預先授權",confirmPurchase:"確認購買",applyGiftcard:"兌換",giftcardBalance:"禮品卡餘額",deductedBalance:"扣除餘額","creditCard.pin.title":"數字密碼","creditCard.encryptedPassword.label":"卡密碼的前 2 位數字","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"密碼無效","creditCard.taxNumber":"持卡人生日或公司註冊號碼","creditCard.taxNumber.label":"持卡人生日（年月日）或公司註冊號碼（10 位數）","creditCard.taxNumber.labelAlt":"公司註冊號碼（10 位數）","creditCard.taxNumber.invalid":"持卡人生日或公司註冊號碼無效","storedPaymentMethod.disable.button":"移除","storedPaymentMethod.disable.confirmation":"移除已儲存付款方式","storedPaymentMethod.disable.confirmButton":"是，請移除","storedPaymentMethod.disable.cancelButton":"取消","ach.bankAccount":"銀行帳戶","ach.accountHolderNameField.title":"帳戶持有人姓名","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"帳戶持有人姓名無效","ach.accountNumberField.title":"帳戶號碼","ach.accountNumberField.invalid":"帳戶號碼無效","ach.accountLocationField.title":"ABA 匯款路徑編號","ach.accountLocationField.invalid":"ABA 匯款路徑編號無效","ach.savedBankAccount":"儲存的銀行帳戶","ach.savings":"儲蓄帳戶","ach.checking":"支票帳戶","select.state":"選取州","select.stateOrProvince":"選擇州或省","select.provinceOrTerritory":"選取省或地區","select.country":"選取國家／地區","select.noOptionsFound":"找不到任何選項","select.filter.placeholder":"搜尋……","telephoneNumber.invalid":"電話號碼無效",qrCodeOrApp:"或","paypal.processingPayment":"正在處理付款……",generateQRCode:"產生 QR 代碼","await.waitForConfirmation":"正在等候確認","mbway.confirmPayment":"在 MB WAY 應用程式上確認您的付款","shopperEmail.invalid":"電子郵件地址無效","dateOfBirth.format":"日／月／年","dateOfBirth.invalid":"輸入表明您至少年滿 18 歲的有效出生日期","blik.confirmPayment":"開啟您的銀行應用程式以確認付款。","blik.invalid":"輸入 6 個數字","blik.code":"6 位數代碼","blik.help":"從您的銀行應用程式中獲取代碼。","swish.pendingMessage":"掃描後，該待完成狀態可能持續長達 10 分鐘。試圖在這段時間內再次付款可能會導致多重收費。","field.valid":"欄位有效","field.invalid":"欄位無效","error.va.gen.01":"不完整欄位","error.va.gen.02":"欄位無效","error.va.sf-cc-num.01":"請輸入有效的卡號","error.va.sf-cc-num.02":"輸入卡號","error.va.sf-cc-num.03":"輸入支援的卡品牌","error.va.sf-cc-num.04":"輸入完整的卡號","error.va.sf-cc-dat.01":"輸入有效的到期日","error.va.sf-cc-dat.02":"輸入有效的到期日","error.va.sf-cc-dat.03":"信用卡即將到期","error.va.sf-cc-dat.04":"輸入到期日","error.va.sf-cc-dat.05":"輸入完整的到期日","error.va.sf-cc-mth.01":"輸入到期月份","error.va.sf-cc-yr.01":"輸入到期年份","error.va.sf-cc-yr.02":"輸入完整的到期年份","error.va.sf-cc-cvc.01":"輸入安全碼","error.va.sf-cc-cvc.02":"輸入完整的安全碼","error.va.sf-ach-num.01":"銀行帳戶號碼欄位現為空白","error.va.sf-ach-num.02":"銀行帳戶號碼長度有誤","error.va.sf-ach-loc.01":"銀行匯款路徑編號欄位現為空白","error.va.sf-ach-loc.02":"銀行匯款路徑編號長度有誤","error.va.sf-kcp-pwd.01":"密碼欄位現為空白","error.va.sf-kcp-pwd.02":"密碼長度有誤","error.giftcard.no-balance":"此禮品卡的餘額為零","error.giftcard.card-error":"我們的記錄中並沒有這個號碼的禮品卡","error.giftcard.currency-error":"禮品卡只能以其簽發時所使用的貨幣進行結算","amazonpay.signout":"從 Amazon 登出","amazonpay.changePaymentDetails":"變更付款明細","partialPayment.warning":"選取其他付款方式來支付餘額","partialPayment.remainingBalance":"餘額將為 %{amount}","bankTransfer.beneficiary":"受款人","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"參照","bankTransfer.introduction":"繼續建立新的銀行轉帳付款。您可以使用以下螢幕中的詳細資訊來完成此項付款。","bankTransfer.instructions":"多謝惠顧，請使用以下資訊完成付款。","bacs.accountHolderName":"銀行帳戶持有人姓名","bacs.accountHolderName.invalid":"銀行帳戶持有人姓名無效","bacs.accountNumber":"銀行帳戶號碼","bacs.accountNumber.invalid":"銀行帳戶號碼無效","bacs.bankLocationId":"銀行代碼","bacs.bankLocationId.invalid":"銀行代碼無效","bacs.consent.amount":"我同意從我的銀行帳戶扣除上述金額。","bacs.consent.account":"我確認該帳戶以我的名義開設，並且我是授權從該帳戶直接扣款的唯一簽署人。",edit:"編輯","bacs.confirm":"確認並支付","bacs.result.introduction":"下載您的直接扣款指示（DDI／授權）","download.pdf":"下載 PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"卡號的 IFrame","creditCard.encryptedCardNumber.aria.label":"信用卡號碼","creditCard.encryptedExpiryDate.aria.iframeTitle":"到期日的 IFrame","creditCard.encryptedExpiryDate.aria.label":"到期日期","creditCard.encryptedExpiryMonth.aria.iframeTitle":"到期月份的 IFrame","creditCard.encryptedExpiryMonth.aria.label":"到期月份","creditCard.encryptedExpiryYear.aria.iframeTitle":"到期年份的 IFrame","creditCard.encryptedExpiryYear.aria.label":"到期年份","creditCard.encryptedSecurityCode.aria.iframeTitle":"安全碼的 IFrame","creditCard.encryptedSecurityCode.aria.label":"安全碼","creditCard.encryptedPassword.aria.iframeTitle":"密碼的 IFrame","creditCard.encryptedPassword.aria.label":"卡密碼的前 2 位數字","giftcard.encryptedCardNumber.aria.iframeTitle":"卡號的 IFrame","giftcard.encryptedCardNumber.aria.label":"信用卡號碼","giftcard.encryptedSecurityCode.aria.iframeTitle":"PIN 碼的 IFrame","giftcard.encryptedSecurityCode.aria.label":"數字密碼",giftcardTransactionLimit:"此禮品卡每筆交易的金額上限為 %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"銀行帳戶號碼的 IFrame","ach.encryptedBankAccountNumber.aria.label":"帳戶號碼","ach.encryptedBankLocationId.aria.iframeTitle":"銀行匯款路徑編號的 IFrame","ach.encryptedBankLocationId.aria.label":"ABA 匯款路徑編號","twint.saved":"儲存的",orPayWith:"或透過以下方式支付：",invalidFormatExpects:"格式無效。格式必須為：%{format}","upi.qrCodeWaitingMessage":"使用您偏好的 UPI 應用程式掃描 QR 代碼以完成付款","upi.vpaWaitingMessage":"開啟您的 UPI 應用程式以確認付款","upi.modeSelection":"您想如何使用 UPI？","upi.completePayment":"完成您的付款","upi.mode.enterUpiId":"輸入 UPI ID","upi.mode.qrCode":"QR 代碼","upi.mode.payByAnyUpi":"透過任何 UPI 應用程式付款","upi.collect.dropdown.label":"輸入 UPI ID","upi.collect.field.label":"輸入 UPI ID / VPA","onlineBanking.termsAndConditions":"繼續操作即表示您同意%#條款及細則%#","onlineBankingPL.termsAndConditions":"繼續操作即表示您同意 Przelewy24 的%#法規%#和%#資訊責任%#","ctp.loading.poweredByCtp":"由 Click to Pay 提供技術支援","ctp.loading.intro":"我們正在檢查您是否有任何供 Click to Pay 使用的已儲存卡……","ctp.login.title":"繼續使用 Click to Pay","ctp.login.subtitle":"輸入連接到 Click to Pay 的電子郵件地址以繼續。","ctp.login.inputLabel":"電子郵件","ctp.logout.notYou":"不是您本人？","ctp.logout.notYourCards":"不是您的卡？","ctp.logout.notYourCard":"不是您的卡？","ctp.logout.notYourProfile":"不是您的個人資料？","ctp.otp.fieldLabel":"一次性驗證碼","ctp.otp.resendCode":"重新傳送驗證碼","ctp.otp.codeResent":"已重新傳送驗證碼","ctp.otp.title":"存取您的 Click to Pay 卡","ctp.otp.subtitle":"請輸入我們傳送至 %@ 的驗證碼 %@，以驗證是您本人。","ctp.otp.saveCookiesCheckbox.label":"下次略過驗證","ctp.otp.saveCookiesCheckbox.information":"選擇在裝置和瀏覽器中記住參與商店的驗證資訊，以加快結帳速度。不建議在共享裝置上選取該設定。","ctp.otp.saveCookiesCheckbox.shorterInfo":"選擇在裝置和瀏覽器中記住","ctp.emptyProfile.message":"此 Click to Pay 個人資料中未註冊任何卡","ctp.separatorText":"或使用","ctp.cards.title":"使用 Click to Pay 完成付款","ctp.cards.subtitle":"選取要使用的卡。","ctp.cards.expiredCard":"已過期","ctp.manualCardEntry":"手動卡輸入","ctp.aria.infoModalButton":"什麼是 Click to Pay","ctp.infoPopup.title":"Click to Pay 提供便捷的線上非接觸式支付方式","ctp.infoPopup.subtitle":"Mastercard、Visa 和其他支付卡支援的安全快捷支付方式。","ctp.infoPopup.benefit1":"Click to Pay 使用加密技術，確保您的資料安全無虞","ctp.infoPopup.benefit2":"與全球商家一起使用","ctp.infoPopup.benefit3":"只需設定一次，即可輕鬆支付","ctp.errors.AUTH_INVALID":"驗證無效","ctp.errors.NOT_FOUND":"找不到帳戶，請輸入有效的電子郵件或繼續手動輸入卡資料","ctp.errors.ID_FORMAT_UNSUPPORTED":"不支援此格式","ctp.errors.FRAUD":"使用者帳戶已鎖定或停用","ctp.errors.CONSUMER_ID_MISSING":"請求中遺漏取用者身份識別","ctp.errors.ACCT_INACCESSIBLE":"目前無法使用此帳戶，例如已鎖定","ctp.errors.CODE_INVALID":"驗證碼有誤","ctp.errors.CODE_EXPIRED":"此驗證碼已過期","ctp.errors.RETRIES_EXCEEDED":"超出了產生 OTP 的重試次數上限","ctp.errors.OTP_SEND_FAILED":"OTP 無法傳送給收件者","ctp.errors.REQUEST_TIMEOUT":"出了一些問題，請重試或手動輸入卡資料","ctp.errors.UNKNOWN_ERROR":"出了一些問題，請重試或手動輸入卡資料","ctp.errors.SERVICE_ERROR":"出了一些問題，請重試或手動輸入卡資料","ctp.errors.SERVER_ERROR":"出了一些問題，請重試或手動輸入卡資料","ctp.errors.INVALID_PARAMETER":"出了一些問題，請重試或手動輸入卡資料","ctp.errors.AUTH_ERROR":"出了一些問題，請重試或手動輸入卡資料","paymentMethodsList.aria.label":"選擇付款方式","companyDetails.name.invalid":"輸入公司名稱","companyDetails.registrationNumber.invalid":"輸入註冊號碼","consent.checkbox.invalid":"您必須同意相關條款及細則","form.instruction":"除非另有標示，否則必須填寫所有欄位。","trustly.descriptor":"即時銀行付款","trustly.description1":"直接從任何銀行帳戶付款，並獲得銀行級別安全保障","trustly.description2":"無需卡，無需下載應用程式，無需註冊","ancv.input.label":"您的 ANCV 身分識別","ancv.confirmPayment":"使用您的 ANCV 應用程式確認付款。","ancv.form.instruction":"必須申請 Cheque-Vacances 才能驗證此付款。","ancv.beneficiaryId.invalid":"輸入有效的電子郵件地址或 ANCV ID","payme.openPayMeApp":"在 PayMe 應用程式中授權付款，完成付款並等待確認。","payme.redirectButtonLabel":"開啟 PayMe 應用程式","payme.scanQrCode":"使用 QR 代碼完成付款","payme.timeToPay":"此 QR 代碼對 %@ 有效","payme.instructions.steps":"開啟 PayMe 應用程式。%@掃描 QR 代碼授權付款。%@在應用程式中完成付款並等待確認。","payme.instructions.footnote":"完成付款前，請勿關閉此頁面","payByBankAISDD.disclaimer.header":"運用銀行支付方式透過任何銀行帳戶立即付款。","payByBankAISDD.disclaimer.body":"連結您的銀行帳戶即表示您授權我們可以在您享有我們的服務和/或購買我們的產品時，向您的帳戶扣款對應的費用，直到授權被撤銷為止。","paymentMethodBrand.other":"其他"}}}]);