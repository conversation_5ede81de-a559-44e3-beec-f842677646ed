(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[3975],{24276:function(t,e,n){
/*!
 * vue-awesome-swiper v4.1.1
 * Copyright (c) Surmon. All rights reserved.
 * Released under the MIT License.
 * Surmon <https://github.com/surmon-china>
 */
(function(t,r){r(e,n(5398),n(85471))})(0,function(t,e,n){"use strict";var r;e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e["default"]:e,n=n&&Object.prototype.hasOwnProperty.call(n,"default")?n["default"]:n,function(t){t["SwiperComponent"]="Swiper",t["SwiperSlideComponent"]="SwiperSlide",t["SwiperDirective"]="swiper",t["SwiperInstance"]="$swiper"}(r||(r={}));var i,a,o=Object.freeze({containerClass:"swiper-container",wrapperClass:"swiper-wrapper",slideClass:"swiper-slide"});(function(t){t["Ready"]="ready",t["ClickSlide"]="clickSlide"})(i||(i={})),function(t){t["AutoUpdate"]="autoUpdate",t["AutoDestroy"]="autoDestroy",t["DeleteInstanceOnDestroy"]="deleteInstanceOnDestroy",t["CleanupStylesOnDestroy"]="cleanupStylesOnDestroy"}(a||(a={}));var s=["init","beforeDestroy","slideChange","slideChangeTransitionStart","slideChangeTransitionEnd","slideNextTransitionStart","slideNextTransitionEnd","slidePrevTransitionStart","slidePrevTransitionEnd","transitionStart","transitionEnd","touchStart","touchMove","touchMoveOpposite","sliderMove","touchEnd","click","tap","doubleTap","imagesReady","progress","reachBeginning","reachEnd","fromEdge","setTranslate","setTransition","resize","observerUpdate","beforeLoopFix","loopFix"];
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */function l(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var r=Array(t),i=0;for(e=0;e<n;e++)for(var a=arguments[e],o=0,s=a.length;o<s;o++,i++)r[i]=a[o];return r}var c,u=function(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\s+/g,"-").toLowerCase()},p=function(t,e,n){var r,a,o;if(t&&!t.destroyed){var s=(null===(r=e.composedPath)||void 0===r?void 0:r.call(e))||e.path;if((null===e||void 0===e?void 0:e.target)&&s){var l=Array.from(t.slides),c=Array.from(s);if(l.includes(e.target)||c.some(function(t){return l.includes(t)})){var p=t.clickedIndex,f=Number(null===(o=null===(a=t.clickedSlide)||void 0===a?void 0:a.dataset)||void 0===o?void 0:o.swiperSlideIndex),h=Number.isInteger(f)?f:null;n(i.ClickSlide,p,h),n(u(i.ClickSlide),p,h)}}}},f=function(t,e){s.forEach(function(n){t.on(n,function(){for(var t=arguments,r=[],i=0;i<arguments.length;i++)r[i]=t[i];e.apply(void 0,l([n],r));var a=u(n);a!==n&&e.apply(void 0,l([a],r))})})},h="instanceName";function d(t,e){var n=function(t,e){var n,r,i,a,o=null===(r=null===(n=t.data)||void 0===n?void 0:n.attrs)||void 0===r?void 0:r[e];return void 0!==o?o:null===(a=null===(i=t.data)||void 0===i?void 0:i.attrs)||void 0===a?void 0:a[u(e)]},s=function(t,e,i){return e.arg||n(i,h)||t.id||r.SwiperInstance},l=function(t,e,n){var r=s(t,e,n);return n.context[r]||null},c=function(t){return t.value||e},d=function(t){return[!0,void 0,null,""].includes(t)},v=function(t){var e,n,r=(null===(e=t.data)||void 0===e?void 0:e.on)||(null===(n=t.componentOptions)||void 0===n?void 0:n.listeners);return function(t){for(var e,n=arguments,i=[],a=1;a<arguments.length;a++)i[a-1]=n[a];var o=null===(e=r)||void 0===e?void 0:e[t];o&&o.fns.apply(o,i)}};return{bind:function(t,e,n){-1===t.className.indexOf(o.containerClass)&&(t.className+=(t.className?" ":"")+o.containerClass),t.addEventListener("click",function(r){var i=v(n),a=l(t,e,n);p(a,r,i)})},inserted:function(e,n,r){var a=r.context,o=c(n),l=s(e,n,r),u=v(r),p=a,h=null===p||void 0===p?void 0:p[l];h&&!h.destroyed||(h=new t(e,o),p[l]=h,f(h,u),u(i.Ready,h))},componentUpdated:function(t,e,r){var i,o,s,u,p,f,h,v,m,g,_,y,b=n(r,a.AutoUpdate);if(d(b)){var w=l(t,e,r);if(w){var k=c(e),S=k.loop;S&&(null===(o=null===(i=w)||void 0===i?void 0:i.loopDestroy)||void 0===o||o.call(i)),null===(s=null===w||void 0===w?void 0:w.update)||void 0===s||s.call(w),null===(p=null===(u=w.navigation)||void 0===u?void 0:u.update)||void 0===p||p.call(u),null===(h=null===(f=w.pagination)||void 0===f?void 0:f.render)||void 0===h||h.call(f),null===(m=null===(v=w.pagination)||void 0===v?void 0:v.update)||void 0===m||m.call(v),S&&(null===(_=null===(g=w)||void 0===g?void 0:g.loopCreate)||void 0===_||_.call(g),null===(y=null===w||void 0===w?void 0:w.update)||void 0===y||y.call(w))}}},unbind:function(t,e,r){var i,o=n(r,a.AutoDestroy);if(d(o)){var s=l(t,e,r);s&&s.initialized&&(null===(i=null===s||void 0===s?void 0:s.destroy)||void 0===i||i.call(s,d(n(r,a.DeleteInstanceOnDestroy)),d(n(r,a.CleanupStylesOnDestroy))))}}}}function v(t){var e;return n.extend({name:r.SwiperComponent,props:(e={defaultOptions:{type:Object,required:!1,default:function(){return{}}},options:{type:Object,required:!1}},e[a.AutoUpdate]={type:Boolean,default:!0},e[a.AutoDestroy]={type:Boolean,default:!0},e[a.DeleteInstanceOnDestroy]={type:Boolean,required:!1,default:!0},e[a.CleanupStylesOnDestroy]={type:Boolean,required:!1,default:!0},e),data:function(){var t;return t={},t[r.SwiperInstance]=null,t},computed:{swiperInstance:{cache:!1,set:function(t){this[r.SwiperInstance]=t},get:function(){return this[r.SwiperInstance]}},swiperOptions:function(){return this.options||this.defaultOptions},wrapperClass:function(){return this.swiperOptions.wrapperClass||o.wrapperClass}},methods:{handleSwiperClick:function(t){p(this.swiperInstance,t,this.$emit.bind(this))},autoReLoopSwiper:function(){var t,e;if(this.swiperInstance&&this.swiperOptions.loop){var n=this.swiperInstance;null===(t=null===n||void 0===n?void 0:n.loopDestroy)||void 0===t||t.call(n),null===(e=null===n||void 0===n?void 0:n.loopCreate)||void 0===e||e.call(n)}},updateSwiper:function(){var t,e,n,r,i,o,s,l;this[a.AutoUpdate]&&this.swiperInstance&&(this.autoReLoopSwiper(),null===(e=null===(t=this.swiperInstance)||void 0===t?void 0:t.update)||void 0===e||e.call(t),null===(r=null===(n=this.swiperInstance.navigation)||void 0===n?void 0:n.update)||void 0===r||r.call(n),null===(o=null===(i=this.swiperInstance.pagination)||void 0===i?void 0:i.render)||void 0===o||o.call(i),null===(l=null===(s=this.swiperInstance.pagination)||void 0===s?void 0:s.update)||void 0===l||l.call(s))},destroySwiper:function(){var t,e;this[a.AutoDestroy]&&this.swiperInstance&&this.swiperInstance.initialized&&(null===(e=null===(t=this.swiperInstance)||void 0===t?void 0:t.destroy)||void 0===e||e.call(t,this[a.DeleteInstanceOnDestroy],this[a.CleanupStylesOnDestroy]))},initSwiper:function(){this.swiperInstance=new t(this.$el,this.swiperOptions),f(this.swiperInstance,this.$emit.bind(this)),this.$emit(i.Ready,this.swiperInstance)}},mounted:function(){this.swiperInstance||this.initSwiper()},activated:function(){this.updateSwiper()},updated:function(){this.updateSwiper()},beforeDestroy:function(){this.$nextTick(this.destroySwiper)},render:function(t){return t("div",{staticClass:o.containerClass,on:{click:this.handleSwiperClick}},[this.$slots[c.ParallaxBg],t("div",{class:this.wrapperClass},this.$slots.default),this.$slots[c.Pagination],this.$slots[c.PrevButton],this.$slots[c.NextButton],this.$slots[c.Scrollbar]])}})}(function(t){t["ParallaxBg"]="parallax-bg",t["Pagination"]="pagination",t["Scrollbar"]="scrollbar",t["PrevButton"]="button-prev",t["NextButton"]="button-next"})(c||(c={}));var m=n.extend({name:r.SwiperSlideComponent,computed:{slideClass:function(){var t,e;return(null===(e=null===(t=this.$parent)||void 0===t?void 0:t.swiperOptions)||void 0===e?void 0:e.slideClass)||o.slideClass}},methods:{update:function(){var t,e=this.$parent;e[a.AutoUpdate]&&(null===(t=null===e||void 0===e?void 0:e.swiperInstance)||void 0===t||t.update())}},mounted:function(){this.update()},updated:function(){this.update()},render:function(t){return t("div",{class:this.slideClass},this.$slots.default)}}),g=function(t){var e=function(n,i){if(!e.installed){var a=v(t);i&&(a.options.props.defaultOptions.default=function(){return i}),n.component(r.SwiperComponent,a),n.component(r.SwiperSlideComponent,m),n.directive(r.SwiperDirective,d(t,i)),e.installed=!0}};return e};function _(t){var e;return e={version:"4.1.1",install:g(t),directive:d(t)},e[r.SwiperComponent]=v(t),e[r.SwiperSlideComponent]=m,e}var y=_(e),b=y.version,w=y.install,k=y.directive,S=y.Swiper,F=y.SwiperSlide;t.Swiper=S,t.SwiperSlide=F,t.default=y,t.directive=k,t.install=w,t.version=b,Object.defineProperty(t,"__esModule",{value:!0})})},64765:function(t,e){"use strict";
/*!
 * vue-i18n v8.28.2 
 * (c) 2022 kazuya kawaguchi
 * Released under the MIT License.
 */var n=["compactDisplay","currency","currencyDisplay","currencySign","localeMatcher","notation","numberingSystem","signDisplay","style","unit","unitDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits"],r=["dateStyle","timeStyle","calendar","localeMatcher","hour12","hourCycle","timeZone","formatMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName"];function i(t,e){"undefined"!==typeof console&&(console.warn("[vue-i18n] "+t),e&&console.warn(e.stack))}function a(t,e){"undefined"!==typeof console&&(console.error("[vue-i18n] "+t),e&&console.error(e.stack))}var o=Array.isArray;function s(t){return null!==t&&"object"===typeof t}function l(t){return"boolean"===typeof t}function c(t){return"string"===typeof t}var u=Object.prototype.toString,p="[object Object]";function f(t){return u.call(t)===p}function h(t){return null===t||void 0===t}function d(t){return"function"===typeof t}function v(){var t=[],e=arguments.length;while(e--)t[e]=arguments[e];var n=null,r=null;return 1===t.length?s(t[0])||o(t[0])?r=t[0]:"string"===typeof t[0]&&(n=t[0]):2===t.length&&("string"===typeof t[0]&&(n=t[0]),(s(t[1])||o(t[1]))&&(r=t[1])),{locale:n,params:r}}function m(t){return JSON.parse(JSON.stringify(t))}function g(t,e){if(t.delete(e))return t}function _(t){var e=[];return t.forEach(function(t){return e.push(t)}),e}function y(t,e){return!!~t.indexOf(e)}var b=Object.prototype.hasOwnProperty;function w(t,e){return b.call(t,e)}function k(t){for(var e=arguments,n=Object(t),r=1;r<arguments.length;r++){var i=e[r];if(void 0!==i&&null!==i){var a=void 0;for(a in i)w(i,a)&&(s(i[a])?n[a]=k(n[a],i[a]):n[a]=i[a])}}return n}function S(t,e){if(t===e)return!0;var n=s(t),r=s(e);if(!n||!r)return!n&&!r&&String(t)===String(e);try{var i=o(t),a=o(e);if(i&&a)return t.length===e.length&&t.every(function(t,n){return S(t,e[n])});if(i||a)return!1;var l=Object.keys(t),c=Object.keys(e);return l.length===c.length&&l.every(function(n){return S(t[n],e[n])})}catch(u){return!1}}function F(t){return t.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}function O(t){return null!=t&&Object.keys(t).forEach(function(e){"string"==typeof t[e]&&(t[e]=F(t[e]))}),t}function C(t){t.prototype.hasOwnProperty("$i18n")||Object.defineProperty(t.prototype,"$i18n",{get:function(){return this._i18n}}),t.prototype.$t=function(t){var e=[],n=arguments.length-1;while(n-- >0)e[n]=arguments[n+1];var r=this.$i18n;return r._t.apply(r,[t,r.locale,r._getMessages(),this].concat(e))},t.prototype.$tc=function(t,e){var n=[],r=arguments.length-2;while(r-- >0)n[r]=arguments[r+2];var i=this.$i18n;return i._tc.apply(i,[t,i.locale,i._getMessages(),this,e].concat(n))},t.prototype.$te=function(t,e){var n=this.$i18n;return n._te(t,n.locale,n._getMessages(),e)},t.prototype.$d=function(t){var e,n=[],r=arguments.length-1;while(r-- >0)n[r]=arguments[r+1];return(e=this.$i18n).d.apply(e,[t].concat(n))},t.prototype.$n=function(t){var e,n=[],r=arguments.length-1;while(r-- >0)n[r]=arguments[r+1];return(e=this.$i18n).n.apply(e,[t].concat(n))}}function T(t){function e(){this!==this.$root&&this.$options.__INTLIFY_META__&&this.$el&&this.$el.setAttribute("data-intlify",this.$options.__INTLIFY_META__)}return void 0===t&&(t=!1),t?{mounted:e}:{beforeCreate:function(){var t=this.$options;if(t.i18n=t.i18n||(t.__i18nBridge||t.__i18n?{}:null),t.i18n)if(t.i18n instanceof Ot){if(t.__i18nBridge||t.__i18n)try{var e=t.i18n&&t.i18n.messages?t.i18n.messages:{},n=t.__i18nBridge||t.__i18n;n.forEach(function(t){e=k(e,JSON.parse(t))}),Object.keys(e).forEach(function(n){t.i18n.mergeLocaleMessage(n,e[n])})}catch(l){0}this._i18n=t.i18n,this._i18nWatcher=this._i18n.watchI18nData()}else if(f(t.i18n)){var r=this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Ot?this.$root.$i18n:null;if(r&&(t.i18n.root=this.$root,t.i18n.formatter=r.formatter,t.i18n.fallbackLocale=r.fallbackLocale,t.i18n.formatFallbackMessages=r.formatFallbackMessages,t.i18n.silentTranslationWarn=r.silentTranslationWarn,t.i18n.silentFallbackWarn=r.silentFallbackWarn,t.i18n.pluralizationRules=r.pluralizationRules,t.i18n.preserveDirectiveContent=r.preserveDirectiveContent),t.__i18nBridge||t.__i18n)try{var i=t.i18n&&t.i18n.messages?t.i18n.messages:{},a=t.__i18nBridge||t.__i18n;a.forEach(function(t){i=k(i,JSON.parse(t))}),t.i18n.messages=i}catch(l){0}var o=t.i18n,s=o.sharedMessages;s&&f(s)&&(t.i18n.messages=k(t.i18n.messages,s)),this._i18n=new Ot(t.i18n),this._i18nWatcher=this._i18n.watchI18nData(),(void 0===t.i18n.sync||t.i18n.sync)&&(this._localeWatcher=this.$i18n.watchLocale()),r&&r.onComponentInstanceCreated(this._i18n)}else 0;else this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Ot?this._i18n=this.$root.$i18n:t.parent&&t.parent.$i18n&&t.parent.$i18n instanceof Ot&&(this._i18n=t.parent.$i18n)},beforeMount:function(){var t=this.$options;t.i18n=t.i18n||(t.__i18nBridge||t.__i18n?{}:null),t.i18n?(t.i18n instanceof Ot||f(t.i18n))&&(this._i18n.subscribeDataChanging(this),this._subscribing=!0):(this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Ot||t.parent&&t.parent.$i18n&&t.parent.$i18n instanceof Ot)&&(this._i18n.subscribeDataChanging(this),this._subscribing=!0)},mounted:e,beforeDestroy:function(){if(this._i18n){var t=this;this.$nextTick(function(){t._subscribing&&(t._i18n.unsubscribeDataChanging(t),delete t._subscribing),t._i18nWatcher&&(t._i18nWatcher(),t._i18n.destroyVM(),delete t._i18nWatcher),t._localeWatcher&&(t._localeWatcher(),delete t._localeWatcher)})}}}}var $={name:"i18n",functional:!0,props:{tag:{type:[String,Boolean,Object],default:"span"},path:{type:String,required:!0},locale:{type:String},places:{type:[Array,Object]}},render:function(t,e){var n=e.data,r=e.parent,i=e.props,a=e.slots,o=r.$i18n;if(o){var s=i.path,l=i.locale,c=i.places,u=a(),p=o.i(s,l,I(u)||c?D(u.default,c):u),f=i.tag&&!0!==i.tag||!1===i.tag?i.tag:"span";return f?t(f,n,p):p}}};function I(t){var e;for(e in t)if("default"!==e)return!1;return Boolean(e)}function D(t,e){var n=e?M(e):{};if(!t)return n;t=t.filter(function(t){return t.tag||""!==t.text.trim()});var r=t.every(x);return t.reduce(r?j:L,n)}function M(t){return Array.isArray(t)?t.reduce(L,{}):Object.assign({},t)}function j(t,e){return e.data&&e.data.attrs&&e.data.attrs.place&&(t[e.data.attrs.place]=e),t}function L(t,e,n){return t[n]=e,t}function x(t){return Boolean(t.data&&t.data.attrs&&t.data.attrs.place)}var N,E={name:"i18n-n",functional:!0,props:{tag:{type:[String,Boolean,Object],default:"span"},value:{type:Number,required:!0},format:{type:[String,Object]},locale:{type:String}},render:function(t,e){var r=e.props,i=e.parent,a=e.data,o=i.$i18n;if(!o)return null;var l=null,u=null;c(r.format)?l=r.format:s(r.format)&&(r.format.key&&(l=r.format.key),u=Object.keys(r.format).reduce(function(t,e){var i;return y(n,e)?Object.assign({},t,(i={},i[e]=r.format[e],i)):t},null));var p=r.locale||o.locale,f=o._ntp(r.value,p,l,u),h=f.map(function(t,e){var n,r=a.scopedSlots&&a.scopedSlots[t.type];return r?r((n={},n[t.type]=t.value,n.index=e,n.parts=f,n)):t.value}),d=r.tag&&!0!==r.tag||!1===r.tag?r.tag:"span";return d?t(d,{attrs:a.attrs,class:a["class"],staticClass:a.staticClass},h):h}};function P(t,e,n){W(t,n)&&U(t,e,n)}function A(t,e,n,r){if(W(t,n)){var i=n.context.$i18n;B(t,n)&&S(e.value,e.oldValue)&&S(t._localeMessage,i.getLocaleMessage(i.locale))||U(t,e,n)}}function R(t,e,n,r){var a=n.context;if(a){var o=n.context.$i18n||{};e.modifiers.preserve||o.preserveDirectiveContent||(t.textContent=""),t._vt=void 0,delete t["_vt"],t._locale=void 0,delete t["_locale"],t._localeMessage=void 0,delete t["_localeMessage"]}else i("Vue instance does not exists in VNode context")}function W(t,e){var n=e.context;return n?!!n.$i18n||(i("VueI18n instance does not exists in Vue instance"),!1):(i("Vue instance does not exists in VNode context"),!1)}function B(t,e){var n=e.context;return t._locale===n.$i18n.locale}function U(t,e,n){var r,a,o=e.value,s=H(o),l=s.path,c=s.locale,u=s.args,p=s.choice;if(l||c||u)if(l){var f=n.context;t._vt=t.textContent=null!=p?(r=f.$i18n).tc.apply(r,[l,p].concat(V(c,u))):(a=f.$i18n).t.apply(a,[l].concat(V(c,u))),t._locale=f.$i18n.locale,t._localeMessage=f.$i18n.getLocaleMessage(f.$i18n.locale)}else i("`path` is required in v-t directive");else i("value type not supported")}function H(t){var e,n,r,i;return c(t)?e=t:f(t)&&(e=t.path,n=t.locale,r=t.args,i=t.choice),{path:e,locale:n,args:r,choice:i}}function V(t,e){var n=[];return t&&n.push(t),e&&(Array.isArray(e)||f(e))&&n.push(e),n}function z(t,e){void 0===e&&(e={bridge:!1}),z.installed=!0,N=t;N.version&&Number(N.version.split(".")[0]);C(N),N.mixin(T(e.bridge)),N.directive("t",{bind:P,update:A,unbind:R}),N.component($.name,$),N.component(E.name,E);var n=N.config.optionMergeStrategies;n.i18n=function(t,e){return void 0===e?t:e}}var q=function(){this._caches=Object.create(null)};q.prototype.interpolate=function(t,e){if(!e)return[t];var n=this._caches[t];return n||(n=J(t),this._caches[t]=n),Y(n,e)};var G=/^(?:\d)+/,Z=/^(?:\w)+/;function J(t){var e=[],n=0,r="";while(n<t.length){var i=t[n++];if("{"===i){r&&e.push({type:"text",value:r}),r="";var a="";i=t[n++];while(void 0!==i&&"}"!==i)a+=i,i=t[n++];var o="}"===i,s=G.test(a)?"list":o&&Z.test(a)?"named":"unknown";e.push({value:a,type:s})}else"%"===i?"{"!==t[n]&&(r+=i):r+=i}return r&&e.push({type:"text",value:r}),e}function Y(t,e){var n=[],r=0,i=Array.isArray(e)?"list":s(e)?"named":"unknown";if("unknown"===i)return n;while(r<t.length){var a=t[r];switch(a.type){case"text":n.push(a.value);break;case"list":n.push(e[parseInt(a.value,10)]);break;case"named":"named"===i&&n.push(e[a.value]);break;case"unknown":0;break}r++}return n}var X=0,K=1,Q=2,tt=3,et=0,nt=1,rt=2,it=3,at=4,ot=5,st=6,lt=7,ct=8,ut=[];ut[et]={ws:[et],ident:[it,X],"[":[at],eof:[lt]},ut[nt]={ws:[nt],".":[rt],"[":[at],eof:[lt]},ut[rt]={ws:[rt],ident:[it,X],0:[it,X],number:[it,X]},ut[it]={ident:[it,X],0:[it,X],number:[it,X],ws:[nt,K],".":[rt,K],"[":[at,K],eof:[lt,K]},ut[at]={"'":[ot,X],'"':[st,X],"[":[at,Q],"]":[nt,tt],eof:ct,else:[at,X]},ut[ot]={"'":[at,X],eof:ct,else:[ot,X]},ut[st]={'"':[at,X],eof:ct,else:[st,X]};var pt=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ft(t){return pt.test(t)}function ht(t){var e=t.charCodeAt(0),n=t.charCodeAt(t.length-1);return e!==n||34!==e&&39!==e?t:t.slice(1,-1)}function dt(t){if(void 0===t||null===t)return"eof";var e=t.charCodeAt(0);switch(e){case 91:case 93:case 46:case 34:case 39:return t;case 95:case 36:case 45:return"ident";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"ws"}return"ident"}function vt(t){var e=t.trim();return("0"!==t.charAt(0)||!isNaN(t))&&(ft(e)?ht(e):"*"+e)}function mt(t){var e,n,r,i,a,o,s,l=[],c=-1,u=et,p=0,f=[];function h(){var e=t[c+1];if(u===ot&&"'"===e||u===st&&'"'===e)return c++,r="\\"+e,f[X](),!0}f[K]=function(){void 0!==n&&(l.push(n),n=void 0)},f[X]=function(){void 0===n?n=r:n+=r},f[Q]=function(){f[X](),p++},f[tt]=function(){if(p>0)p--,u=at,f[X]();else{if(p=0,void 0===n)return!1;if(n=vt(n),!1===n)return!1;f[K]()}};while(null!==u)if(c++,e=t[c],"\\"!==e||!h()){if(i=dt(e),s=ut[u],a=s[i]||s["else"]||ct,a===ct)return;if(u=a[0],o=f[a[1]],o&&(r=a[2],r=void 0===r?e:r,!1===o()))return;if(u===lt)return l}}var gt=function(){this._cache=Object.create(null)};gt.prototype.parsePath=function(t){var e=this._cache[t];return e||(e=mt(t),e&&(this._cache[t]=e)),e||[]},gt.prototype.getPathValue=function(t,e){if(!s(t))return null;var n=this.parsePath(e);if(0===n.length)return null;var r=n.length,i=t,a=0;while(a<r){var o=i[n[a]];if(void 0===o||null===o)return null;i=o,a++}return i};var _t,yt=/<\/?[\w\s="/.':;#-\/]+>/,bt=/(?:@(?:\.[a-zA-Z]+)?:(?:[\w\-_|./]+|\([\w\-_:|./]+\)))/g,wt=/^@(?:\.([a-zA-Z]+))?:/,kt=/[()]/g,St={upper:function(t){return t.toLocaleUpperCase()},lower:function(t){return t.toLocaleLowerCase()},capitalize:function(t){return""+t.charAt(0).toLocaleUpperCase()+t.substr(1)}},Ft=new q,Ot=function(t){var e=this;void 0===t&&(t={}),!N&&"undefined"!==typeof window&&window.Vue&&z(window.Vue);var n=t.locale||"en-US",r=!1!==t.fallbackLocale&&(t.fallbackLocale||"en-US"),i=t.messages||{},a=t.dateTimeFormats||t.datetimeFormats||{},o=t.numberFormats||{};this._vm=null,this._formatter=t.formatter||Ft,this._modifiers=t.modifiers||{},this._missing=t.missing||null,this._root=t.root||null,this._sync=void 0===t.sync||!!t.sync,this._fallbackRoot=void 0===t.fallbackRoot||!!t.fallbackRoot,this._fallbackRootWithEmptyString=void 0===t.fallbackRootWithEmptyString||!!t.fallbackRootWithEmptyString,this._formatFallbackMessages=void 0!==t.formatFallbackMessages&&!!t.formatFallbackMessages,this._silentTranslationWarn=void 0!==t.silentTranslationWarn&&t.silentTranslationWarn,this._silentFallbackWarn=void 0!==t.silentFallbackWarn&&!!t.silentFallbackWarn,this._dateTimeFormatters={},this._numberFormatters={},this._path=new gt,this._dataListeners=new Set,this._componentInstanceCreatedListener=t.componentInstanceCreatedListener||null,this._preserveDirectiveContent=void 0!==t.preserveDirectiveContent&&!!t.preserveDirectiveContent,this.pluralizationRules=t.pluralizationRules||{},this._warnHtmlInMessage=t.warnHtmlInMessage||"off",this._postTranslation=t.postTranslation||null,this._escapeParameterHtml=t.escapeParameterHtml||!1,"__VUE_I18N_BRIDGE__"in t&&(this.__VUE_I18N_BRIDGE__=t.__VUE_I18N_BRIDGE__),this.getChoiceIndex=function(t,n){var r=Object.getPrototypeOf(e);if(r&&r.getChoiceIndex){var i=r.getChoiceIndex;return i.call(e,t,n)}var a=function(t,e){return t=Math.abs(t),2===e?t?t>1?1:0:1:t?Math.min(t,2):0};return e.locale in e.pluralizationRules?e.pluralizationRules[e.locale].apply(e,[t,n]):a(t,n)},this._exist=function(t,n){return!(!t||!n)&&(!h(e._path.getPathValue(t,n))||!!t[n])},"warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||Object.keys(i).forEach(function(t){e._checkLocaleMessage(t,e._warnHtmlInMessage,i[t])}),this._initVM({locale:n,fallbackLocale:r,messages:i,dateTimeFormats:a,numberFormats:o})},Ct={vm:{configurable:!0},messages:{configurable:!0},dateTimeFormats:{configurable:!0},numberFormats:{configurable:!0},availableLocales:{configurable:!0},locale:{configurable:!0},fallbackLocale:{configurable:!0},formatFallbackMessages:{configurable:!0},missing:{configurable:!0},formatter:{configurable:!0},silentTranslationWarn:{configurable:!0},silentFallbackWarn:{configurable:!0},preserveDirectiveContent:{configurable:!0},warnHtmlInMessage:{configurable:!0},postTranslation:{configurable:!0},sync:{configurable:!0}};Ot.prototype._checkLocaleMessage=function(t,e,n){var r=[],s=function(t,e,n,r){if(f(n))Object.keys(n).forEach(function(i){var a=n[i];f(a)?(r.push(i),r.push("."),s(t,e,a,r),r.pop(),r.pop()):(r.push(i),s(t,e,a,r),r.pop())});else if(o(n))n.forEach(function(n,i){f(n)?(r.push("["+i+"]"),r.push("."),s(t,e,n,r),r.pop(),r.pop()):(r.push("["+i+"]"),s(t,e,n,r),r.pop())});else if(c(n)){var l=yt.test(n);if(l){var u="Detected HTML in message '"+n+"' of keypath '"+r.join("")+"' at '"+e+"'. Consider component interpolation with '<i18n>' to avoid XSS. See https://bit.ly/2ZqJzkp";"warn"===t?i(u):"error"===t&&a(u)}}};s(e,t,n,r)},Ot.prototype._initVM=function(t){var e=N.config.silent;N.config.silent=!0,this._vm=new N({data:t,__VUE18N__INSTANCE__:!0}),N.config.silent=e},Ot.prototype.destroyVM=function(){this._vm.$destroy()},Ot.prototype.subscribeDataChanging=function(t){this._dataListeners.add(t)},Ot.prototype.unsubscribeDataChanging=function(t){g(this._dataListeners,t)},Ot.prototype.watchI18nData=function(){var t=this;return this._vm.$watch("$data",function(){var e=_(t._dataListeners),n=e.length;while(n--)N.nextTick(function(){e[n]&&e[n].$forceUpdate()})},{deep:!0})},Ot.prototype.watchLocale=function(t){if(t){if(!this.__VUE_I18N_BRIDGE__)return null;var e=this,n=this._vm;return this.vm.$watch("locale",function(r){n.$set(n,"locale",r),e.__VUE_I18N_BRIDGE__&&t&&(t.locale.value=r),n.$forceUpdate()},{immediate:!0})}if(!this._sync||!this._root)return null;var r=this._vm;return this._root.$i18n.vm.$watch("locale",function(t){r.$set(r,"locale",t),r.$forceUpdate()},{immediate:!0})},Ot.prototype.onComponentInstanceCreated=function(t){this._componentInstanceCreatedListener&&this._componentInstanceCreatedListener(t,this)},Ct.vm.get=function(){return this._vm},Ct.messages.get=function(){return m(this._getMessages())},Ct.dateTimeFormats.get=function(){return m(this._getDateTimeFormats())},Ct.numberFormats.get=function(){return m(this._getNumberFormats())},Ct.availableLocales.get=function(){return Object.keys(this.messages).sort()},Ct.locale.get=function(){return this._vm.locale},Ct.locale.set=function(t){this._vm.$set(this._vm,"locale",t)},Ct.fallbackLocale.get=function(){return this._vm.fallbackLocale},Ct.fallbackLocale.set=function(t){this._localeChainCache={},this._vm.$set(this._vm,"fallbackLocale",t)},Ct.formatFallbackMessages.get=function(){return this._formatFallbackMessages},Ct.formatFallbackMessages.set=function(t){this._formatFallbackMessages=t},Ct.missing.get=function(){return this._missing},Ct.missing.set=function(t){this._missing=t},Ct.formatter.get=function(){return this._formatter},Ct.formatter.set=function(t){this._formatter=t},Ct.silentTranslationWarn.get=function(){return this._silentTranslationWarn},Ct.silentTranslationWarn.set=function(t){this._silentTranslationWarn=t},Ct.silentFallbackWarn.get=function(){return this._silentFallbackWarn},Ct.silentFallbackWarn.set=function(t){this._silentFallbackWarn=t},Ct.preserveDirectiveContent.get=function(){return this._preserveDirectiveContent},Ct.preserveDirectiveContent.set=function(t){this._preserveDirectiveContent=t},Ct.warnHtmlInMessage.get=function(){return this._warnHtmlInMessage},Ct.warnHtmlInMessage.set=function(t){var e=this,n=this._warnHtmlInMessage;if(this._warnHtmlInMessage=t,n!==t&&("warn"===t||"error"===t)){var r=this._getMessages();Object.keys(r).forEach(function(t){e._checkLocaleMessage(t,e._warnHtmlInMessage,r[t])})}},Ct.postTranslation.get=function(){return this._postTranslation},Ct.postTranslation.set=function(t){this._postTranslation=t},Ct.sync.get=function(){return this._sync},Ct.sync.set=function(t){this._sync=t},Ot.prototype._getMessages=function(){return this._vm.messages},Ot.prototype._getDateTimeFormats=function(){return this._vm.dateTimeFormats},Ot.prototype._getNumberFormats=function(){return this._vm.numberFormats},Ot.prototype._warnDefault=function(t,e,n,r,i,a){if(!h(n))return n;if(this._missing){var o=this._missing.apply(null,[t,e,r,i]);if(c(o))return o}else 0;if(this._formatFallbackMessages){var s=v.apply(void 0,i);return this._render(e,a,s.params,e)}return e},Ot.prototype._isFallbackRoot=function(t){return(this._fallbackRootWithEmptyString?!t:h(t))&&!h(this._root)&&this._fallbackRoot},Ot.prototype._isSilentFallbackWarn=function(t){return this._silentFallbackWarn instanceof RegExp?this._silentFallbackWarn.test(t):this._silentFallbackWarn},Ot.prototype._isSilentFallback=function(t,e){return this._isSilentFallbackWarn(e)&&(this._isFallbackRoot()||t!==this.fallbackLocale)},Ot.prototype._isSilentTranslationWarn=function(t){return this._silentTranslationWarn instanceof RegExp?this._silentTranslationWarn.test(t):this._silentTranslationWarn},Ot.prototype._interpolate=function(t,e,n,r,i,a,s){if(!e)return null;var l,u=this._path.getPathValue(e,n);if(o(u)||f(u))return u;if(h(u)){if(!f(e))return null;if(l=e[n],!c(l)&&!d(l))return null}else{if(!c(u)&&!d(u))return null;l=u}return c(l)&&(l.indexOf("@:")>=0||l.indexOf("@.")>=0)&&(l=this._link(t,e,l,r,"raw",a,s)),this._render(l,i,a,n)},Ot.prototype._link=function(t,e,n,r,i,a,s){var l=n,c=l.match(bt);for(var u in c)if(c.hasOwnProperty(u)){var p=c[u],f=p.match(wt),h=f[0],d=f[1],v=p.replace(h,"").replace(kt,"");if(y(s,v))return l;s.push(v);var m=this._interpolate(t,e,v,r,"raw"===i?"string":i,"raw"===i?void 0:a,s);if(this._isFallbackRoot(m)){if(!this._root)throw Error("unexpected error");var g=this._root.$i18n;m=g._translate(g._getMessages(),g.locale,g.fallbackLocale,v,r,i,a)}m=this._warnDefault(t,v,m,r,o(a)?a:[a],i),this._modifiers.hasOwnProperty(d)?m=this._modifiers[d](m):St.hasOwnProperty(d)&&(m=St[d](m)),s.pop(),l=m?l.replace(p,m):l}return l},Ot.prototype._createMessageContext=function(t,e,n,r){var i=this,a=o(t)?t:[],l=s(t)?t:{},c=function(t){return a[t]},u=function(t){return l[t]},p=this._getMessages(),f=this.locale;return{list:c,named:u,values:t,formatter:e,path:n,messages:p,locale:f,linked:function(t){return i._interpolate(f,p[f]||{},t,null,r,void 0,[t])}}},Ot.prototype._render=function(t,e,n,r){if(d(t))return t(this._createMessageContext(n,this._formatter||Ft,r,e));var i=this._formatter.interpolate(t,n,r);return i||(i=Ft.interpolate(t,n,r)),"string"!==e||c(i)?i:i.join("")},Ot.prototype._appendItemToChain=function(t,e,n){var r=!1;return y(t,e)||(r=!0,e&&(r="!"!==e[e.length-1],e=e.replace(/!/g,""),t.push(e),n&&n[e]&&(r=n[e]))),r},Ot.prototype._appendLocaleToChain=function(t,e,n){var r,i=e.split("-");do{var a=i.join("-");r=this._appendItemToChain(t,a,n),i.splice(-1,1)}while(i.length&&!0===r);return r},Ot.prototype._appendBlockToChain=function(t,e,n){for(var r=!0,i=0;i<e.length&&l(r);i++){var a=e[i];c(a)&&(r=this._appendLocaleToChain(t,a,n))}return r},Ot.prototype._getLocaleChain=function(t,e){if(""===t)return[];this._localeChainCache||(this._localeChainCache={});var n=this._localeChainCache[t];if(!n){e||(e=this.fallbackLocale),n=[];var r,i=[t];while(o(i))i=this._appendBlockToChain(n,i,e);r=o(e)?e:s(e)?e["default"]?e["default"]:null:e,i=c(r)?[r]:r,i&&this._appendBlockToChain(n,i,null),this._localeChainCache[t]=n}return n},Ot.prototype._translate=function(t,e,n,r,i,a,o){for(var s,l=this._getLocaleChain(e,n),c=0;c<l.length;c++){var u=l[c];if(s=this._interpolate(u,t[u],r,i,a,o,[r]),!h(s))return s}return null},Ot.prototype._t=function(t,e,n,r){var i,a=[],o=arguments.length-4;while(o-- >0)a[o]=arguments[o+4];if(!t)return"";var s=v.apply(void 0,a);this._escapeParameterHtml&&(s.params=O(s.params));var l=s.locale||e,c=this._translate(n,l,this.fallbackLocale,t,r,"string",s.params);if(this._isFallbackRoot(c)){if(!this._root)throw Error("unexpected error");return(i=this._root).$t.apply(i,[t].concat(a))}return c=this._warnDefault(l,t,c,r,a,"string"),this._postTranslation&&null!==c&&void 0!==c&&(c=this._postTranslation(c,t)),c},Ot.prototype.t=function(t){var e,n=[],r=arguments.length-1;while(r-- >0)n[r]=arguments[r+1];return(e=this)._t.apply(e,[t,this.locale,this._getMessages(),null].concat(n))},Ot.prototype._i=function(t,e,n,r,i){var a=this._translate(n,e,this.fallbackLocale,t,r,"raw",i);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.i(t,e,i)}return this._warnDefault(e,t,a,r,[i],"raw")},Ot.prototype.i=function(t,e,n){return t?(c(e)||(e=this.locale),this._i(t,e,this._getMessages(),null,n)):""},Ot.prototype._tc=function(t,e,n,r,i){var a,o=[],s=arguments.length-5;while(s-- >0)o[s]=arguments[s+5];if(!t)return"";void 0===i&&(i=1);var l={count:i,n:i},c=v.apply(void 0,o);return c.params=Object.assign(l,c.params),o=null===c.locale?[c.params]:[c.locale,c.params],this.fetchChoice((a=this)._t.apply(a,[t,e,n,r].concat(o)),i)},Ot.prototype.fetchChoice=function(t,e){if(!t||!c(t))return null;var n=t.split("|");return e=this.getChoiceIndex(e,n.length),n[e]?n[e].trim():t},Ot.prototype.tc=function(t,e){var n,r=[],i=arguments.length-2;while(i-- >0)r[i]=arguments[i+2];return(n=this)._tc.apply(n,[t,this.locale,this._getMessages(),null,e].concat(r))},Ot.prototype._te=function(t,e,n){var r=[],i=arguments.length-3;while(i-- >0)r[i]=arguments[i+3];var a=v.apply(void 0,r).locale||e;return this._exist(n[a],t)},Ot.prototype.te=function(t,e){return this._te(t,this.locale,this._getMessages(),e)},Ot.prototype.getLocaleMessage=function(t){return m(this._vm.messages[t]||{})},Ot.prototype.setLocaleMessage=function(t,e){"warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||this._checkLocaleMessage(t,this._warnHtmlInMessage,e),this._vm.$set(this._vm.messages,t,e)},Ot.prototype.mergeLocaleMessage=function(t,e){"warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||this._checkLocaleMessage(t,this._warnHtmlInMessage,e),this._vm.$set(this._vm.messages,t,k("undefined"!==typeof this._vm.messages[t]&&Object.keys(this._vm.messages[t]).length?Object.assign({},this._vm.messages[t]):{},e))},Ot.prototype.getDateTimeFormat=function(t){return m(this._vm.dateTimeFormats[t]||{})},Ot.prototype.setDateTimeFormat=function(t,e){this._vm.$set(this._vm.dateTimeFormats,t,e),this._clearDateTimeFormat(t,e)},Ot.prototype.mergeDateTimeFormat=function(t,e){this._vm.$set(this._vm.dateTimeFormats,t,k(this._vm.dateTimeFormats[t]||{},e)),this._clearDateTimeFormat(t,e)},Ot.prototype._clearDateTimeFormat=function(t,e){for(var n in e){var r=t+"__"+n;this._dateTimeFormatters.hasOwnProperty(r)&&delete this._dateTimeFormatters[r]}},Ot.prototype._localizeDateTime=function(t,e,n,r,i,a){for(var o=e,s=r[o],l=this._getLocaleChain(e,n),c=0;c<l.length;c++){var u=l[c];if(s=r[u],o=u,!h(s)&&!h(s[i]))break}if(h(s)||h(s[i]))return null;var p,f=s[i];if(a)p=new Intl.DateTimeFormat(o,Object.assign({},f,a));else{var d=o+"__"+i;p=this._dateTimeFormatters[d],p||(p=this._dateTimeFormatters[d]=new Intl.DateTimeFormat(o,f))}return p.format(t)},Ot.prototype._d=function(t,e,n,r){if(!n){var i=r?new Intl.DateTimeFormat(e,r):new Intl.DateTimeFormat(e);return i.format(t)}var a=this._localizeDateTime(t,e,this.fallbackLocale,this._getDateTimeFormats(),n,r);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.d(t,n,e)}return a||""},Ot.prototype.d=function(t){var e=[],n=arguments.length-1;while(n-- >0)e[n]=arguments[n+1];var i=this.locale,a=null,o=null;return 1===e.length?(c(e[0])?a=e[0]:s(e[0])&&(e[0].locale&&(i=e[0].locale),e[0].key&&(a=e[0].key)),o=Object.keys(e[0]).reduce(function(t,n){var i;return y(r,n)?Object.assign({},t,(i={},i[n]=e[0][n],i)):t},null)):2===e.length&&(c(e[0])&&(a=e[0]),c(e[1])&&(i=e[1])),this._d(t,i,a,o)},Ot.prototype.getNumberFormat=function(t){return m(this._vm.numberFormats[t]||{})},Ot.prototype.setNumberFormat=function(t,e){this._vm.$set(this._vm.numberFormats,t,e),this._clearNumberFormat(t,e)},Ot.prototype.mergeNumberFormat=function(t,e){this._vm.$set(this._vm.numberFormats,t,k(this._vm.numberFormats[t]||{},e)),this._clearNumberFormat(t,e)},Ot.prototype._clearNumberFormat=function(t,e){for(var n in e){var r=t+"__"+n;this._numberFormatters.hasOwnProperty(r)&&delete this._numberFormatters[r]}},Ot.prototype._getNumberFormatter=function(t,e,n,r,i,a){for(var o=e,s=r[o],l=this._getLocaleChain(e,n),c=0;c<l.length;c++){var u=l[c];if(s=r[u],o=u,!h(s)&&!h(s[i]))break}if(h(s)||h(s[i]))return null;var p,f=s[i];if(a)p=new Intl.NumberFormat(o,Object.assign({},f,a));else{var d=o+"__"+i;p=this._numberFormatters[d],p||(p=this._numberFormatters[d]=new Intl.NumberFormat(o,f))}return p},Ot.prototype._n=function(t,e,n,r){if(!Ot.availabilities.numberFormat)return"";if(!n){var i=r?new Intl.NumberFormat(e,r):new Intl.NumberFormat(e);return i.format(t)}var a=this._getNumberFormatter(t,e,this.fallbackLocale,this._getNumberFormats(),n,r),o=a&&a.format(t);if(this._isFallbackRoot(o)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.n(t,Object.assign({},{key:n,locale:e},r))}return o||""},Ot.prototype.n=function(t){var e=[],r=arguments.length-1;while(r-- >0)e[r]=arguments[r+1];var i=this.locale,a=null,o=null;return 1===e.length?c(e[0])?a=e[0]:s(e[0])&&(e[0].locale&&(i=e[0].locale),e[0].key&&(a=e[0].key),o=Object.keys(e[0]).reduce(function(t,r){var i;return y(n,r)?Object.assign({},t,(i={},i[r]=e[0][r],i)):t},null)):2===e.length&&(c(e[0])&&(a=e[0]),c(e[1])&&(i=e[1])),this._n(t,i,a,o)},Ot.prototype._ntp=function(t,e,n,r){if(!Ot.availabilities.numberFormat)return[];if(!n){var i=r?new Intl.NumberFormat(e,r):new Intl.NumberFormat(e);return i.formatToParts(t)}var a=this._getNumberFormatter(t,e,this.fallbackLocale,this._getNumberFormats(),n,r),o=a&&a.formatToParts(t);if(this._isFallbackRoot(o)){if(!this._root)throw Error("unexpected error");return this._root.$i18n._ntp(t,e,n,r)}return o||[]},Object.defineProperties(Ot.prototype,Ct),Object.defineProperty(Ot,"availabilities",{get:function(){if(!_t){var t="undefined"!==typeof Intl;_t={dateTimeFormat:t&&"undefined"!==typeof Intl.DateTimeFormat,numberFormat:t&&"undefined"!==typeof Intl.NumberFormat}}return _t}}),Ot.install=z,Ot.version="8.28.2",e.A=Ot},97073:function(t,e,n){"use strict";var r=n(85471);function i(t){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach(function(e){a(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var l=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(n,r){if("undefined"!==typeof document){var i=document.head||document.getElementsByTagName("head")[0],a=document.createElement("script");if(a.async=!0,a.src=t,a.defer=e.defer,e.preconnectOrigin){var o=document.createElement("link");o.href=e.preconnectOrigin,o.rel="preconnect",i.appendChild(o)}i.appendChild(a),a.onload=n,a.onerror=r}})},c=function(t){return"function"===typeof t},u=function(t){return t&&"object"===i(t)&&!Array.isArray(t)},p=function t(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];if(!r.length)return e;var o=r.shift();if(u(e)&&u(o)){for(var s in o)u(o[s])?(e[s]||Object.assign(e,a({},s,{})),t(e[s],o[s])):Object.assign(e,a({},s,o[s]));return t.apply(void 0,[e].concat(r))}},f=function(){return"undefined"!==typeof window&&"undefined"!==typeof document},h=function(t){f()},d=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h('Missing "appName" property inside the plugin options.',null==t.app_name),h('Missing "name" property in the route.',null==t.screen_name),t};function v(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=t.split("/"),r=e.split("/");return""===n[0]&&"/"===e[e.length-1]&&n.shift(),r.join("/")+n.join("/")}var m,g=function(){return{bootstrap:!0,onReady:null,onError:null,onBeforeTrack:null,onAfterTrack:null,pageTrackerTemplate:null,customResourceURL:"https://www.googletagmanager.com/gtag/js",customPreconnectOrigin:"https://www.googletagmanager.com",deferScriptLoad:!1,pageTrackerExcludedRoutes:[],pageTrackerEnabled:!0,enabled:!0,disableScriptLoad:!1,pageTrackerScreenviewEnabled:!1,appName:null,pageTrackerUseFullPath:!1,pageTrackerPrependBase:!0,pageTrackerSkipSamePath:!0,globalDataLayerName:"dataLayer",globalObjectName:"gtag",defaultGroupName:"default",includes:null,config:{id:null,params:{send_page_view:!1}}}},_={},y=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=g();_=p(e,t)},b=function(){return _},w=function(){var t,e=b(),n=e.globalObjectName;f()&&"undefined"!==typeof window[n]&&(t=window)[n].apply(t,arguments)},k=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=b(),i=r.config,a=r.includes;w.apply(void 0,["config",i.id].concat(e)),Array.isArray(a)&&a.forEach(function(t){w.apply(void 0,["config",t.id].concat(e))})},S=function(t,e){f()&&(window["ga-disable-".concat(t)]=e)},F=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=b(),n=e.config,r=e.includes;S(n.id,t),Array.isArray(r)&&r.forEach(function(e){return S(e.id,t)})},O=function(){F(!0)},C=function(){F(!1)},T=function(t){m=t},$=function(){return m},I=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=b(),r=n.includes,i=n.defaultGroupName;null==e.send_to&&Array.isArray(r)&&r.length&&(e.send_to=r.map(function(t){return t.id}).concat(i)),w("event",t,e)},D=function(t){if(f()){var e;if("string"===typeof t)e={page_path:t};else if(t.path||t.fullPath){var n=b(),r=n.pageTrackerUseFullPath,i=n.pageTrackerPrependBase,a=$(),o=a&&a.options.base,l=r?t.fullPath:t.path;e=s(s({},t.name&&{page_title:t.name}),{},{page_path:i?v(l,o):l})}else e=t;null==e.page_location&&(e.page_location=window.location.href),null==e.send_page_view&&(e.send_page_view=!0),I("page_view",e)}},M=function(t){var e,n=b(),r=n.appName;t&&(e="string"===typeof t?{screen_name:t}:t,e.app_name=e.app_name||r,I("screen_view",e))},j=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];I.apply(void 0,["exception"].concat(e))},L=function(t){k("linker",t)},x=function(t){I("timing_complete",t)},N=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];w.apply(void 0,["set"].concat(e))},E=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];I.apply(void 0,["refund"].concat(e))},P=function(t){I("purchase",t)},A=function(t){k({custom_map:t})},R=Object.freeze({__proto__:null,query:w,config:k,optOut:O,optIn:C,pageview:D,screenview:M,exception:j,linker:L,time:x,set:N,refund:E,purchase:P,customMap:A,event:I}),W=function(t){return t.$gtag=t.prototype.$gtag=R},B=function(){if(f()){var t=b(),e=t.enabled,n=t.globalObjectName,r=t.globalDataLayerName;return null==window[n]&&(window[r]=window[r]||[],window[n]=function(){window[r].push(arguments)}),window[n]("js",new Date),e||O(),window[n]}},U=function(t){return s({send_page_view:!1},t)},H=function(){var t=b(),e=t.config,n=t.includes;w("config",e.id,U(e.params)),Array.isArray(n)&&n.forEach(function(t){w("config",t.id,U(t.params))})},V=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=b(),r=n.appName,i=n.pageTrackerTemplate,a=n.pageTrackerScreenviewEnabled,o=n.pageTrackerSkipSamePath;if(!o||t.path!==e.path){var s=t;c(i)?s=i(t,e):a&&(s=d({app_name:r,screen_name:t.name})),a?M(s):D(s)}},z=function(t){var e=b(),n=e.pageTrackerExcludedRoutes;return n.includes(t.path)||n.includes(t.name)},q=function(){var t=b(),e=t.onBeforeTrack,n=t.onAfterTrack,i=$();i.onReady(function(){r["default"].nextTick().then(function(){var t=i.currentRoute;H(),z(t)||V(t)}),i.afterEach(function(t,i){r["default"].nextTick().then(function(){z(t)||(c(e)&&e(t,i),V(t,i),c(n)&&n(t,i))})})})},G=function(){var t=b(),e=t.onReady,n=t.onError,r=t.globalObjectName,i=t.globalDataLayerName,a=t.config,o=t.customResourceURL,s=t.customPreconnectOrigin,c=t.deferScriptLoad,u=t.pageTrackerEnabled,p=t.disableScriptLoad,f=Boolean(u&&$());if(B(),f?q():H(),!p)return l("".concat(o,"?id=").concat(a.id,"&l=").concat(i),{preconnectOrigin:s,defer:c}).then(function(){e&&e(window[r])}).catch(function(t){return n&&n(t),t})},Z=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;W(t),y(e),T(n),b().bootstrap&&G()};e.Ay=Z}}]);