"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[404],{28:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Pagar","payButton.redirecting":"Redirecionando...","payButton.with":"Pague %{value} com %{maskedData}","payButton.saveDetails":"Salvar informações",close:"Fechar",storeDetails:"Salvar para meu próximo pagamento",readMore:"Leia mais","creditCard.holderName":"Nome no cartão","creditCard.holderName.placeholder":"<PERSON><PERSON>","creditCard.holderName.invalid":"Digite o nome conforme mostrado no cartão","creditCard.numberField.title":"Número do cartão","creditCard.expiryDateField.title":"Data de validade","creditCard.expiryDateField.placeholder":"MM/AA","creditCard.expiryDateField.month":"Mês","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"AA","creditCard.expiryDateField.year":"Ano","creditCard.cvcField.title":"Código de segurança","creditCard.storeDetailsButton":"Lembrar para a próxima vez","creditCard.cvcField.placeholder.4digits":"4 dígitos","creditCard.cvcField.placeholder.3digits":"3 dígitos","creditCard.taxNumber.placeholder":"AAAMMDD / 0123456789",installments:"Opções de Parcelamento",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} meses","installments.oneTime":"Pagamento à vista","installments.installments":"Pagamento parcelado","installments.revolving":"Pagamento rotativo","sepaDirectDebit.ibanField.invalid":"Número de conta inválido","sepaDirectDebit.nameField.placeholder":"J. Silva","sepa.ownerName":"Nome do titular da conta bancária","sepa.ibanNumber":"Número de conta (NIB)","error.title":"Erro","error.subtitle.redirect":"Falha no redirecionamento","error.subtitle.payment":"Falha no pagamento","error.subtitle.refused":"Pagamento recusado","error.message.unknown":"Ocorreu um erro desconhecido","errorPanel.title":"Erros existentes","idealIssuer.selectField.title":"Banco","idealIssuer.selectField.placeholder":"Selecione seu banco","creditCard.success":"Pagamento bem-sucedido",loading:"Carregando...",continue:"Continuar",continueTo:"Continuar para","wechatpay.timetopay":"Você tem %@ para pagar","sr.wechatpay.timetopay":"Você tem %#minutos%# e %#segundos%# para pagar","wechatpay.scanqrcode":"Escanear código QR",personalDetails:"Informações pessoais",companyDetails:"Dados da empresa","companyDetails.name":"Nome da empresa","companyDetails.registrationNumber":"Número de registro",socialSecurityNumber:"CPF",firstName:"Nome","firstName.invalid":"Digite seu nome",infix:"Prefixo",lastName:"Sobrenome","lastName.invalid":"Digite seu sobrenome",mobileNumber:"Celular","mobileNumber.invalid":"Número de celular inválido",city:"Cidade",postalCode:"CEP","postalCode.optional":"Código postal (opcional)",countryCode:"Código do país",telephoneNumber:"Número de telefone",dateOfBirth:"Data de nascimento",shopperEmail:"Endereço de e-mail",gender:"Gênero","gender.notselected":"Selecione seu gênero",male:"Masculino",female:"Feminino",billingAddress:"Endereço de cobrança",street:"Rua",stateOrProvince:"Estado ou província",country:"País/região",houseNumberOrName:"Número da casa",separateDeliveryAddress:"Especificar um endereço de entrega separado",deliveryAddress:"Endereço de entrega","deliveryAddress.firstName":"Nome do destinatário","deliveryAddress.lastName":"Sobrenome do destinatário",zipCode:"Código postal",apartmentSuite:"Apartamento/Conjunto",provinceOrTerritory:"Província ou território",cityTown:"Cidade",address:"Endereço","address.placeholder":"Encontre seu endereço","address.errors.incomplete":"Insira um endereço para continuar","address.enterManually":"Inserir endereço manualmente",state:"Estado","field.title.optional":"(opcional)","creditCard.cvcField.title.optional":"Código de segurança (opcional)","issuerList.wallet.placeholder":"Selecione uma carteira",privacyPolicy:"Política de Privacidade","afterPay.agreement":"Concordo com o %@ da Riverty","riverty.termsAndConditions":"Concordo com os %#Termos e Condições%# gerais do método de pagamento Riverty. A política de privacidade da Riverty de encontra %#aqui%#.",paymentConditions:"condições de pagamento",openApp:"Abrir o aplicativo","voucher.readInstructions":"Leia as instruções","voucher.introduction":"Obrigado pela sua compra, use o cupom a seguir para concluir o seu pagamento.","voucher.expirationDate":"Data de validade","voucher.alternativeReference":"Referência alternativa","dragonpay.voucher.non.bank.selectField.placeholder":"Selecione o seu fornecedor","dragonpay.voucher.bank.selectField.placeholder":"Selecione seu banco","voucher.paymentReferenceLabel":"Referência de pagamento","voucher.surcharge":"Inclui %@ de sobretaxa","voucher.introduction.doku":"Obrigado pela sua compra, use a informação a seguir para concluir o seu pagamento.","voucher.shopperName":"Nome do consumidor","voucher.merchantName":"Comerciante online","voucher.introduction.econtext":"Obrigado pela sua compra, use a informação a seguir para concluir o seu pagamento.","voucher.telephoneNumber":"Número de telefone","voucher.shopperReference":"Referência do consumidor","voucher.collectionInstitutionNumber":"Número da instituição de cobrança","voucher.econtext.telephoneNumber.invalid":"O número de telefone deve ter 10 ou 11 dígitos","boletobancario.btnLabel":"Gerar Boleto","boleto.sendCopyToEmail":"Enviar uma cópia por e-mail","button.copy":"Copiar","button.download":"Baixar","boleto.socialSecurityNumber.invalid":"Digite um número de CPF/CNPJ válido","creditCard.storedCard.description.ariaLabel":"O cartão armazenado termina em %@","voucher.entity":"Entidade",donateButton:"Doar",notNowButton:"Agora não",thanksForYourSupport:"Obrigado pelo apoio!","resultMessages.preauthorized":"Informações salvas",preauthorizeWith:"Pré-autorizar com",confirmPreauthorization:"Confirmar pré-autorização",confirmPurchase:"Confirmar compra",applyGiftcard:"Resgatar",giftcardBalance:"Saldo do vale-presente",deductedBalance:"Saldo debitado","creditCard.pin.title":"Pin","creditCard.encryptedPassword.label":"Primeiros dois dígitos da senha do cartão","creditCard.encryptedPassword.invalid":"Senha inválida","creditCard.taxNumber":"Data de nascimento do titular do cartão ou número de registro corporativo","creditCard.taxNumber.label":"Data de nascimento do titular do cartão (AAMMDD) ou número de registro corporativo (10 dígitos)","creditCard.taxNumber.labelAlt":"Número de registro corporativo (10 dígitos)","creditCard.taxNumber.invalid":"Data de nascimento do titular do cartão ou número de registro corporativo inválidos","storedPaymentMethod.disable.button":"Remover","storedPaymentMethod.disable.confirmation":"Remover método de pagamento armazenado","storedPaymentMethod.disable.confirmButton":"Sim, remover","storedPaymentMethod.disable.cancelButton":"Cancelar","ach.bankAccount":"Conta bancária","ach.accountHolderNameField.title":"Nome do titular da conta","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Nome do titular da conta inválido","ach.accountNumberField.title":"Número da conta","ach.accountNumberField.invalid":"Número de conta inválido","ach.accountLocationField.title":"Número de roteamento ABA","ach.accountLocationField.invalid":"Número de roteamento ABA inválido","ach.savedBankAccount":"Conta bancária cadastrada","ach.savings":"Conta poupança","ach.checking":"Conta corrente","select.state":"Selecionar estado","select.stateOrProvince":"Selecione estado ou província","select.provinceOrTerritory":"Selecionar província ou território","select.country":"Selecione o país/região","select.noOptionsFound":"Nenhuma opção encontrada","select.filter.placeholder":"Pesquisar...","telephoneNumber.invalid":"Número de telefone inválido",qrCodeOrApp:"ou","paypal.processingPayment":"Processando pagamento...",generateQRCode:"Gerar código QR","await.waitForConfirmation":"Aguardando confirmação","mbway.confirmPayment":"Confirme seu pagamento no aplicativo MB WAY","shopperEmail.invalid":"Endereço de e-mail inválido","dateOfBirth.format":"DD/MM/AAAA","dateOfBirth.invalid":"Insira uma data de nascimento válida que indique que você tem pelo menos 18 anos","blik.confirmPayment":"Abra o aplicativo do seu banco para confirmar o pagamento.","blik.invalid":"Digite 6 números","blik.code":"Código de 6 dígitos","blik.help":"Obtenha o código no aplicativo do seu banco.","swish.pendingMessage":"Depois de escanear o QR, o status pode ficar pendente por até 10 minutos. Não tente refazer o pagamento antes desse período para evitar cobrança duplicada.","field.valid":"Campo válido","field.invalid":"Campo inválido","error.va.gen.01":"Campo incompleto","error.va.gen.02":"Campo inválido","error.va.sf-cc-num.01":"Digite um número de cartão válido","error.va.sf-cc-num.02":"Digite o número do cartão","error.va.sf-cc-num.03":"Digite uma bandeira de cartão aceita","error.va.sf-cc-num.04":"Digite o número completo do cartão","error.va.sf-cc-dat.01":"Digite uma data válida","error.va.sf-cc-dat.02":"Digite uma data válida","error.va.sf-cc-dat.03":"Cartão de crédito prestes a vencer","error.va.sf-cc-dat.04":"Digite a data de validade","error.va.sf-cc-dat.05":"Digite a data de validade completa","error.va.sf-cc-mth.01":"Digite o mês de validade","error.va.sf-cc-yr.01":"Digite o ano de validade","error.va.sf-cc-yr.02":"Digite o ano de validade completo","error.va.sf-cc-cvc.01":"Digite o código de segurança","error.va.sf-cc-cvc.02":"Digite o código de segurança completo","error.va.sf-ach-num.01":"O campo do número da conta bancária está vazio","error.va.sf-ach-num.02":"O número da conta bancária tem o comprimento errado","error.va.sf-ach-loc.01":"O campo do número de identificação do banco está vazio","error.va.sf-ach-loc.02":"O número de identificação do banco tem o comprimento errado","error.va.sf-kcp-pwd.01":"O campo da senha está vazio","error.va.sf-kcp-pwd.02":"A senha tem o comprimento errado","error.giftcard.no-balance":"Este vale-presente tem saldo zero","error.giftcard.card-error":"Não existe um vale-presente com esse número em nossos registros","error.giftcard.currency-error":"Os vales-presente são válidos somente na moeda em que foram emitidos","amazonpay.signout":"Sair do Amazon","amazonpay.changePaymentDetails":"Alterar dados de pagamento","partialPayment.warning":"Selecione outro método de pagamento para pagar o restante","partialPayment.remainingBalance":"O saldo restante será %{amount}","bankTransfer.beneficiary":"Beneficiário","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referência","bankTransfer.introduction":"Continue criando o novo pagamento por transferência bancária. Use as informações na tela a seguir para concluí-lo.","bankTransfer.instructions":"Obrigado pela sua compra, use a informação a seguir para concluir o seu pagamento.","bacs.accountHolderName":"Nome do titular da conta bancária","bacs.accountHolderName.invalid":"Nome do titular da conta bancária inválido","bacs.accountNumber":"Número da conta bancária","bacs.accountNumber.invalid":"Número da conta bancária inválido","bacs.bankLocationId":"Código de classificação","bacs.bankLocationId.invalid":"Código de classificação inválido","bacs.consent.amount":"Concordo que o valor acima seja deduzido da minha conta bancária.","bacs.consent.account":"Confirmo que a conta está em meu nome e que sou o único signatário que deve autorizar o débito direto nessa conta.",edit:"Editar","bacs.confirm":"Confirmar e pagar","bacs.result.introduction":"Baixar instrução de débito direto (DDI)","download.pdf":"Baixar PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe para número do cartão","creditCard.encryptedCardNumber.aria.label":"Número do cartão","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe para data de validade","creditCard.encryptedExpiryDate.aria.label":"Data de validade","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe para mês de validade","creditCard.encryptedExpiryMonth.aria.label":"Mês de vencimento","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe para ano de validade","creditCard.encryptedExpiryYear.aria.label":"Ano de vencimento","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe para código de segurança","creditCard.encryptedSecurityCode.aria.label":"Código de segurança","creditCard.encryptedPassword.aria.iframeTitle":"Iframe para senha","creditCard.encryptedPassword.aria.label":"Primeiros dois dígitos da senha do cartão","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe para número do cartão","giftcard.encryptedCardNumber.aria.label":"Número do cartão","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe para pin","giftcard.encryptedSecurityCode.aria.label":"Pin",giftcardTransactionLimit:"Máximo de %{amount} permitido por transação neste cartão-presente","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe para número da conta bancária","ach.encryptedBankAccountNumber.aria.label":"Número da conta","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe para número de identificação do banco","ach.encryptedBankLocationId.aria.label":"Número de roteamento ABA","pix.instructions":"Abra o app com sua chave PIX cadastrada, escolha Pagar com Pix e escaneie o QR Code ou copie e cole o código","twint.saved":"salvo",orPayWith:"ou pague com",invalidFormatExpects:"Formato inválido. Formato esperado: %{format}","upi.qrCodeWaitingMessage":"Escaneie o QR code com o aplicativo UPI de sua preferência para concluir o pagamento","upi.vpaWaitingMessage":"Abra o aplicativo UPI para confirmar o pagamento","upi.modeSelection":"Como você gostaria de usar a UPI?","upi.completePayment":"Conclua seu pagamento","upi.mode.enterUpiId":"Digite o ID UPI","upi.mode.qrCode":"Código QR","upi.mode.payByAnyUpi":"Pagar em qualquer aplicativo UPI","upi.collect.dropdown.label":"Informe o ID da UPI","upi.collect.field.label":"Digite o ID UPI/VPA","onlineBanking.termsAndConditions":"Ao continuar, você concorda com os %#termos e condições%#","onlineBankingPL.termsAndConditions":"Ao continuar, você concorda com as %#condições%# e as %#informações obrigatórias%# da Przelewy24","ctp.loading.poweredByCtp":"Desenvolvido por Click to Pay","ctp.loading.intro":"Estamos verificando se você tem algum cartão salvo no Click to Pay...","ctp.login.title":"Continuar para o Click to Pay","ctp.login.subtitle":"Digite o endereço de e-mail associado ao Click to Pay para continuar.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Não é você?","ctp.logout.notYourCards":"Não é o seu cartão?","ctp.logout.notYourCard":"Não é o seu cartão?","ctp.logout.notYourProfile":"Não é o seu perfil?","ctp.otp.fieldLabel":"Código de acesso único","ctp.otp.resendCode":"Reenviar código","ctp.otp.codeResent":"Código reenviado","ctp.otp.title":"Acesse seus cartões Click to Pay","ctp.otp.subtitle":"Digite o código %@ enviado para %@ para confirmar que é você.","ctp.otp.saveCookiesCheckbox.label":"Ignorar verificação da próxima vez","ctp.otp.saveCookiesCheckbox.information":"Selecione esta opção para ser lembrado no seu dispositivo e navegador, agilizando o pagamento nas lojas participantes. Não recomendado para dispositivos compartilhados.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Selecione para ser lembrado em seu dispositivo e navegador","ctp.emptyProfile.message":"Não há nenhum cartão cadastrado neste perfil do Click to Pay","ctp.separatorText":"ou use","ctp.cards.title":"Concluir o pagamento com o Click to Pay","ctp.cards.subtitle":"Selecione um cartão para usar.","ctp.cards.expiredCard":"Vencido","ctp.manualCardEntry":"Entrada manual do cartão","ctp.aria.infoModalButton":"O que é Click to Pay","ctp.infoPopup.title":"O Click to Pay traz a facilidade do pagamento online sem contato","ctp.infoPopup.subtitle":"Um método de pagamento rápido e seguro, compatível com Mastercard, Visa e outros cartões.","ctp.infoPopup.benefit1":"O Click to Pay usa criptografia para manter suas informações seguras e protegidas","ctp.infoPopup.benefit2":"Use com comerciantes no mundo todo","ctp.infoPopup.benefit3":"Configure apenas uma vez e simplifique seus pagamentos no futuro","ctp.errors.AUTH_INVALID":"Autenticação inválida","ctp.errors.NOT_FOUND":"Nenhuma conta foi encontrada. Informe um e-mail válido ou continue inserindo os dados do cartão manualmente","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formato não compatível","ctp.errors.FRAUD":"A conta do usuário foi bloqueada ou desativada","ctp.errors.CONSUMER_ID_MISSING":"A identidade do consumidor está ausente na solicitação","ctp.errors.ACCT_INACCESSIBLE":"Esta conta não está disponível no momento. Ela pode estar bloqueada, por exemplo","ctp.errors.CODE_INVALID":"Código de verificação incorreto","ctp.errors.CODE_EXPIRED":"Esse código expirou","ctp.errors.RETRIES_EXCEEDED":"O limite de tentativas para gerar a senha de uso único foi excedido","ctp.errors.OTP_SEND_FAILED":"Não foi possível enviar a senha de uso único ao destinatário","ctp.errors.REQUEST_TIMEOUT":"Algo deu errado. Tente de novo ou insira os dados do cartão manualmente","ctp.errors.UNKNOWN_ERROR":"Algo deu errado. Tente de novo ou insira os dados do cartão manualmente","ctp.errors.SERVICE_ERROR":"Algo deu errado. Tente de novo ou insira os dados do cartão manualmente","ctp.errors.SERVER_ERROR":"Algo deu errado. Tente de novo ou insira os dados do cartão manualmente","ctp.errors.INVALID_PARAMETER":"Algo deu errado. Tente de novo ou insira os dados do cartão manualmente","ctp.errors.AUTH_ERROR":"Algo deu errado. Tente de novo ou insira os dados do cartão manualmente","paymentMethodsList.aria.label":"Escolha um método de pagamento","companyDetails.name.invalid":"Digite o nome da empresa","companyDetails.registrationNumber.invalid":"Digite o número de registro","consent.checkbox.invalid":"Você precisa concordar com os termos e condições","form.instruction":"Todos os campos são obrigatórios, a menos que marcados em contrário.","trustly.descriptor":"Pagamento instantâneo","trustly.description1":"Pague diretamente de qualquer conta, com a segurança do seu banco","trustly.description2":"Sem cartão, sem download de aplicativo, sem cadastro","ancv.input.label":"Sua identificação da ANCV","ancv.confirmPayment":"Use o aplicativo da ANCV para confirmar o pagamento.","ancv.form.instruction":"É necessário ter o aplicativo da Cheque-Vacances para validar este pagamento.","ancv.beneficiaryId.invalid":"Insira um endereço de e-mail válido ou uma ID da ANCV","payme.openPayMeApp":"Para concluir o pagamento no PayMe, autorize-o no aplicativo e aguarde a confirmação.","payme.redirectButtonLabel":"Abrir o aplicativo PayMe","payme.scanQrCode":"Concluir o pagamento com um código QR","payme.timeToPay":"Este QR code é válido para %@","payme.instructions.steps":"Abra o aplicativo PayMe.%@Escaneie o QR code para autorizar o pagamento.%@Conclua o pagamento no aplicativo e aguarde a confirmação.","payme.instructions.footnote":"Não feche esta página antes de concluir o pagamento","payByBankAISDD.disclaimer.header":"Use o Pay by Bank para pagar instantaneamente de qualquer conta bancária.","payByBankAISDD.disclaimer.body":"Ao conectar sua conta bancária, você autoriza débitos dessa conta de qualquer valor devido pelo uso de nossos serviços e/ou compra de nossos produtos até que esta autorização seja revogada.","paymentMethodBrand.other":"outro"}},1455:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Betala","payButton.redirecting":"Omdirigerar…","payButton.with":"Betala %{value} med %{maskedData}","payButton.saveDetails":"Spara information",close:"Stäng",storeDetails:"Spara till min nästa betalning",readMore:"Läs mer","creditCard.holderName":"Namn på kort","creditCard.holderName.placeholder":"A. Andersson","creditCard.holderName.invalid":"Ange namnet som det står på kortet","creditCard.numberField.title":"Kortnummer","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Utgångsdatum","creditCard.expiryDateField.placeholder":"MM/ÅÅ","creditCard.expiryDateField.month":"Månad","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"ÅÅ","creditCard.expiryDateField.year":"År","creditCard.cvcField.title":"Säkerhetskod","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Spara kortuppgifter","creditCard.cvcField.placeholder.4digits":"4 siffror","creditCard.cvcField.placeholder.3digits":"3 siffror","creditCard.taxNumber.placeholder":"ÅÅMMDD / 0123456789",installments:"Antal delbetalningar",installmentOption:"%{times} x %{partialValue}",installmentOptionMonths:"%{times} månader","installments.oneTime":"Engångsbetalning","installments.installments":"Delbetalningar","installments.revolving":"Uppdelad betalning","sepaDirectDebit.ibanField.invalid":"Ogiltigt kontonummer","sepaDirectDebit.nameField.placeholder":"A. Andersson","sepa.ownerName":"Känt av kontoinnehavaren","sepa.ibanNumber":"Kontonummer (IBAN)","error.title":"Fel","error.subtitle.redirect":"Omdirigering misslyckades","error.subtitle.payment":"Betalning misslyckades","error.subtitle.refused":"Betalning avvisad","error.message.unknown":"Ett okänt fel uppstod","errorPanel.title":"Befintliga fel","idealIssuer.selectField.title":"Bank","idealIssuer.selectField.placeholder":"Välj din bank","creditCard.success":"Betalning lyckades",loading:"Laddar…",continue:"Fortsätt",continueTo:"Fortsätt till","wechatpay.timetopay":"Du har %@ att betala","sr.wechatpay.timetopay":"Du har %#minuter%# och %#sekunder%# på dig att betala","wechatpay.scanqrcode":"Skanna QR-kod",personalDetails:"Personuppgifter",companyDetails:"Företagsinformation","companyDetails.name":"Företagsnamn","companyDetails.registrationNumber":"Registreringsnummer",socialSecurityNumber:"Personnummer",firstName:"Förnamn","firstName.invalid":"Ange ditt förnamn",infix:"Prefix",lastName:"Efternamn","lastName.invalid":"Ange ditt efternamn",mobileNumber:"Mobilnummer","mobileNumber.invalid":"Ogiltigt mobilnummer",city:"Stad",postalCode:"Postnummer","postalCode.optional":"Postnummer (valfritt)",countryCode:"Landskod",telephoneNumber:"Telefonnummer",dateOfBirth:"Födelsedatum",shopperEmail:"E-postadress",gender:"Kön","gender.notselected":"Välj kön",male:"Man",female:"Kvinna",billingAddress:"Faktureringsadress",street:"Gatuadress",stateOrProvince:"Delstat eller region",country:"Land/region",houseNumberOrName:"Husnummer",separateDeliveryAddress:"Ange en separat leveransadress",deliveryAddress:"Leveransadress","deliveryAddress.firstName":"Mottagarens förnamn","deliveryAddress.lastName":"Mottagarens efternamn",zipCode:"Postnummer",apartmentSuite:"Lägenhetsnummer",provinceOrTerritory:"Provins eller territorium",cityTown:"Ort",address:"Adress","address.placeholder":"Hitta din adress","address.errors.incomplete":"Ange en adress för att fortsätta","address.enterManually":"Ange adress manuellt",state:"Delstat","field.title.optional":"(valfritt)","creditCard.cvcField.title.optional":"Säkerhetskod (valfritt)","issuerList.wallet.placeholder":"Väj din plånbok",privacyPolicy:"Sekretesspolicy","afterPay.agreement":"Jag godkänner Rivertys %@","riverty.termsAndConditions":"Jag godkänner de allmänna %#villkoren%# för betalningssättet Riverty. Rivertys sekretesspolicy finns %#här%#.",paymentConditions:"betalvillkor",openApp:"Öppna appen","voucher.readInstructions":"Läs instruktionerna","voucher.introduction":"Tack för ditt köp, vänligen använd följande kupong för att slutföra din betalning.","voucher.expirationDate":"Utgångsdatum","voucher.alternativeReference":"Alternativ referens","dragonpay.voucher.non.bank.selectField.placeholder":"Välj din leverantör","dragonpay.voucher.bank.selectField.placeholder":"Välj din bank","voucher.paymentReferenceLabel":"Betalreferens","voucher.surcharge":"Inklusive %@ i avgift","voucher.introduction.doku":"Tack för ditt köp, vänligen använd följande information för att slutföra din betalning.","voucher.shopperName":"Konsumentens namn","voucher.merchantName":"Handlare","voucher.introduction.econtext":"Tack för ditt köp, vänligen använd följande information för att slutföra din betalning.","voucher.telephoneNumber":"Telefonnummer","voucher.shopperReference":"Köparreferens","voucher.collectionInstitutionNumber":"Nummer på upphämtningsplats","voucher.econtext.telephoneNumber.invalid":"Telefonnumret måste innehålla 10 eller 11 siffror","boletobancario.btnLabel":"Generera Boleto","boleto.sendCopyToEmail":"Skicka en kopia till min e-post","button.copy":"Kopiera","button.download":"Ladda ner","boleto.socialSecurityNumber.invalid":"Ange ett giltigt CPF-/CNPJ-nummer","creditCard.storedCard.description.ariaLabel":"Sparat kort slutar på %@","voucher.entity":"Enhet",donateButton:"Donera",notNowButton:"Inte nu",thanksForYourSupport:"Tack för ditt stöd!","resultMessages.preauthorized":"Information sparad",preauthorizeWith:"Förauktorisera med",confirmPreauthorization:"Bekräfta förauktorisering",confirmPurchase:"Bekräfta köp",applyGiftcard:"Lös in",giftcardBalance:"Presentkortssaldo",deductedBalance:"Avdraget saldo","creditCard.pin.title":"PIN-kod","creditCard.encryptedPassword.label":"De två första siffrorna i kortets lösenord","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Ogiltigt lösenord","creditCard.taxNumber":"Kortinnehavarens födelsedatum eller företagets organisationsnummer","creditCard.taxNumber.label":"Kortinnehavarens födelsedatum (ÅÅMMDD) eller företagets organisationsnummer (10 siffror)","creditCard.taxNumber.labelAlt":"Företagets organisationsnummer (10 siffror)","creditCard.taxNumber.invalid":"Ogiltigt födelsedatum eller organisationsnummer","storedPaymentMethod.disable.button":"Ta bort","storedPaymentMethod.disable.confirmation":"Ta bort sparat betalningssätt","storedPaymentMethod.disable.confirmButton":"Ja, ta bort","storedPaymentMethod.disable.cancelButton":"Avbryt","ach.bankAccount":"Bankkonto","ach.accountHolderNameField.title":"Kontoinnehavarens namn","ach.accountHolderNameField.placeholder":"A. Andersson","ach.accountHolderNameField.invalid":"Kontoinnehavarens namn är ogiltigt","ach.accountNumberField.title":"Kontonummer","ach.accountNumberField.invalid":"Ogiltigt kontonummer","ach.accountLocationField.title":"ABA-nummer","ach.accountLocationField.invalid":"Ogiltigt ABA-nummer","ach.savedBankAccount":"Sparat bankkonto","ach.savings":"Sparkonto","ach.checking":"Checkkonto","select.state":"Välj delstat","select.stateOrProvince":"Välj delstat eller provins","select.provinceOrTerritory":"Välj provins eller territorium","select.country":"Välj land/region","select.noOptionsFound":"Inga alternativ hittades","select.filter.placeholder":"Sök efter…","telephoneNumber.invalid":"Ogiltigt telefonnummer",qrCodeOrApp:"eller","paypal.processingPayment":"Behandlar betalning…",generateQRCode:"Generera QR-kod","await.waitForConfirmation":"Väntar på bekräftelse","mbway.confirmPayment":"Bekräfta din betalning i appen MB WAY","shopperEmail.invalid":"Ogiltig e-postadress","dateOfBirth.format":"DD/MM/ÅÅÅÅ","dateOfBirth.invalid":"Ange ett giltigt födelsedatum som visar att du är minst 18 år gammal","blik.confirmPayment":"Öppna din bankapp för att bekräfta betalningen.","blik.invalid":"Ange 6 siffror","blik.code":"Sexsiffrig kod","blik.help":"Hämta koden från din bankapp.","swish.pendingMessage":"När du har skannat kan statusen vara väntande i upp till tio minuter. Att försöka betala igen inom denna tid kan leda till flera debiteringar.","field.valid":"Fältet är giltigt","field.invalid":"Fältet ogiltigt","error.va.gen.01":"Ofullständigt fält","error.va.gen.02":"Fältet ogiltigt","error.va.sf-cc-num.01":"Ange ett giltigt kortnummer","error.va.sf-cc-num.02":"Ange kortnumret","error.va.sf-cc-num.03":"Ange ett kortmärke som stöds","error.va.sf-cc-num.04":"Ange det fullständiga kortnumret","error.va.sf-cc-dat.01":"Ange ett giltigt utgångsdatum","error.va.sf-cc-dat.02":"Ange ett giltigt utgångsdatum","error.va.sf-cc-dat.03":"Kreditkortet går snart ut","error.va.sf-cc-dat.04":"Ange utgångsdatumet","error.va.sf-cc-dat.05":"Ange det fullständiga utgångsdatumet","error.va.sf-cc-mth.01":"Ange utgångsmånaden","error.va.sf-cc-yr.01":"Ange utgångsåret","error.va.sf-cc-yr.02":"Ange det fullständiga utgångsåret","error.va.sf-cc-cvc.01":"Ange säkerhetskoden","error.va.sf-cc-cvc.02":"Ange den fullständiga säkerhetskoden","error.va.sf-ach-num.01":"Fältet för bankkontonummer är tomt","error.va.sf-ach-num.02":"Bankkontonumret har fel längd","error.va.sf-ach-loc.01":"Fältet för clearingnummer är tomt","error.va.sf-ach-loc.02":"Clearingnumret har fel längd","error.va.sf-kcp-pwd.01":"Fältet för lösenord är tomt","error.va.sf-kcp-pwd.02":"Lösenordet har fel längd","error.giftcard.no-balance":"Saldot för detta presentkort är noll","error.giftcard.card-error":"Vi har inte registrerat något presentkort med det numret","error.giftcard.currency-error":"Presentkort är endast giltiga i den valuta som de utfärdades i","amazonpay.signout":"Logga ut från Amazon","amazonpay.changePaymentDetails":"Ändra betalningsuppgifter","partialPayment.warning":"Välj ett annat betalningssätt för att betala det återstående","partialPayment.remainingBalance":"Återstående saldo blir %{amount}","bankTransfer.beneficiary":"Mottagare","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"referens","bankTransfer.introduction":"Fortsätt för att skapa en ny banköverföring. Du kan använda informationen på följande skärm för att slutföra denna betalning.","bankTransfer.instructions":"Tack för ditt köp! Använd följande information för att slutföra din betalning.","bacs.accountHolderName":"Bankkontoinnehavarens namn","bacs.accountHolderName.invalid":"Ogiltigt namn på bankkontoinnehavare","bacs.accountNumber":"Bankkontonummer","bacs.accountNumber.invalid":"Ogiltigt bankkontonummer","bacs.bankLocationId":"Clearingnummer","bacs.bankLocationId.invalid":"Ogiltigt clearingnummer","bacs.consent.amount":"Jag godkänner att ovanstående belopp dras från mitt bankkonto.","bacs.consent.account":"Jag bekräftar att jag är kontoinnehavare och att jag är den enda person vars godkännande krävs för att auktorisera autogiro från detta konto.",edit:"Redigera","bacs.confirm":"Bekräfta och betala","bacs.result.introduction":"Ladda ner din instruktion för autogiro/direktdebitering (DDI / Mandate)","download.pdf":"Ladda ner PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe för kortnummer","creditCard.encryptedCardNumber.aria.label":"Kortnummer","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe för utgångsdatum","creditCard.encryptedExpiryDate.aria.label":"Utgångsdatum","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe för utgångsmånad","creditCard.encryptedExpiryMonth.aria.label":"Utgångsmånad","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe för utgångsår","creditCard.encryptedExpiryYear.aria.label":"Utgångsår","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe för säkerhetskod","creditCard.encryptedSecurityCode.aria.label":"Säkerhetskod","creditCard.encryptedPassword.aria.iframeTitle":"Iframe för lösenord","creditCard.encryptedPassword.aria.label":"De två första siffrorna i kortets lösenord","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe för kortnummer","giftcard.encryptedCardNumber.aria.label":"Kortnummer","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe för PIN","giftcard.encryptedSecurityCode.aria.label":"PIN-kod",giftcardTransactionLimit:"Maximalt %{amount} per transaktion är tillåtet på detta presentkort","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe för bankkontonummer","ach.encryptedBankAccountNumber.aria.label":"Kontonummer","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe för clearingnummer","ach.encryptedBankLocationId.aria.label":"ABA-nummer","twint.saved":"sparat",orPayWith:"eller betala med",invalidFormatExpects:"Ogiltigt format. Förväntat format: %{format}","upi.qrCodeWaitingMessage":"Skanna QR-koden med valfri UPI-app för att slutföra betalningen","upi.vpaWaitingMessage":"Öppna din UPI-app för att bekräfta betalningen","upi.modeSelection":"Hur vill du använda UPI?","upi.completePayment":"Slutför din betalning","upi.mode.enterUpiId":"Ange UPI-ID","upi.mode.qrCode":"QR-kod","upi.mode.payByAnyUpi":"Betala med valfri UPI-app","upi.collect.dropdown.label":"Ange UPI-ID","upi.collect.field.label":"Ange UPI-ID/VPA","onlineBanking.termsAndConditions":"Genom att fortsätta godkänner du %#villkoren%#","onlineBankingPL.termsAndConditions":"Genom att fortsätta godkänner du Przelewy24s %#regler%# och %#informationsplikt%#.","ctp.loading.poweredByCtp":"Drivs av Click to Pay","ctp.loading.intro":"Vi kontrollerar om du har några sparade kort med Click to Pay...","ctp.login.title":"Fortsätt till Click to Pay","ctp.login.subtitle":"Ange den e-postadress som är ansluten till Click to Pay för att fortsätta.","ctp.login.inputLabel":"E-postadress","ctp.logout.notYou":"Inte du?","ctp.logout.notYourCards":"Inte dina kort?","ctp.logout.notYourCard":"Inte ditt kort?","ctp.logout.notYourProfile":"Inte din profil?","ctp.otp.fieldLabel":"Engångskod","ctp.otp.resendCode":"Skicka koden igen","ctp.otp.codeResent":"Koden skickad igen","ctp.otp.title":"Få tillgång till dina Click to Pay-kort","ctp.otp.subtitle":"Ange koden som %@ skickade till %@ för att verifiera att det är du.","ctp.otp.saveCookiesCheckbox.label":"Hoppa över verifieringen nästa gång","ctp.otp.saveCookiesCheckbox.information":"Välj att bli ihågkommen på din enhet och webbläsare i deltagande butiker för snabbare utcheckning. Rekommenderas inte för delade enheter.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Välj att bli ihågkommen på din enhet och i din webbläsare","ctp.emptyProfile.message":"Inga kort registrerade i denna Click to Pay-profil","ctp.separatorText":"eller använd","ctp.cards.title":"Slutför betalningen med Click to Pay","ctp.cards.subtitle":"Välj ett kort att använda.","ctp.cards.expiredCard":"Utgångna","ctp.manualCardEntry":"Manuell kortinmatning","ctp.aria.infoModalButton":"Vad är Click to Pay?","ctp.infoPopup.title":"Click to Pay – lika enkelt som kontaktlös betalning, fast online","ctp.infoPopup.subtitle":"En snabb och säker betalningsmetod som stöds av Mastercard, Visa och andra betalkort.","ctp.infoPopup.benefit1":"Click to Pay använder kryptering för att hålla din information säker och trygg","ctp.infoPopup.benefit2":"Använd den hos handlare över hela världen","ctp.infoPopup.benefit3":"Konfigurera en gång för problemfria betalningar i framtiden","ctp.errors.AUTH_INVALID":"Autentisering ogiltig","ctp.errors.NOT_FOUND":"Inget konto hittades, ange en giltig e-postadress eller fortsätt med manuell kortinmatning","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formatet stöds inte","ctp.errors.FRAUD":"Användarkontot var låst eller inaktiverat","ctp.errors.CONSUMER_ID_MISSING":"Konsumentidentitet saknas i begäran","ctp.errors.ACCT_INACCESSIBLE":"Det här kontot är för närvarande inte tillgängligt, det kanske är låst","ctp.errors.CODE_INVALID":"Felaktig verifieringskod","ctp.errors.CODE_EXPIRED":"Denna kod har gått ut","ctp.errors.RETRIES_EXCEEDED":"Gränsen för antalet försök till OTP-generering har överskridits","ctp.errors.OTP_SEND_FAILED":"OTP kunde inte skickas till mottagaren","ctp.errors.REQUEST_TIMEOUT":"Något gick fel, försök igen eller använd den manuella kortinmatningen","ctp.errors.UNKNOWN_ERROR":"Något gick fel, försök igen eller använd manuell kortinmatning","ctp.errors.SERVICE_ERROR":"Något gick fel, försök igen eller använd manuell kortinmatning","ctp.errors.SERVER_ERROR":"Något gick fel, försök igen eller använd manuell kortinmatning","ctp.errors.INVALID_PARAMETER":"Något gick fel, försök igen eller använd manuell kortinmatning","ctp.errors.AUTH_ERROR":"Något gick fel, försök igen eller använd manuell kortinmatning","paymentMethodsList.aria.label":"Välj en betalningsmetod","companyDetails.name.invalid":"Ange företagsnamnet","companyDetails.registrationNumber.invalid":"Ange registreringsnumret","consent.checkbox.invalid":"Du måste godkänna villkoren","form.instruction":"Alla fält är obligatoriska om inte något annat anges.","trustly.descriptor":"Omedelbar bankbetalning","trustly.description1":"Betala direkt från något av dina bankkonton med säkerhet på banknivå","trustly.description2":"Inga kort, ingen nerladdning av app, ingen registrering","ancv.input.label":"Din ANCV-identifiering","ancv.confirmPayment":"Bekräfta betalningen i din ANCV-app.","ancv.form.instruction":"Appen Cheque-Vacances krävs för att validera denna betalning.","ancv.beneficiaryId.invalid":"Ange en giltig e-postadress eller ANCV-ID","payme.openPayMeApp":"Slutför din betalning i PayMe-appen genom att godkänna betalningen i appen och vänta på bekräftelse.","payme.redirectButtonLabel":"Öppna PayMe-appen","payme.scanQrCode":"Slutför din betalning med QR-kod","payme.timeToPay":"Denna QR-kod är giltig i %@","payme.instructions.steps":"Öppna PayMe-appen.%@Skanna QR-koden för att godkänna betalningen.%@Slutför betalningen i appen och vänta på bekräftelse.","payme.instructions.footnote":"Stäng inte denna sida innan betalningen är slutförd","payByBankAISDD.disclaimer.header":"Använd Pay by Bank och betala direkt från vilket bankkonto som helst.","payByBankAISDD.disclaimer.body":"Genom att ansluta ditt bankkonto godkänner du att ditt konto debiteras eventuella belopp som du är skyldig för användning av våra tjänster och/eller köp av våra produkter, tills detta godkännande återkallas.","paymentMethodBrand.other":"andra"}},5234:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Заплатить","payButton.redirecting":"Перенаправление...","payButton.with":"Оплатить %{value} %{maskedData}","payButton.saveDetails":"Сохранить данные",close:"Закрыть",storeDetails:"Сохранить для следующего платежа",readMore:"Подробнее","creditCard.holderName":"Имя на карте","creditCard.holderName.placeholder":"И. Петров","creditCard.holderName.invalid":"Введите имя, как оно указано на карте","creditCard.numberField.title":"Номер карты","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Срок действия","creditCard.expiryDateField.placeholder":"ММ/ГГ","creditCard.expiryDateField.month":"Месяц","creditCard.expiryDateField.month.placeholder":"ММ","creditCard.expiryDateField.year.placeholder":"ГГ","creditCard.expiryDateField.year":"Год","creditCard.cvcField.title":"Защитный код","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Запомнить на следующий раз","creditCard.cvcField.placeholder.4digits":"4 цифры","creditCard.cvcField.placeholder.3digits":"3 цифры","creditCard.taxNumber.placeholder":"ГГММДД / 0123456789",installments:"Количество платежей",installmentOption:"%{times}× %{partialValue}",installmentOptionMonths:"%{times} мес.","installments.oneTime":"Одноразовый платеж","installments.installments":"Рассрочка","installments.revolving":"Повторяющаяся оплата","sepaDirectDebit.ibanField.invalid":"Недействительный номер счета","sepaDirectDebit.nameField.placeholder":"И. Петров","sepa.ownerName":"Имя владельца","sepa.ibanNumber":"Номер счета (IBAN)","error.title":"Ошибка","error.subtitle.redirect":"Сбой перенаправления","error.subtitle.payment":"Сбой оплаты","error.subtitle.refused":"Оплата отклонена","error.message.unknown":"Возникла неизвестная ошибка","errorPanel.title":"Имеющиеся ошибки","idealIssuer.selectField.title":"Банк","idealIssuer.selectField.placeholder":"Выберите банк","creditCard.success":"Платеж успешно завершен",loading:"Загрузка…",continue:"Продолжить",continueTo:"Перейти к","wechatpay.timetopay":"У вас %@ на оплату","sr.wechatpay.timetopay":"У вас есть %#мин.%# %#сек.%# для оплаты","wechatpay.scanqrcode":"Сканировать QR-код",personalDetails:"Личные данные",companyDetails:"Данные компании","companyDetails.name":"Название компании","companyDetails.registrationNumber":"Регистрационный номер",socialSecurityNumber:"Номер социального страхования или ИНН",firstName:"Имя","firstName.invalid":"Введите имя",infix:"Приставка",lastName:"Фамилия","lastName.invalid":"Введите фамилию",mobileNumber:"Мобильный телефон","mobileNumber.invalid":"Недействительной номер мобильного",city:"Город",postalCode:"Почтовый индекс","postalCode.optional":"Почтовый индекс (необязательно)",countryCode:"Код страны",telephoneNumber:"Номер телефона",dateOfBirth:"Дата рождения",shopperEmail:"Адрес эл. почты",gender:"Пол","gender.notselected":"Укажите свой пол",male:"Мужчина",female:"Женщина",billingAddress:"Платежный адрес",street:"Улица",stateOrProvince:"Регион",country:"Страна/регион",houseNumberOrName:"Номер дома",separateDeliveryAddress:"Укажите отдельный адрес доставки",deliveryAddress:"Адрес доставки","deliveryAddress.firstName":"Имя получателя","deliveryAddress.lastName":"Фамилия получателя",zipCode:"Почтовый индекс",apartmentSuite:"Квартира / помещение",provinceOrTerritory:"Провинция или территория",cityTown:"Город",address:"Адрес","address.placeholder":"Найти свой адрес","address.errors.incomplete":"Для продолжения укажите адрес","address.enterManually":"Ввести адрес вручную",state:"Штат","field.title.optional":"(необязательно)","creditCard.cvcField.title.optional":"Защитный код (необязательно)","issuerList.wallet.placeholder":"Выберите кошелек",privacyPolicy:"Политика конфиденциальности","afterPay.agreement":"Я принимаю %@ Riverty","riverty.termsAndConditions":"Я выражаю согласие с общими %#Условиями%# использования платежного средства Riverty. С политикой конфиденциальности Riverty можно ознакомиться %#здесь%#.",paymentConditions:"условия оплаты",openApp:"Открыть приложение","voucher.readInstructions":"Прочитайте инструкции","voucher.introduction":"Благодарим за покупку. Для завершения оплаты используйте следующий купон.","voucher.expirationDate":"Срок действия","voucher.alternativeReference":"Другой код","dragonpay.voucher.non.bank.selectField.placeholder":"Выберите своего оператора","dragonpay.voucher.bank.selectField.placeholder":"Выберите банк","voucher.paymentReferenceLabel":"Код оплаты","voucher.surcharge":"Вкл. комиссию %@","voucher.introduction.doku":"Благодарим за покупку. Для завершения оплаты используйте следующие сведения.","voucher.shopperName":"Имя покупателя","voucher.merchantName":"Продавец","voucher.introduction.econtext":"Благодарим за покупку. Для завершения оплаты используйте следующие сведения.","voucher.telephoneNumber":"Номер телефона","voucher.shopperReference":"Идентификатор покупателя","voucher.collectionInstitutionNumber":"Номер получателя средств","voucher.econtext.telephoneNumber.invalid":"Номер телефона должен быть длиной 10 или 11 цифр","boletobancario.btnLabel":"Создать Boleto","boleto.sendCopyToEmail":"Отправить мне копию на эл. почту","button.copy":"Копия","button.download":"Загрузить","boleto.socialSecurityNumber.invalid":"Введите действительный номер CPF/CNPJ","creditCard.storedCard.description.ariaLabel":"Сохраненная карта заканчивается на %@","voucher.entity":"Объект",donateButton:"Пожертвовать",notNowButton:"Позже",thanksForYourSupport:"Благодарим за поддержку!","resultMessages.preauthorized":"Данные сохранены",preauthorizeWith:"Предавторизация в",confirmPreauthorization:"Подтвердить предавторизацию",confirmPurchase:"Подтвердить покупку",applyGiftcard:"Использовать",giftcardBalance:"Баланс подарочной карты",deductedBalance:"Баланс списаний","creditCard.pin.title":"PIN-код","creditCard.encryptedPassword.label":"Первые 2 цифры пароля карты","creditCard.encryptedPassword.invalid":"Неверный пароль","creditCard.taxNumber":"Дата рождения владельца карты или регистрационный номер предприятия","creditCard.taxNumber.label":"Дата рождения владельца карты (ГГММДД) или регистрационный номер предприятия (10 цифр)","creditCard.taxNumber.labelAlt":"Регистрационный номер предприятия (10 цифр)","creditCard.taxNumber.invalid":"Неверная дата рождения владельца карты или регистрационный номер предприятия","storedPaymentMethod.disable.button":"Удалить","storedPaymentMethod.disable.confirmation":"Удалить сохраненный способ оплаты","storedPaymentMethod.disable.confirmButton":"Да, удалить","storedPaymentMethod.disable.cancelButton":"Отменить","ach.bankAccount":"Банковский счет","ach.accountHolderNameField.title":"Имя владельца карты","ach.accountHolderNameField.placeholder":"И. Петров","ach.accountHolderNameField.invalid":"Недействительное имя владельца карты","ach.accountNumberField.title":"Номер счета","ach.accountNumberField.invalid":"Недействительный номер счета","ach.accountLocationField.title":"Маршрутный номер ABA","ach.accountLocationField.invalid":"Недействительный маршрутный номер ABA","ach.savedBankAccount":"Сохраненный банковский счет","ach.savings":"Сберегательный счет","ach.checking":"Текущий счет","select.state":"Выберите штат","select.stateOrProvince":"Выберите штат или область","select.provinceOrTerritory":"Выберите провинцию или территорию","select.country":"Выберите страну/регион","select.noOptionsFound":"Вариантов не найдено","select.filter.placeholder":"Поиск…","telephoneNumber.invalid":"Недействительный номер телефона",qrCodeOrApp:"или","paypal.processingPayment":"Платеж обрабатывается…",generateQRCode:"Создать QR-код","await.waitForConfirmation":"Ожидание подтверждения","mbway.confirmPayment":"Подтвердите оплату в приложении MB WAY","shopperEmail.invalid":"Недействительный адрес эл. почты","dateOfBirth.format":"ДД/ММ/ГГГГ","dateOfBirth.invalid":"Введите правильную дату рождения. Вам должно быть не менее 18 лет.","blik.confirmPayment":"Для подтверждения оплаты откройте приложение банка.","blik.invalid":"Введите 6 цифр","blik.code":"6-значный код","blik.help":"Получите код из приложения вашего банка.","swish.pendingMessage":"Обработка оплаты после сканирования может занять 10 минут. Попытка провести оплату повторно в течение указанного времени может привести к нескольким списаниям со счета.","field.valid":"Действительное поле","field.invalid":"Недействительное поле","error.va.gen.01":"Незаполненное поле","error.va.gen.02":"Недействительное поле","error.va.sf-cc-num.01":"Введите номер действительной карты","error.va.sf-cc-num.02":"Введите номер карты","error.va.sf-cc-num.03":"Введите поддерживаемую марку карты","error.va.sf-cc-num.04":"Введите номер карты полностью","error.va.sf-cc-dat.01":"Введите действительную дату окончания срока действия","error.va.sf-cc-dat.02":"Введите действительную дату окончания срока действия","error.va.sf-cc-dat.03":"Скоро истекает срок действия кредитной карты","error.va.sf-cc-dat.04":"Введите срок годности","error.va.sf-cc-dat.05":"Введите полную дату истечения срока действия","error.va.sf-cc-mth.01":"Введите месяц истечения срока действия","error.va.sf-cc-yr.01":"Введите год истечения срока действия","error.va.sf-cc-yr.02":"Введите год истечения срока действия полностью","error.va.sf-cc-cvc.01":"Введите защитный код","error.va.sf-cc-cvc.02":"Введите защитный код полностью","error.va.sf-ach-num.01":"Поле номера банковского счета не заполнено","error.va.sf-ach-num.02":"Номер банковского счета имеет неправильную длину","error.va.sf-ach-loc.01":"Поле маршрутного номера банка не заполнено","error.va.sf-ach-loc.02":"Маршрутный номер банка имеет неправильную длину","error.va.sf-kcp-pwd.01":"Поле пароля не заполнено","error.va.sf-kcp-pwd.02":"Пароль имеет неправильную длину","error.giftcard.no-balance":"На этой подарочной карте нет средств","error.giftcard.card-error":"У нас не зарегистрирована карта с таким номером","error.giftcard.currency-error":"Принимаются только подарочные карты соответствующей валюты","amazonpay.signout":"Выйти из Amazon","amazonpay.changePaymentDetails":"Изменить информацию об оплате","partialPayment.warning":"Выбрать другой способ оплаты остатка","partialPayment.remainingBalance":"Остаток на балансе составит %{amount}","bankTransfer.beneficiary":"Получатель","bankTransfer.reference":"Ссылка","bankTransfer.introduction":"Для создания нового банковского перечисления перейдите далее. Для окончательного оформления платежа используйте сведения на следующем экране.","bankTransfer.instructions":"Благодарим за покупку. Для завершения оплаты используйте следующие сведения.","bacs.accountHolderName":"Имя владельца банковского счета","bacs.accountHolderName.invalid":"Неверное имя владельца банковского счета","bacs.accountNumber":"Номер банковского счета","bacs.accountNumber.invalid":"Неверный номер банковского счета","bacs.bankLocationId":"Код банка","bacs.bankLocationId.invalid":"Неверный код банка","bacs.consent.amount":"Выражаю согласие на списание вышеуказанной суммы с моего банковского счета.","bacs.consent.account":"Подтверждаю, что счет оформлен на мое имя и что я – единственное лицо, имеющее право подписи, разрешающей прямое дебетование средств со счета.",edit:"Изменить","bacs.confirm":"Подтвердить и оплатить","bacs.result.introduction":"Загрузить распоряжение прямого дебетования (DDI / поручение)","download.pdf":"Загрузить PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe для номера карты","creditCard.encryptedCardNumber.aria.label":"Номер карты","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe для даты истечения срока действия","creditCard.encryptedExpiryDate.aria.label":"Срок действия","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe для месяца истечения срока действия","creditCard.encryptedExpiryMonth.aria.label":"Месяц срока действия","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe для года истечения срока действия","creditCard.encryptedExpiryYear.aria.label":"Год срока действия","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe для защитного кода","creditCard.encryptedSecurityCode.aria.label":"Защитный код","creditCard.encryptedPassword.aria.iframeTitle":"Iframe для пароля","creditCard.encryptedPassword.aria.label":"Первые 2 цифры пароля карты","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe для номера карты","giftcard.encryptedCardNumber.aria.label":"Номер карты","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe для PIN-кода","giftcard.encryptedSecurityCode.aria.label":"PIN-код",giftcardTransactionLimit:"Максимальная сумма операции по этой подарочной карте: %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe для номера банковского счета","ach.encryptedBankAccountNumber.aria.label":"Номер счета","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe для маршрутного номера банка","ach.encryptedBankLocationId.aria.label":"Маршрутный номер ABA","twint.saved":"сохранено",orPayWith:"или заплатите через",invalidFormatExpects:"Неверный формат. Ожидаемый формат: %{format}","upi.qrCodeWaitingMessage":"Для завершения платежа отсканируйте QR-код с помощью предпочитаемого приложения UPI","upi.vpaWaitingMessage":"Откройте приложение UPI для подтверждения платежа","upi.modeSelection":"Как вы хотите использовать UPI?","upi.completePayment":"Завершите платеж","upi.mode.enterUpiId":"Введите идентификатор UPI","upi.mode.qrCode":"QR-код","upi.mode.payByAnyUpi":"Оплата любым приложением UPI","upi.collect.dropdown.label":"Введите идентификатор UPI","upi.collect.field.label":"Введите VPA или идентификатор UPI","onlineBanking.termsAndConditions":"Продолжая, вы тем самым соглашаетесь с %#условиями%#","onlineBankingPL.termsAndConditions":"Продолжая, вы соглашаетесь с %#правилами%# и %#обязательствами в отношении данных%# Przelewy24","ctp.loading.poweredByCtp":"На платформе Click to Pay","ctp.loading.intro":"Проверка наличия сохраненных карт Click to Pay…","ctp.login.title":"Перейти на Click to Pay","ctp.login.subtitle":"Для продолжения введите адрес эл. почты, связанный с Click to Pay.","ctp.login.inputLabel":"Эл. почта","ctp.logout.notYou":"Это не вы?","ctp.logout.notYourCards":"Не ваши платежные карты?","ctp.logout.notYourCard":"Не ваша карта?","ctp.logout.notYourProfile":"Не ваш профиль?","ctp.otp.fieldLabel":"Одноразовый код","ctp.otp.resendCode":"Отправить код повторно","ctp.otp.codeResent":"Код отправлен повторно","ctp.otp.title":"Получите доступ к своим картам Click to Pay","ctp.otp.subtitle":"Для подтверждения своей личности введите код %@, отправленный %@.","ctp.otp.saveCookiesCheckbox.label":"Пропустить подтверждение в следующий раз","ctp.otp.saveCookiesCheckbox.information":"Чтобы в соответствующих магазинах запомнили ваше устройство и браузер, выберите эту опцию. Это позволит быстрее оформлять заказы. Не рекомендуется на устройствах совместного использования.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Выберите, чтобы запомнили ваше устройство и браузер","ctp.emptyProfile.message":"В данном профиле Click to Pay нет зарегистрированных карт","ctp.separatorText":"или используйте","ctp.cards.title":"Оформите оплату с помощью Click to Pay","ctp.cards.subtitle":"Выберите карту.","ctp.cards.expiredCard":"Просрочена","ctp.manualCardEntry":"Ввод данных карты вручную","ctp.aria.infoModalButton":"Что такое Click to Pay","ctp.infoPopup.title":"Click to Pay – удобная бесконтактная оплата онлайн","ctp.infoPopup.subtitle":"Быстрый безопасный способ оплаты, поддерживаемый Mastercard, Visa и другими платежными картами.","ctp.infoPopup.benefit1":"Для обеспечения безопасности ваших данных Click to Pay использует шифрование","ctp.infoPopup.benefit2":"Используйте его в магазинах по всему миру","ctp.infoPopup.benefit3":"Беспроблемные платежи в будущем — настройка требуется только один раз","ctp.errors.AUTH_INVALID":"Аутентификация недействительна","ctp.errors.NOT_FOUND":"Учетная запись не найдена. Введите действительный адрес эл. почты или продолжите ввод карты вручную","ctp.errors.ID_FORMAT_UNSUPPORTED":"Формат не поддерживается","ctp.errors.FRAUD":"Уч. запись пользователя заблокирована или отключена","ctp.errors.CONSUMER_ID_MISSING":"Идентификатор потребителя отсутствует в запросе","ctp.errors.ACCT_INACCESSIBLE":"Эта учетная запись сейчас недоступна (она может быть, например, заблокирована)","ctp.errors.CODE_INVALID":"Неверный код подтверждения","ctp.errors.CODE_EXPIRED":"Срок действия кода истек","ctp.errors.RETRIES_EXCEEDED":"Превышен лимит попыток создания одноразового пароля","ctp.errors.OTP_SEND_FAILED":"Не удалось отправить получателю одноразовый пароль","ctp.errors.REQUEST_TIMEOUT":"Возник сбой. Повторите попытку или введите номер карты вручную","ctp.errors.UNKNOWN_ERROR":"Возник сбой. Повторите попытку или введите номер карты вручную","ctp.errors.SERVICE_ERROR":"Возник сбой. Повторите попытку или введите номер карты вручную","ctp.errors.SERVER_ERROR":"Возник сбой. Повторите попытку или введите номер карты вручную","ctp.errors.INVALID_PARAMETER":"Возник сбой. Повторите попытку или введите номер карты вручную","ctp.errors.AUTH_ERROR":"Возник сбой. Повторите попытку или введите номер карты вручную","paymentMethodsList.aria.label":"Выберите способ оплаты","companyDetails.name.invalid":"Введите название компании","companyDetails.registrationNumber.invalid":"Введите регистрационный номер","consent.checkbox.invalid":"Требуется выразить согласие с условиями","form.instruction":"Все поля обязательны для заполнения, если не указано иное.","trustly.descriptor":"Мгновенный банковский платеж","trustly.description1":"Платите непосредственно со своего банковского счета под такой же надежной защитой, как в банках","trustly.description2":"Без карт, загрузки приложений и регистрации","ancv.input.label":"Ваш идентификатор ANCV","ancv.confirmPayment":"Используйте приложение ANCV для подтверждения платежа.","ancv.form.instruction":"Для подтверждения этого платежа необходимо приложение Cheque-Vacances.","ancv.beneficiaryId.invalid":"Введите действительный адрес электронной почты или идентификатор ANCV","payme.openPayMeApp":"Для завершения оплаты разрешите ее в приложении PayMe и дождитесь подтверждения.","payme.redirectButtonLabel":"Открыть приложение PayMe","payme.scanQrCode":"Завершить платеж с помощью QR-кода","payme.timeToPay":"Данный QR-код действителен в течение %@","payme.instructions.steps":"Откройте приложение PayMe.%@Отсканируйте QR-код, чтобы разрешить платеж.%@Завершите платеж в приложении и дождитесь подтверждения.","payme.instructions.footnote":"Не закрывайте эту страницу до завершения платежа","payByBankAISDD.disclaimer.header":"Для моментальной оплаты с любого банковского счета воспользуйтесь приложением Pay by Bank.","payByBankAISDD.disclaimer.body":"Привязывая банковский счет, вы тем самым даете разрешение на списание с него средств за пользование нашими услугами и/или покупку нашей продукции. Данное разрешение продействует до тех пор, пока оно не будет отозвано.","paymentMethodBrand.other":"другие"}},15480:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Zaplatiť","payButton.redirecting":"Prebieha presmerovanie...","payButton.with":"Zaplatiť %{value} pomocou %{maskedData}","payButton.saveDetails":"Uložiť údaje",close:"Zavrieť",storeDetails:"Uložiť pre moju ďalšiu platbu",readMore:"Prečítajte si viac","creditCard.holderName":"Meno na karte","creditCard.holderName.placeholder":"J. Novák","creditCard.holderName.invalid":"Zadajte meno tak, ako je uvedené na karte","creditCard.numberField.title":"Číslo karty","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Koniec platnosti","creditCard.expiryDateField.placeholder":"MM/RR","creditCard.expiryDateField.month":"Mesiac","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"RR","creditCard.expiryDateField.year":"Rok","creditCard.cvcField.title":"Bezpečnostný kód","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Zapamätať na budúce použitie","creditCard.cvcField.placeholder.4digits":"4 číslice","creditCard.cvcField.placeholder.3digits":"3 číslice","creditCard.taxNumber.placeholder":"RRMMDD/0123456789",installments:"Počet splátok",installmentOption:"% {times} x % {partialValue}",installmentOptionMonths:"%{times} mesiace/-ov","installments.oneTime":"Jednorazová platba","installments.installments":"Platba po splátkach","installments.revolving":"Revolvingová platba","sepaDirectDebit.ibanField.invalid":"Neplatné číslo účtu","sepaDirectDebit.nameField.placeholder":"J. Novák","sepa.ownerName":"Meno držiteľa","sepa.ibanNumber":"Číslo účtu (IBAN)","error.title":"Chyba","error.subtitle.redirect":"Nepodarilo sa presmerovať","error.subtitle.payment":"Platba zlyhala","error.subtitle.refused":"Platba bola zamietnutá","error.message.unknown":"Vyskytla sa neznáma chyba","errorPanel.title":"Existujúce chyby","idealIssuer.selectField.title":"Banka","idealIssuer.selectField.placeholder":"Vyberte svoju banku","creditCard.success":"Platba bola úspešná",loading:"Načítava sa…",continue:"Pokračovať",continueTo:"Pokračovať do","wechatpay.timetopay":"Na zaplatenie máte %@","sr.wechatpay.timetopay":"Na zaplatenie máte %#min. %# %#sek.%#","wechatpay.scanqrcode":"Naskenujte QR kód",personalDetails:"Osobné údaje",companyDetails:"Údaje o spoločnosti","companyDetails.name":"Názov spoločnosti","companyDetails.registrationNumber":"Identifikačné číslo",socialSecurityNumber:"Rodné číslo",firstName:"Krstné meno","firstName.invalid":"Zadajte svoje meno",infix:"Predpona k priezvisku (ak existuje)",lastName:"Priezvisko","lastName.invalid":"Zadajte svoje priezvisko",mobileNumber:"Mobilné telefónne číslo","mobileNumber.invalid":"Neplatné číslo na mobil",city:"Mesto",postalCode:"PSČ","postalCode.optional":"Poštové smerovacie číslo (nepovinné)",countryCode:"Kód krajiny",telephoneNumber:"Telefónne číslo",dateOfBirth:"Dátum narodenia",shopperEmail:"E-mailová adresa",gender:"Pohlavie","gender.notselected":"Vyberte rod, s ktorým sa stotožňujete",male:"Muž",female:"Žena",billingAddress:"Fakturačná adresa",street:"Ulica",stateOrProvince:"Kraj",country:"Krajina/región",houseNumberOrName:"Číslo domu",separateDeliveryAddress:"Zadajte samostatnú dodaciu adresu",deliveryAddress:"Dodacia adresa","deliveryAddress.firstName":"Krstné meno príjemcu","deliveryAddress.lastName":"Priezvisko príjemcu",zipCode:"PSČ",apartmentSuite:"Byt/apartmán",provinceOrTerritory:"Okres alebo územie",cityTown:"Mesto/obec",address:"Adresa","address.placeholder":"Vyhľadajte svoju adresu","address.errors.incomplete":"Ak chcete pokračovať, zadajte adresu","address.enterManually":"Manuálne zadajte adresu",state:"Štát","field.title.optional":"(voliteľné)","creditCard.cvcField.title.optional":"Bezpečnostný kód (voliteľný)","issuerList.wallet.placeholder":"Vyberte si peňaženku",privacyPolicy:"Zásady ochrany osobných údajov","afterPay.agreement":"Súhlasím, že budem dodržiavať %@ spoločnosti Riverty","riverty.termsAndConditions":"Súhlasím so všeobecnými %#Zmluvnými podmienkami%# pre spôsob platby Riverty. Zásady ochrany osobných údajov spoločnosti Riverty nájdete %#tu%#.",paymentConditions:"podmienkami platby",openApp:"Otvorte aplikáciu","voucher.readInstructions":"Prečítajte si pokyny","voucher.introduction":"Ďakujeme vám za nákup; na dokončenie platby použite nasledujúci kupón.","voucher.expirationDate":"Dátum vypršania platnosti","voucher.alternativeReference":"Alternatívny odkaz","dragonpay.voucher.non.bank.selectField.placeholder":"Vyberte si poskytovateľa","dragonpay.voucher.bank.selectField.placeholder":"Vyberte svoju banku","voucher.paymentReferenceLabel":"Platobná referencia","voucher.surcharge":"Vrátane poplatku vo výške %@","voucher.introduction.doku":"Ďakujeme vám za nákup. Na dokončenie platby použite nasledujúce informácie.","voucher.shopperName":"Meno kupujúceho","voucher.merchantName":"Predajca","voucher.introduction.econtext":"Ďakujeme vám za nákup. Na dokončenie platby použite nasledujúce informácie.","voucher.telephoneNumber":"Telefónne číslo","voucher.shopperReference":"Referencia kupujúceho","voucher.collectionInstitutionNumber":"Číslo inkasnej inštitúcie","voucher.econtext.telephoneNumber.invalid":"Telefónne číslo musí mať 10 alebo 11 číslic","boletobancario.btnLabel":"Generovať Boleto","boleto.sendCopyToEmail":"Zaslať kópiu na môj e-mail","button.copy":"Kopírovať","button.download":"Stiahnuť","boleto.socialSecurityNumber.invalid":"Zadajte platné číslo CPF/CNPJ","creditCard.storedCard.description.ariaLabel":"Platnosť uloženej karty končí o %@","voucher.entity":"Subjekt",donateButton:"Prispieť",notNowButton:"Teraz nie",thanksForYourSupport:"Ďakujeme za podporu!","resultMessages.preauthorized":"Údaje boli uložené",preauthorizeWith:"Predbežne autorizovať pomocou",confirmPreauthorization:"Potvrďte predbežnú autorizáciu",confirmPurchase:"Potvrďte nákup",applyGiftcard:"Uplatniť",giftcardBalance:"Zostatok na darčekovej karte",deductedBalance:"Znížený zostatok","creditCard.pin.title":"Kód PIN","creditCard.encryptedPassword.label":"Prvé 2 číslice hesla karty","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Neplané heslo","creditCard.taxNumber":"Dátum narodenia držiteľa karty alebo identifikačné číslo organizácie","creditCard.taxNumber.label":"Dátum narodenia držiteľa karty (RRRMMDD) alebo identifikačné číslo organizácie (10 číslic)","creditCard.taxNumber.labelAlt":"Identifikačné číslo organizácie (10 číslic)","creditCard.taxNumber.invalid":"Neplatný dátum narodenia držiteľa karty alebo identifikačné číslo organizácie","storedPaymentMethod.disable.button":"Odstrániť","storedPaymentMethod.disable.confirmation":"Odstrániť uložený spôsob platby","storedPaymentMethod.disable.confirmButton":"Áno, odstrániť","storedPaymentMethod.disable.cancelButton":"Zrušiť","ach.bankAccount":"Bankový účet","ach.accountHolderNameField.title":"Meno majiteľa účtu","ach.accountHolderNameField.placeholder":"J. Novák","ach.accountHolderNameField.invalid":"Neplatné meno majiteľa účtu","ach.accountNumberField.title":"Číslo účtu","ach.accountNumberField.invalid":"Neplatné číslo účtu","ach.accountLocationField.title":"Smerovacie číslo ABA","ach.accountLocationField.invalid":"Neplatné smerovacie číslo ABA","ach.savedBankAccount":"Uložený bankový účet","ach.savings":"Sporiaci účet","ach.checking":"Kontrolný účet","select.state":"Vyberte kraj","select.stateOrProvince":"Vyberte kraj alebo okres","select.provinceOrTerritory":"Vyberte okres alebo územie","select.country":"Vyberte krajinu/región","select.noOptionsFound":"Neboli nájdené žiadne možnosti","select.filter.placeholder":"Vyhľadávanie...","telephoneNumber.invalid":"Neplatné telefónne číslo",qrCodeOrApp:"alebo","paypal.processingPayment":"Platba sa spracúva.",generateQRCode:"Generovať QR kód","await.waitForConfirmation":"Čaká sa na potvrdenie","mbway.confirmPayment":"Potvrďte svoju platbu v aplikácii MB WAY","shopperEmail.invalid":"Neplatná emailová adresa","dateOfBirth.format":"DD/MM/RRRR","dateOfBirth.invalid":"Zadajte platný dátum narodenia, ktorý uvádza, že máte aspoň 18 rokov","blik.confirmPayment":"Otvorte svoju bankovú aplikáciu a potvrďte platbu.","blik.invalid":"Zadajte 6 číslic","blik.code":"6-ciferný kód","blik.help":"Získajte kód zo svojej bankovej aplikácie.","swish.pendingMessage":"Po nasnímaní môže spracovanie trvať až 10 minút. Pokus o opätovné zaplatenie v tejto lehote môže viesť k niekoľkým poplatkom.","field.valid":"Pole je platné","field.invalid":"Pole je neplatné","error.va.gen.01":"Neúplné pole","error.va.gen.02":"Pole je neplatné","error.va.sf-cc-num.01":"Zadajte číslo platnej karty","error.va.sf-cc-num.02":"Zadajte číslo karty","error.va.sf-cc-num.03":"Zadajte podporovanú značku karty","error.va.sf-cc-num.04":"Zadajte celé číslo karty","error.va.sf-cc-dat.01":"Zadajte platný dátum vypršania platnosti","error.va.sf-cc-dat.02":"Zadajte platný dátum vypršania platnosti","error.va.sf-cc-dat.03":"Platnosť kreditnej karty čoskoro vyprší","error.va.sf-cc-dat.04":"Zadajte deň vypršania platnosti","error.va.sf-cc-dat.05":"Zadajte celý dátum vypršania platnosti","error.va.sf-cc-mth.01":"Zadajte mesiac vypršania platnosti","error.va.sf-cc-yr.01":"Zadajte rok vypršania platnosti","error.va.sf-cc-yr.02":"Zadajte celý rok vypršania platnosti","error.va.sf-cc-cvc.01":"Zadajte bezpečnostný kód","error.va.sf-cc-cvc.02":"Zadajte celý bezpečnostný kód","error.va.sf-ach-num.01":"Pole na zadanie čísla bankového účtu je prázdne","error.va.sf-ach-num.02":"Číslo bankového účtu má nesprávnu dĺžku","error.va.sf-ach-loc.01":"Pole na zadanie smerového čísla banky je prázdne","error.va.sf-ach-loc.02":"Smerové číslo banky má nesprávnu dĺžku","error.va.sf-kcp-pwd.01":"Pole na zadanie hesla je prázdne","error.va.sf-kcp-pwd.02":"Heslo má nesprávnu dĺžku","error.giftcard.no-balance":"Táto darčeková karta má nulový zostatok","error.giftcard.card-error":"V našich záznamoch nemáme žiadnu darčekovú kartu s týmto číslom","error.giftcard.currency-error":"Darčekové karty sú platné iba v mene, v ktorej boli vydané","amazonpay.signout":"Odhlásiť sa z Amazonu","amazonpay.changePaymentDetails":"Zmeniť podrobnosti o platbe","partialPayment.warning":"Ak chcete zaplatiť zostatok, vyberte iný spôsob platby","partialPayment.remainingBalance":"Zvyšný zostatok bude %{amount}","bankTransfer.beneficiary":"Príjemca","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referencie","bankTransfer.introduction":"Pokračujte vo vytváraní novej platby bankovým prevodom. Túto platbu môžete dokončiť pomocou informácií na nasledujúcej obrazovke.","bankTransfer.instructions":"Ďakujeme vám za nákup. Na dokončenie platby použite nasledujúce informácie.","bacs.accountHolderName":"Meno majiteľa bankového účtu","bacs.accountHolderName.invalid":"Neplatné meno majiteľa bankového účtu","bacs.accountNumber":"Číslo bankového účtu","bacs.accountNumber.invalid":"Neplatné číslo bankového účtu","bacs.bankLocationId":"Variabilný symbol","bacs.bankLocationId.invalid":"Neplatný variabilný symbol","bacs.consent.amount":"Súhlasím s tým, že nižšie uvedená čiastka bude odpísaná z môjho bankového účtu.","bacs.consent.account":"Potvrdzujem, že účet je na moje meno a som jediný podpisovateľ, ktorý je povinný autorizovať inkaso v tomto účte.",edit:"Upraviť","bacs.confirm":"Potvrdiť a zaplatiť","bacs.result.introduction":"Stiahnite si pokyny k inkasu (DDI/Mandát)","download.pdf":"Stiahnuť vo formáte PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe pre číslo karty","creditCard.encryptedCardNumber.aria.label":"Číslo karty","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe pre deň vypršania platnosti","creditCard.encryptedExpiryDate.aria.label":"Koniec platnosti","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe pre mesiac vypršania platnosti","creditCard.encryptedExpiryMonth.aria.label":"Mesiac skončenia platnosti","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe pre rok vypršania platnosti","creditCard.encryptedExpiryYear.aria.label":"Rok skončenia platnosti","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe pre bezpečnostný kód","creditCard.encryptedSecurityCode.aria.label":"Bezpečnostný kód","creditCard.encryptedPassword.aria.iframeTitle":"Iframe pre heslo","creditCard.encryptedPassword.aria.label":"Prvé 2 číslice hesla karty","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe pre číslo karty","giftcard.encryptedCardNumber.aria.label":"Číslo karty","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe pre PIN","giftcard.encryptedSecurityCode.aria.label":"Kód PIN",giftcardTransactionLimit:"Pre transakciu s touto darčekovou kartou je povolené maximum %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe pre číslo bankového účtu","ach.encryptedBankAccountNumber.aria.label":"Číslo účtu","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe pre smerovacie číslo banky","ach.encryptedBankLocationId.aria.label":"Smerovacie číslo ABA","twint.saved":"uložené",orPayWith:"alebo zaplatiť pomocou",invalidFormatExpects:"Neplatný formát. Očakávaný formát: %{format}","upi.qrCodeWaitingMessage":"Naskenujte QR kód pomocou preferovanej aplikácie UPI a dokončite platbu","upi.vpaWaitingMessage":"Otvorte aplikáciu UPI a potvrďte platbu","upi.modeSelection":"Ako by ste chceli použiť UPI?","upi.completePayment":"Dokončite svoju platbu","upi.mode.enterUpiId":"Zadajte UPI ID","upi.mode.qrCode":"QR kód","upi.mode.payByAnyUpi":"Plaťte pomocou ľubovoľnej aplikácie UPI","upi.collect.dropdown.label":"Zadajte UPI ID","upi.collect.field.label":"Zadajte UPI ID / VPA","onlineBanking.termsAndConditions":"Pokračovaním súhlasíte so %#zmluvnými podmienkami%#","onlineBankingPL.termsAndConditions":"Pokračovaním súhlasíte, že budete dodržiavať %#regulations%# a %#information obligation%# spoločnosti Przelewy24","ctp.loading.poweredByCtp":"Využíva technológiu Click to Pay","ctp.loading.intro":"Zisťujeme, či máte uložené nejaké karty v službe Click to Pay...","ctp.login.title":"Pokračovať do služby Click to Pay","ctp.login.subtitle":"Na pokračovanie zadajte e-mailovú adresu, ktorá je pripojená k službe Click to Pay.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Nie ste to vy?","ctp.logout.notYourCards":"Nie sú to vaše karty?","ctp.logout.notYourCard":"Nie je to vaša karta?","ctp.logout.notYourProfile":"Nie je to váš profil?","ctp.otp.fieldLabel":"Jednorazový kód","ctp.otp.resendCode":"Znova odoslať kód","ctp.otp.codeResent":"Kód bol znova odoslaný","ctp.otp.title":"Získajte prístup k vašim kartám v službe Click to Pay","ctp.otp.subtitle":"Zadajte kód %@, ktorý sme poslali na adresu %@, aby sme overili, že ste to vy.","ctp.otp.saveCookiesCheckbox.label":"Nabudúce preskočiť overovanie","ctp.otp.saveCookiesCheckbox.information":"Vyberte túto možnosť na zapamätanie v zariadení a prehliadači v zúčastnených obchodoch, čím urýchlite platbu. Neodporúča sa pre zdieľané zariadenia.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Vyberte túto možnosť na zapamätanie v zariadení a prehliadači","ctp.emptyProfile.message":"V tomto profile Click to Pay nie sú registrované žiadne karty","ctp.separatorText":"alebo použite","ctp.cards.title":"Dokončite platbu v službe Click to Pay","ctp.cards.subtitle":"Vyberte kartu, ktorú chcete použiť.","ctp.cards.expiredCard":"Platnosť uplynula","ctp.manualCardEntry":"Manuálne zadanie karty","ctp.aria.infoModalButton":"Čo znamená Click to Pay","ctp.infoPopup.title":"Click to Pay prináša jednoduchosť bezkontaktných platieb online","ctp.infoPopup.subtitle":"Rýchly a bezpečný spôsob platby podporovaný platobnými kartami Mastercard, Visa a ďalšími.","ctp.infoPopup.benefit1":"Click to Pay používa šifrovanie, aby boli vaše údaje v bezpečí","ctp.infoPopup.benefit2":"Používajte ju u obchodníkov po celom svete","ctp.infoPopup.benefit3":"Jedno nastavenie pre pohodlné platby v budúcnosti","ctp.errors.AUTH_INVALID":"Overenie bolo neplatné","ctp.errors.NOT_FOUND":"Nenašiel sa žiadny účet. Zadajte platný e-mail alebo pokračujte manuálnym zadaním karty.","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formát nie je podporovaný","ctp.errors.FRAUD":"Používateľský účet bol uzamknutý alebo zakázaný","ctp.errors.CONSUMER_ID_MISSING":"V žiadosti chýba identita spotrebiteľa","ctp.errors.ACCT_INACCESSIBLE":"Tento účet je momentálne nedostupný (napr. je zamknutý)","ctp.errors.CODE_INVALID":"Nesprávny overovací kód","ctp.errors.CODE_EXPIRED":"Platnosť tohto kódu uplynula","ctp.errors.RETRIES_EXCEEDED":"Limit počtu opakovaní pre generovanie jednorazového hesla bol prekročený","ctp.errors.OTP_SEND_FAILED":"Jednorazové heslo nebolo možné odoslať príjemcovi","ctp.errors.REQUEST_TIMEOUT":"Došlo k chybe. Skúste to znova alebo kartu zadajte ručne.","ctp.errors.UNKNOWN_ERROR":"Došlo k chybe. Skúste to znova alebo kartu zadajte ručne.","ctp.errors.SERVICE_ERROR":"Došlo k chybe. Skúste to znova alebo kartu zadajte ručne.","ctp.errors.SERVER_ERROR":"Došlo k chybe. Skúste to znova alebo kartu zadajte ručne.","ctp.errors.INVALID_PARAMETER":"Došlo k chybe. Skúste to znova alebo kartu zadajte ručne.","ctp.errors.AUTH_ERROR":"Došlo k chybe. Skúste to znova alebo kartu zadajte ručne.","paymentMethodsList.aria.label":"Výber spôsobu platby","companyDetails.name.invalid":"Zadajte názov spoločnosti","companyDetails.registrationNumber.invalid":"Zadajte registračné číslo","consent.checkbox.invalid":"Musíte súhlasiť s obchodnými podmienkami","form.instruction":"Všetky polia sú povinné, ak nie je označené inak.","trustly.descriptor":"Okamžitá banková platba","trustly.description1":"Platba priamo z ktoréhokoľvek bankového účtu so zabezpečením na úrovni banky","trustly.description2":"Žiadne karty, žiadne sťahovanie aplikácií, žiadna registrácia","ancv.input.label":"Vaša identifikácia ANCV","ancv.confirmPayment":"Na potvrdenie platby použite svoju aplikáciu ANCV.","ancv.form.instruction":"Na potvrdenie tejto platby je potrebná aplikácia Cheque-Vacances.","ancv.beneficiaryId.invalid":"Zadajte platnú e-mailovú adresu alebo ANCV ID","payme.openPayMeApp":"Dokončite platbu v aplikácii PayMe tak, že v aplikácii autorizujete platbu a počkáte na potvrdenie.","payme.redirectButtonLabel":"Otvoriť aplikáciu PayMe","payme.scanQrCode":"Dokončite platbu pomocou kódu QR","payme.timeToPay":"Tento kód QR platí %@","payme.instructions.steps":"Otvorte aplikáciu PayMe.%@Autorizujte platbu naskenovaním QR kódu.%@Dokončite platbu v aplikácii a počkajte na potvrdenie.","payme.instructions.footnote":"Nezatvárajte túto stránku pred dokončením platby","payByBankAISDD.disclaimer.header":"Používajte Pay by Bank na okamžitú platbu z akéhokoľvek bankového účtu.","payByBankAISDD.disclaimer.body":"Pripojením svojho bankového účtu dávate súhlas na odpísanie akejkoľvek dlžnej sumy z vášho účtu za používanie našich služieb a/alebo nákup našich produktov, a to až do odvolania tohto súhlasu.","paymentMethodBrand.other":"Iné"}},65746:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Plătiți","payButton.redirecting":"Se redirecționează...","payButton.with":"Plătiți %{value} cu %{maskedData}","payButton.saveDetails":"Salvați detaliile",close:"Închidere",storeDetails:"Salvează pentru următoarea mea plată",readMore:"Citiți mai mult","creditCard.holderName":"Numele de pe card","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"Completați numele după cum figurează pe card","creditCard.numberField.title":"Număr card","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Data expirării","creditCard.expiryDateField.placeholder":"LL/AA","creditCard.expiryDateField.month":"Lună","creditCard.expiryDateField.month.placeholder":"LL","creditCard.expiryDateField.year.placeholder":"AA","creditCard.expiryDateField.year":"An","creditCard.cvcField.title":"Cod de securitate","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Reține pentru data viitoare","creditCard.cvcField.placeholder.4digits":"4 cifre","creditCard.cvcField.placeholder.3digits":"3 cifre","creditCard.taxNumber.placeholder":"AALLZZ / 0123456789",installments:"Număr de rate",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} luni","installments.oneTime":"Plată unică","installments.installments":"Plată în rate","installments.revolving":"Plată recurentă","sepaDirectDebit.ibanField.invalid":"Numărul de cont este incorect","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"Nume posesor","sepa.ibanNumber":"Număr cont (IBAN)","error.title":"Eroare","error.subtitle.redirect":"Redirecționare eșuată","error.subtitle.payment":"Plată eșuată","error.subtitle.refused":"Plată refuzată","error.message.unknown":"S-a produs o eroare necunoscută","errorPanel.title":"Erori existente","idealIssuer.selectField.title":"Bancă","idealIssuer.selectField.placeholder":"Selectați-vă banca","creditCard.success":"Plată reușită",loading:"Se încarcă...",continue:"Continuare",continueTo:"Continuați către","wechatpay.timetopay":"Trebuie să achitați %@","sr.wechatpay.timetopay":"Aveți la dispoziție %#minute%# %#secunde%# pentru a plăti","wechatpay.scanqrcode":"Scanați codul QR",personalDetails:"Informații personale",companyDetails:"Informații societate","companyDetails.name":"Denumirea societății","companyDetails.registrationNumber":"Număr de înregistrare",socialSecurityNumber:"Cod numeric personal",firstName:"Prenume","firstName.invalid":"Completați prenumele dvs.",infix:"Titulatură",lastName:"Nume de familie","lastName.invalid":"Completați numele dvs. de familie",mobileNumber:"Număr de mobil","mobileNumber.invalid":"Număr de telefon mobil incorect",city:"Oraș",postalCode:"Cod poștal","postalCode.optional":"Cod poștal (opțional)",countryCode:"Codul țării",telephoneNumber:"Număr de telefon",dateOfBirth:"Data nașterii",shopperEmail:"Adresă de e-mail",gender:"Gen","gender.notselected":"Selectați sexul dvs.",male:"Bărbat",female:"Femeie",billingAddress:"Adresa de facturare",street:"Strada",stateOrProvince:"Județ sau provincie",country:"Țară/regiune",houseNumberOrName:"Număr",separateDeliveryAddress:"Specificați o adresă de livrare separată",deliveryAddress:"Adresă de livrare","deliveryAddress.firstName":"Prenumele destinatarului","deliveryAddress.lastName":"Numele de familie al destinatarului",zipCode:"Cod poștal",apartmentSuite:"Apartament",provinceOrTerritory:"Provincie sau teritoriu",cityTown:"Oraș/localitate",address:"Adresă","address.placeholder":"Găsiți-vă adresa","address.errors.incomplete":"Pentru a continua, introduceți o adresă","address.enterManually":"Introduceți adresa manual",state:"Stat","field.title.optional":"(opțional)","creditCard.cvcField.title.optional":"Cod de securitate (opțional)","issuerList.wallet.placeholder":"Selectați-vă portofelul",privacyPolicy:"Politica de confidențialitate","afterPay.agreement":"Sunt de acord cu %@ aparținând Riverty","riverty.termsAndConditions":"Sunt de acord cu %#Termenii și condițiile%# generale pentru metoda de plată Riverty. Politica de confidențialitate Riverty este disponibilă %#aici%#.",paymentConditions:"condiții de plată",openApp:"Deschideți aplicația","voucher.readInstructions":"Citiți instrucțiunile","voucher.introduction":"Vă mulțumim pentru cumpărături, vă rugăm să utilizați următorul cupon pentru a vă finaliza plata.","voucher.expirationDate":"Data de expirare","voucher.alternativeReference":"Referință alternativă","dragonpay.voucher.non.bank.selectField.placeholder":"Selectați furnizorul dvs.","dragonpay.voucher.bank.selectField.placeholder":"Selectați banca dvs.","voucher.paymentReferenceLabel":"Referința plății","voucher.surcharge":"Include suprataxa de %@","voucher.introduction.doku":"Vă mulțumim pentru cumpărături, vă rugăm să utilizați următoarele informații pentru a vă finaliza plata.","voucher.shopperName":"Nume cumpărător","voucher.merchantName":"Comerciant","voucher.introduction.econtext":"Vă mulțumim pentru cumpărături, vă rugăm să utilizați următoarele informații pentru a vă finaliza plata.","voucher.telephoneNumber":"Număr de telefon","voucher.shopperReference":"Referință cumpărător","voucher.collectionInstitutionNumber":"Număr instituție de colectare","voucher.econtext.telephoneNumber.invalid":"Numărul de telefon trebuie să aibă 10 sau 11 cifre","boletobancario.btnLabel":"Generare Boleto","boleto.sendCopyToEmail":"Trimite o copie la adresa mea de e-mail","button.copy":"Copiere","button.download":"Descărcare","boleto.socialSecurityNumber.invalid":"Completați un număr CPF/CNPJ valabil","creditCard.storedCard.description.ariaLabel":"Cardul memorat se termină în %@","voucher.entity":"Entitate",donateButton:"Donați",notNowButton:"Nu acum",thanksForYourSupport:"Vă mulțumim pentru sprijin!","resultMessages.preauthorized":"Detalii salvate",preauthorizeWith:"Preautorizare cu",confirmPreauthorization:"Confirmați preautorizarea",confirmPurchase:"Confirmați achiziția",applyGiftcard:"Valorificare",giftcardBalance:"Soldul de pe cardul cadou",deductedBalance:"Sold scăzut","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Primele 2 cifre ale parolei aferente cardului","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Parolă incorectă","creditCard.taxNumber":"Data de naștere a posesorului de card sau codul unic de înregistrare al societății","creditCard.taxNumber.label":"Data de naștere a posesorului de card (AALLZZ) sau numărul de înregistrare al societății (10 cifre).","creditCard.taxNumber.labelAlt":"Numărul de înregistrare al societății (10 cifre)","creditCard.taxNumber.invalid":"Data de naștere a posesorului de card sau numărul de înregistrare al societății este incorect","storedPaymentMethod.disable.button":"Ștergere","storedPaymentMethod.disable.confirmation":"Ștergeți metoda de plată memorată","storedPaymentMethod.disable.confirmButton":"Da, șterge","storedPaymentMethod.disable.cancelButton":"Anulare","ach.bankAccount":"Cont bancar","ach.accountHolderNameField.title":"Numele titularului de cont","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Numele titularului de cont este incorect","ach.accountNumberField.title":"Număr de cont","ach.accountNumberField.invalid":"Numărul de cont este incorect","ach.accountLocationField.title":"Număr de direcționare ABA","ach.accountLocationField.invalid":"Număr de direcționare ABA incorect","ach.savedBankAccount":"Cont bancar salvat","ach.savings":"Cont de economii","ach.checking":"Cont curent","select.state":"Selectați statul","select.stateOrProvince":"Selectați județul sau provincia","select.provinceOrTerritory":"Selectați provincia sau teritoriul","select.country":"Selectați țara/regiunea","select.noOptionsFound":"Nu s-a găsit nicio opțiune","select.filter.placeholder":"Căutare...","telephoneNumber.invalid":"Număr de telefon incorect",qrCodeOrApp:"sau","paypal.processingPayment":"Se prelucrează plata...",generateQRCode:"Generați codul QR","await.waitForConfirmation":"Se așteaptă confirmarea","mbway.confirmPayment":"Confirmați plata în aplicația MB WAY","shopperEmail.invalid":"Adresă de e-mail incorectă","dateOfBirth.format":"ZZ/LL/AAAA","dateOfBirth.invalid":"Completați o dată de naștere valabilă care să indice că aveți cel puțin 18 ani.","blik.confirmPayment":"Deschideți aplicația dvs. de banking pentru a confirma plata.","blik.invalid":"Introduceți 6 cifre","blik.code":"Cod din 6 cifre","blik.help":"Obțineți codul din aplicația dvs. de banking.","swish.pendingMessage":"După ce scanați, starea poate fi „în așteptare” timp de maximum 10 minute. Încercările de a plăti din nou în acest răstimp pot genera prelevări multiple de fonduri.","field.valid":"Câmp valid","field.invalid":"Câmp incorect","error.va.gen.01":"Câmp incomplet","error.va.gen.02":"Câmp incorect","error.va.sf-cc-num.01":"Completați un număr valabil de card","error.va.sf-cc-num.02":"Completați numărul cardului","error.va.sf-cc-num.03":"Completați un brand de card acceptat","error.va.sf-cc-num.04":"Completați numărul complet al cardului","error.va.sf-cc-dat.01":"Completați o dată de expirare valabilă","error.va.sf-cc-dat.02":"Completați o dată de expirare valabilă","error.va.sf-cc-dat.03":"Card de credit pe cale să expire","error.va.sf-cc-dat.04":"Completați data de expirare","error.va.sf-cc-dat.05":"Completați data de expirare completă","error.va.sf-cc-mth.01":"Completați luna de expirare","error.va.sf-cc-yr.01":"Completați anul de expirare","error.va.sf-cc-yr.02":"Completați anul de expirare complet","error.va.sf-cc-cvc.01":"Completați codul de securitate","error.va.sf-cc-cvc.02":"Completați codul de securitate complet","error.va.sf-ach-num.01":"Câmpul pentru numărul de cont bancar este necompletat","error.va.sf-ach-num.02":"Numărul contului bancar are lungimea greșită","error.va.sf-ach-loc.01":"Câmpul pentru numărul de rutare al băncii este necompletat","error.va.sf-ach-loc.02":"Numărul de rutare al băncii are lungimea greșită","error.va.sf-kcp-pwd.01":"Câmpul pentru parolă este necompletat","error.va.sf-kcp-pwd.02":"Parola are lungimea greșită","error.giftcard.no-balance":"Acest card cadou are soldul zero","error.giftcard.card-error":"În evidențele noastre nu figurează niciun card cadou cu acest număr","error.giftcard.currency-error":"Cardurile cadou sunt valabile numai în moneda în care au fost emise","amazonpay.signout":"Deconectați-vă de pe platforma Amazon","amazonpay.changePaymentDetails":"Modificați detaliile de plată","partialPayment.warning":"Pentru a achita suma rămasă, selectați o altă metodă de plată","partialPayment.remainingBalance":"Soldul rămas va fi de %{amount}","bankTransfer.beneficiary":"Beneficiar","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referinţă","bankTransfer.introduction":"Continuați să creați o nouă plată prin transfer bancar. Puteți utiliza detaliile din ecranul următor pentru a finaliza această plată.","bankTransfer.instructions":"Vă mulțumim pentru cumpărături, vă rugăm să utilizați următoarele informații pentru a vă finaliza plata.","bacs.accountHolderName":"Numele titularului contului bancar","bacs.accountHolderName.invalid":"Numele titularului contului bancar este incorect","bacs.accountNumber":"Numărul contului bancar","bacs.accountNumber.invalid":"Numărul contului bancar este incorect","bacs.bankLocationId":"Cod de identificare bancară","bacs.bankLocationId.invalid":"Cod de identificare bancară incorect","bacs.consent.amount":"Sunt de acord ca suma menționată mai sus să fie dedusă din contul meu bancar.","bacs.consent.account":"Confirm că sunt titularul acestui cont și că sunt singurul semnatar necesar pentru autorizarea debitului direct pentru acest cont.",edit:"Editare","bacs.confirm":"Confirmați și plătiți","bacs.result.introduction":"Descărcați instrucțiunile de debitare directă (DDI/mandat)","download.pdf":"Descărcați PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe pentru numărul cardului","creditCard.encryptedCardNumber.aria.label":"Număr card","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe pentru data de expirare","creditCard.encryptedExpiryDate.aria.label":"Data expirării","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe pentru luna de expirare","creditCard.encryptedExpiryMonth.aria.label":"Luna de expirare","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe pentru anul de expirare","creditCard.encryptedExpiryYear.aria.label":"Anul de expirare","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe pentru codul de securitate","creditCard.encryptedSecurityCode.aria.label":"Cod de securitate","creditCard.encryptedPassword.aria.iframeTitle":"Iframe pentru parolă","creditCard.encryptedPassword.aria.label":"Primele 2 cifre ale parolei aferente cardului","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe pentru numărul cardului","giftcard.encryptedCardNumber.aria.label":"Număr card","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe pentru PIN","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Pentru acest card cadou, suma maximă permisă per tranzacție este de %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe pentru numărul de cont bancar","ach.encryptedBankAccountNumber.aria.label":"Număr de cont","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe pentru numărul de identificare bancară","ach.encryptedBankLocationId.aria.label":"Număr de direcționare ABA","twint.saved":"salvat",orPayWith:"sau plătiți cu",invalidFormatExpects:"Format nevalid. Format solicitat: %{format}","upi.qrCodeWaitingMessage":"Scanați codul QR folosind aplicația UPI preferată pentru a finaliza plata","upi.vpaWaitingMessage":"Deschideți aplicația UPI pentru a confirma plata","upi.modeSelection":"Cum doriți să utilizați UPI?","upi.completePayment":"Finalizați plata","upi.mode.enterUpiId":"Completați identificatorul UPI","upi.mode.qrCode":"Cod QR","upi.mode.payByAnyUpi":"Plătiți cu orice aplicație UPI","upi.collect.dropdown.label":"Introduceți ID-ul UPI","upi.collect.field.label":"Completați identificatorul UPI/VPA","onlineBanking.termsAndConditions":"Continuând, sunteți de acord cu %#terms and conditions%#","onlineBankingPL.termsAndConditions":"Continuând, sunteți de acord cu %#reglementările%# și cu %#obligația de informare%# a Przelewy24","ctp.loading.poweredByCtp":"Susținut de Click to Pay","ctp.loading.intro":"Verificăm dacă aveți vreun card salvat cu Click to Pay...","ctp.login.title":"Continuați către Click to Pay","ctp.login.subtitle":"Introduceți adresa de e-mail care este conectată la Click to Pay pentru a continua.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Nu sunteți dvs.?","ctp.logout.notYourCards":"Nu sunt cardurile dvs.?","ctp.logout.notYourCard":"Nu este cardul dvs.?","ctp.logout.notYourProfile":"Nu este profilul dvs.?","ctp.otp.fieldLabel":"Cod unic","ctp.otp.resendCode":"Retrimiteți codul","ctp.otp.codeResent":"Cod retrimis","ctp.otp.title":"Accesați-vă cardurile Click to Pay","ctp.otp.subtitle":"Introduceți codul %@ pe care l-am trimis la %@ pentru a confirma că sunteți dvs.","ctp.otp.saveCookiesCheckbox.label":"Omiteți verificarea data viitoare","ctp.otp.saveCookiesCheckbox.information":"Selectați pentru a fi reținut pe dispozitiv și în browserul dvs. la magazinele participante pentru o plată mai rapidă. Nerecomandat pentru dispozitivele partajate.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Selectați pentru a fi memorat pe dispozitiv și browser","ctp.emptyProfile.message":"Niciun card înregistrat în acest profil Click to Pay","ctp.separatorText":"sau folosiți","ctp.cards.title":"Finalizați plata cu Click to Pay","ctp.cards.subtitle":"Selectați un card pe care să îl utilizați.","ctp.cards.expiredCard":"Expirat","ctp.manualCardEntry":"Introducere manuală a cardului","ctp.aria.infoModalButton":"Ce este Click to Pay","ctp.infoPopup.title":"Click to Pay vă oferă facilitățile contactless, online","ctp.infoPopup.subtitle":"O metodă de plată rapidă și sigură, compatibilă cu Mastercard, Visa și alte carduri de plată.","ctp.infoPopup.benefit1":"Click to Pay folosește criptarea pentru a vă păstra informațiile în siguranță","ctp.infoPopup.benefit2":"Utilizați-l la comercianții din întreaga lume","ctp.infoPopup.benefit3":"Configurați o dată pentru a plăti fără probleme în viitor","ctp.errors.AUTH_INVALID":"Autentificare incorectă","ctp.errors.NOT_FOUND":"Nu s-a găsit niciun cont; introduceți o adresă de e-mail valabilă sau continuați completând manual cardul","ctp.errors.ID_FORMAT_UNSUPPORTED":"Format neacceptat","ctp.errors.FRAUD":"Contul de utilizator a fost blocat sau dezactivat","ctp.errors.CONSUMER_ID_MISSING":"Identitatea consumatorului lipsește din solicitare","ctp.errors.ACCT_INACCESSIBLE":"Acest cont nu este disponibil în prezent, de exemplu, este blocat.","ctp.errors.CODE_INVALID":"Cod de verificare incorect","ctp.errors.CODE_EXPIRED":"Acest cod a expirat","ctp.errors.RETRIES_EXCEEDED":"A fost depășită limita numărului de încercări pentru generarea parolei unice","ctp.errors.OTP_SEND_FAILED":"Parola unică nu a putut fi trimisă destinatarului","ctp.errors.REQUEST_TIMEOUT":"Ceva nu a mers bine, încercați din nou sau introduceți manual cardul","ctp.errors.UNKNOWN_ERROR":"Ceva nu a mers bine, încercați din nou sau introduceți manual cardul","ctp.errors.SERVICE_ERROR":"Ceva nu a mers bine, încercați din nou sau introduceți manual cardul","ctp.errors.SERVER_ERROR":"Ceva nu a mers bine, încercați din nou sau introduceți manual cardul","ctp.errors.INVALID_PARAMETER":"Ceva nu a mers bine, încercați din nou sau introduceți manual cardul","ctp.errors.AUTH_ERROR":"Ceva nu a mers bine, încercați din nou sau introduceți manual cardul","paymentMethodsList.aria.label":"Alegeți o metodă de plată","companyDetails.name.invalid":"Completați numele societății","companyDetails.registrationNumber.invalid":"Completați numărul de înregistrare","consent.checkbox.invalid":"Trebuie să fiți de acord cu termenii și condițiile","form.instruction":"Toate câmpurile sunt obligatorii, numai dacă nu este marcat altfel.","trustly.descriptor":"Plată bancară instantanee","trustly.description1":"Plătiți direct din oricare dintre conturile dvs. bancare, cu sprijinul unei securități de nivel bancar","trustly.description2":"Fără carduri, fără descărcări de aplicații, fără înregistrare","ancv.input.label":"Identificarea dvs. ANCV","ancv.confirmPayment":"Utilizați aplicația ANCV pentru a confirma plata.","ancv.form.instruction":"Aplicația Cheque-Vacances este necesară pentru a valida această plată.","ancv.beneficiaryId.invalid":"Completați o adresă de e-mail sau un ID ANCV valabil","payme.openPayMeApp":"Finalizați plata în aplicația PayMe, autorizând plata în aplicație, și așteptați confirmarea.","payme.redirectButtonLabel":"Deschideți aplicația PayMe","payme.scanQrCode":"Finalizați plata folosind codul QR","payme.timeToPay":"Acest cod QR este valabil pentru %@","payme.instructions.steps":"Deschideți aplicația PayMe.%@Scanați codul QR pentru a autoriza plata.%@Finalizați plata în aplicație și așteptați confirmarea.","payme.instructions.footnote":"Nu închideți această pagină înainte de finalizarea plății.","payByBankAISDD.disclaimer.header":"Utilizați Plată prin bancă pentru a plăti instantaneu din orice cont bancar.","payByBankAISDD.disclaimer.body":"Prin conectarea contului dvs. bancar, autorizați debitarea din contul dvs. a oricărei sume datorate cu privire la utilizarea serviciilor noastre și/sau achiziționarea produselor noastre, până la revocarea acestei autorizații.","paymentMethodBrand.other":"Altele"}},75896:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Pagar","payButton.redirecting":"Redirecionar...","payButton.with":"Pagar %{value} com %{maskedData}","payButton.saveDetails":"Guardar detalhes",close:"Fechar",storeDetails:"Guardar para o meu próximo pagamento",readMore:"Ler mais","creditCard.holderName":"Nome no cartão","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"Introduzir o nome como mostrado no cartão","creditCard.numberField.title":"Número de cartão","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Data de validade","creditCard.expiryDateField.placeholder":"MM/AA","creditCard.expiryDateField.month":"Mês","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"AA","creditCard.expiryDateField.year":"Ano","creditCard.cvcField.title":"Código de segurança","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Lembre-se para a próxima vez","creditCard.cvcField.placeholder.4digits":"4 dígitos","creditCard.cvcField.placeholder.3digits":"3 dígitos","creditCard.taxNumber.placeholder":"AAMMDD / 0123456789",installments:"Número de prestações",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} meses","installments.oneTime":"Pagamento único","installments.installments":"Pagamento em prestações","installments.revolving":"Pagamento rotativo","sepaDirectDebit.ibanField.invalid":"Número de conta inválido","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"Nome do Titular","sepa.ibanNumber":"Número da conta (IBAN)","error.title":"Erro","error.subtitle.redirect":"Falha ao redirecionar","error.subtitle.payment":"O pagamento falhou","error.subtitle.refused":"Pagamento recusado","error.message.unknown":"Ocorreu um erro desconhecido","errorPanel.title":"Erros existentes","idealIssuer.selectField.title":"Banco","idealIssuer.selectField.placeholder":"Selecione o seu banco","creditCard.success":"Pagamento bem-sucedido",loading:"A carregar",continue:"Continuar",continueTo:"Continuar para","wechatpay.timetopay":"Tem %@ para pagar","sr.wechatpay.timetopay":"Tem %#minutos%# %#segundos%# para pagar","wechatpay.scanqrcode":"Digitalizar código QR",personalDetails:"Detalhes pessoais",companyDetails:"Detalhes da empresa","companyDetails.name":"Nome da empresa","companyDetails.registrationNumber":"Número de registo",socialSecurityNumber:"Número de previdência social",firstName:"Nome próprio","firstName.invalid":"Introduza o seu nome próprio",infix:"Prefixo",lastName:"Apelido","lastName.invalid":"Introduza o seu apelido",mobileNumber:"Número de telemóvel","mobileNumber.invalid":"Número de telemóvel inválido",city:"Cidade",postalCode:"Código postal","postalCode.optional":"Código postal (opcional)",countryCode:"Código do país",telephoneNumber:"Número de telefone",dateOfBirth:"Data de nascimento",shopperEmail:"Endereço de correio eletrónico",gender:"Género","gender.notselected":"Selecione o seu género",male:"Homem",female:"Feminino",billingAddress:"Morada de cobrança",street:"Rua",stateOrProvince:"Estado ou província",country:"País/região",houseNumberOrName:"Número de porta",separateDeliveryAddress:"Especifique uma morada de entrega separada",deliveryAddress:"Morada de entrega","deliveryAddress.firstName":"Nome próprio do destinatário","deliveryAddress.lastName":"Apelido do destinatário",zipCode:"Código postal",apartmentSuite:"Apartamento/Suite",provinceOrTerritory:"Província ou Território",cityTown:"Cidade/cidade",address:"Morada","address.placeholder":"Encontre o seu endereço","address.errors.incomplete":"Introduza um endereço para continuar","address.enterManually":"Introduza o endereço manualmente",state:"Estado","field.title.optional":"(opcional)","creditCard.cvcField.title.optional":"Código de segurança (opcional)","issuerList.wallet.placeholder":"Selecione a sua carteira",privacyPolicy:"Política de Privacidade","afterPay.agreement":"Concordo com o %@ da Riverty","riverty.termsAndConditions":"Concordo com os %#Termos e Condições%# gerais para o Método de Pagamento Riverty. A política de privacidade da Riverty pode ser encontrada %#aqui%#.",paymentConditions:"condições de pagamento",openApp:"Abra a aplicação","voucher.readInstructions":"Ler instruções","voucher.introduction":"Obrigado pela sua compra, utilize o seguinte cupão para completar o seu pagamento.","voucher.expirationDate":"Data de validade","voucher.alternativeReference":"Referência alternativa","dragonpay.voucher.non.bank.selectField.placeholder":"Selecione seu fornecedor","dragonpay.voucher.bank.selectField.placeholder":"Selecione o seu banco","voucher.paymentReferenceLabel":"Referência de pagamento","voucher.surcharge":"Incl. %@ sobretaxa","voucher.introduction.doku":"Obrigado pela sua compra, use as seguintes informações para concluir o seu pagamento.","voucher.shopperName":"Nome do comprador","voucher.merchantName":"Comerciante","voucher.introduction.econtext":"Obrigado pela sua compra, use as seguintes informações para concluir o seu pagamento.","voucher.telephoneNumber":"Número de telefone","voucher.shopperReference":"Referência do consumidor","voucher.collectionInstitutionNumber":"Número da instituição de recolha","voucher.econtext.telephoneNumber.invalid":"O número de telefone deve ter 10 ou 11 dígitos","boletobancario.btnLabel":"Gerar comprovativo","boleto.sendCopyToEmail":"Enviar uma cópia para o meu e-mail","button.copy":"Copiar","button.download":"Download","boleto.socialSecurityNumber":"CPF/CNPJ","boleto.socialSecurityNumber.invalid":"Introduza um número de CPF/CNPJ válido","creditCard.storedCard.description.ariaLabel":"O cartão armazenado termina em %@","voucher.entity":"Entidade",donateButton:"Doar",notNowButton:"Agora não",thanksForYourSupport:"Obrigado pelo seu apoio!","resultMessages.preauthorized":"Detalhes guardados",preauthorizeWith:"Pré-autorizar com",confirmPreauthorization:"Confirmar pré-autorização",confirmPurchase:"Confirmar compra",applyGiftcard:"Resgatar",giftcardBalance:"Saldo do cartão-presente",deductedBalance:"Saldo deduzido","creditCard.pin.title":"Pin","creditCard.encryptedPassword.label":"Primeiros 2 dígitos da palavra-passe do cartão","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Palavra-passe inválida","creditCard.taxNumber":"Data de nascimento do titular do cartão ou número de registo da Empresa","creditCard.taxNumber.label":"Data de nascimento do titular do cartão (AAMMDD) ou número de registo da Empresa (10 dígitos)","creditCard.taxNumber.labelAlt":"Número de registo da empresa (10 dígitos)","creditCard.taxNumber.invalid":"Data de nascimento do titular do cartão ou número de registo empresarial inválido","storedPaymentMethod.disable.button":"Remover","storedPaymentMethod.disable.confirmation":"Remover método de pagamento guardado","storedPaymentMethod.disable.confirmButton":"Sim, remover","storedPaymentMethod.disable.cancelButton":"Cancelar","ach.bankAccount":"Conta bancária","ach.accountHolderNameField.title":"Nome do titular da conta","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Nome do titular da conta inválido","ach.accountNumberField.title":"Número da conta","ach.accountNumberField.invalid":"Número de conta inválido","ach.accountLocationField.title":"Número de encaminhamento da ABA","ach.accountLocationField.invalid":"Número de encaminhamento ABA inválido","ach.savedBankAccount":"Conta bancária guardada","ach.savings":"Conta poupança","ach.checking":"Conta corrente","select.state":"Selecione estado","select.stateOrProvince":"Selecione estado ou província","select.provinceOrTerritory":"Selecionar província ou território","select.country":"Selecionar país/região","select.noOptionsFound":"Nenhuma opção encontrada","select.filter.placeholder":"Pesquisar...","telephoneNumber.invalid":"Número de telefone inválido",qrCodeOrApp:"ou","paypal.processingPayment":"A processar pagamento...",generateQRCode:"Gerar código QR","await.waitForConfirmation":"A aguardar confirmação","mbway.confirmPayment":"Confirme o seu pagamento na aplicação MB WAY","shopperEmail.invalid":"Endereço de e-mail inválido","dateOfBirth.format":"DD/MM/AAAA","dateOfBirth.invalid":"Insira uma data de nascimento válida que indique que tem pelo menos 18 anos","blik.confirmPayment":"Abra a sua aplicação bancária para confirmar o pagamento.","blik.invalid":"Digite 6 números","blik.code":"Código de 6 dígitos","blik.help":"Obtenha o código da sua aplicação bancária.","swish.pendingMessage":"Após a leitura, o estado pode estar pendente por até 10 minutos. Tentar pagar novamente dentro desse prazo pode resultar em várias cobranças.","field.valid":"Campo válido","field.invalid":"Campo inválido","error.va.gen.01":"Campo incompleto","error.va.gen.02":"Campo inválido","error.va.sf-cc-num.01":"Introduza um número de cartão válido","error.va.sf-cc-num.02":"Introduza o número do cartão","error.va.sf-cc-num.03":"Introduza uma marca de cartão suportada","error.va.sf-cc-num.04":"Introduza o número do cartão completo","error.va.sf-cc-dat.01":"Introduza uma data de validade válida","error.va.sf-cc-dat.02":"Introduza uma data de validade válida","error.va.sf-cc-dat.03":"Cartão de crédito prestes a expirar","error.va.sf-cc-dat.04":"Introduza a data de validade","error.va.sf-cc-dat.05":"Introduza a data de validade completa","error.va.sf-cc-mth.01":"Introduza o mês de validade","error.va.sf-cc-yr.01":"Introduza o ano de validade","error.va.sf-cc-yr.02":"Introduza o ano de validade completa","error.va.sf-cc-cvc.01":"Introduza o código de segurança","error.va.sf-cc-cvc.02":"Introduza o código de segurança completo","error.va.sf-ach-num.01":"O campo de número da conta bancária está vazio","error.va.sf-ach-num.02":"O número da conta bancária tem o comprimento errado","error.va.sf-ach-loc.01":"O campo do número de encaminhamento bancário está vazio","error.va.sf-ach-loc.02":"O número de identificação bancária tem o comprimento errado","error.va.sf-kcp-pwd.01":"O campo de palavra-passe está vazio","error.va.sf-kcp-pwd.02":"A palavra-passe tem o comprimento errado","error.giftcard.no-balance":"Este cartão oferta tem saldo zero","error.giftcard.card-error":"Nos nossos registos não temos qualquer cartão oferta com este número","error.giftcard.currency-error":"Os cartões oferta só são válidos na moeda em que foram emitidos","amazonpay.signout":"Sair da Amazon","amazonpay.changePaymentDetails":"Alterar detalhes de pagamento","partialPayment.warning":"Selecione outro método de pagamento para pagar o restante","partialPayment.remainingBalance":"O saldo restante será de %{amount}","bankTransfer.beneficiary":"Beneficiário(a)","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referência","bankTransfer.introduction":"Continue para criar um novo pagamento de transferência bancária. Pode usar os detalhes no ecrã a seguir para finalizar este pagamento.","bankTransfer.instructions":"Obrigado pela sua compra, use as seguintes informações para concluir o seu pagamento.","bacs.accountHolderName":"Nome do titular da conta bancária","bacs.accountHolderName.invalid":"Nome do titular da conta bancária inválido","bacs.accountNumber":"Número da conta bancária","bacs.accountNumber.invalid":"Número de conta bancária inválido","bacs.bankLocationId":"Código de classificação","bacs.bankLocationId.invalid":"Código de classificação inválido","bacs.consent.amount":"Concordo que o valor acima seja deduzido da minha conta bancária.","bacs.consent.account":"Confirmo que a conta está em meu nome e sou o único signatário necessário para autorizar o débito automático nesta conta.",edit:"Editar","bacs.confirm":"Confirmar e pagar","bacs.result.introduction":"Descarregue a sua Instrução de Débito Direto (DDI / Mandato)","download.pdf":"Descarregar PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe para o número do cartão","creditCard.encryptedCardNumber.aria.label":"Número de cartão","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe para data de validade","creditCard.encryptedExpiryDate.aria.label":"Data de validade","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe para o mês de validade","creditCard.encryptedExpiryMonth.aria.label":"Mês de validade","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe para o ano de validade","creditCard.encryptedExpiryYear.aria.label":"Ano de validade","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe para código de segurança","creditCard.encryptedSecurityCode.aria.label":"Código de segurança","creditCard.encryptedPassword.aria.iframeTitle":"Iframe para palavra-passe","creditCard.encryptedPassword.aria.label":"Primeiros 2 dígitos da palavra-passe do cartão","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe para o número do cartão","giftcard.encryptedCardNumber.aria.label":"Número de cartão","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe para pin","giftcard.encryptedSecurityCode.aria.label":"Pin",giftcardTransactionLimit:"Máximo de %{amount} permitido por transação neste cartão presente","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe para número de conta bancária","ach.encryptedBankAccountNumber.aria.label":"Número da conta","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe para número de identificação bancária","ach.encryptedBankLocationId.aria.label":"Número de encaminhamento da ABA","pix.instructions":"Abra a aplicação com a chave registada PIX, escolha Pagar com PIX e digitalize o QR Code ou copie e cole o código","twint.saved":"salvou",orPayWith:"ou pague com",invalidFormatExpects:"Formato inválido. Formato esperado: %{format}","upi.qrCodeWaitingMessage":"Digitalize o código QR usando a sua aplicação UPI preferida para concluir o pagamento","upi.vpaWaitingMessage":"Abra a aplicação UPI para confirmar o pagamento","upi.modeSelection":"Como gostaria de usar a UPI?","upi.completePayment":"Conclua o seu pagamento","upi.mode.enterUpiId":"Introduza o ID da aplicação UPI","upi.mode.qrCode":"Código QR","upi.mode.payByAnyUpi":"Pague com qualquer aplicação UPI","upi.collect.dropdown.label":"Introduza o ID da app UPI","upi.collect.field.label":"Introduza o ID da aplicação UPI/VPA","onlineBanking.termsAndConditions":"Ao continuar, concorda com os %#termos e condições%#","onlineBankingPL.termsAndConditions":"Ao continuar, concorda com o %#regulamento%# e a %#obrigação de informação%# da Przelewy24","ctp.loading.poweredByCtp":"Fornecido pelo Click to Pay","ctp.loading.intro":"Estamos a verificar se tem algum cartão Click to Pay guardado...","ctp.login.title":"Continuar para o Click to Pay","ctp.login.subtitle":"Introduza o endereço de e-mail associado ao Click to Pay para continuar.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Não é você?","ctp.logout.notYourCards":"Não são os seus cartões?","ctp.logout.notYourCard":"Não é o seu cartão?","ctp.logout.notYourProfile":"Não é o seu perfil?","ctp.otp.fieldLabel":"Código único","ctp.otp.resendCode":"Reenviar código","ctp.otp.codeResent":"Código reenviado","ctp.otp.title":"Aceda aos seus cartões Click to Pay","ctp.otp.subtitle":"Introduza o código %@ enviado para %@ para verificar se é você.","ctp.otp.saveCookiesCheckbox.label":"Saltar verificação da próxima vez","ctp.otp.saveCookiesCheckbox.information":"Selecione para ser memorizado no seu dispositivo e navegador nas lojas participantes, para uma finalização de compra mais rápida. Não recomendado para dispositivos partilhados.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Selecione para ser memorizado no seu dispositivo e navegador","ctp.emptyProfile.message":"Nenhum cartão registado neste perfil Click to Pay","ctp.separatorText":"ou utilize","ctp.cards.title":"Pagamento completo com Click to Pay","ctp.cards.subtitle":"Selecione um cartão para usar.","ctp.cards.expiredCard":"Expirado","ctp.manualCardEntry":"Introdução manual do cartão","ctp.aria.infoModalButton":"O que é Click to Pay","ctp.infoPopup.title":"O Click to Pay traz a facilidade do contactless online","ctp.infoPopup.subtitle":"Um método de pagamento rápido e seguro suportado pela Mastercard, Visa e outros cartões de pagamento.","ctp.infoPopup.benefit1":"Click to Pay utiliza encriptação para manter as suas informações seguras","ctp.infoPopup.benefit2":"Use-o com comerciantes em todo o mundo","ctp.infoPopup.benefit3":"Configure uma vez para pagamentos sem complicações no futuro","ctp.errors.AUTH_INVALID":"Autenticação inválida","ctp.errors.NOT_FOUND":"Nenhuma conta encontrada, insira um e-mail válido ou continue usando a entrada manual do cartão","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formato não suportado","ctp.errors.FRAUD":"A conta de utilizador foi bloqueada ou desativada","ctp.errors.CONSUMER_ID_MISSING":"A identidade do consumidor está em falta no pedido","ctp.errors.ACCT_INACCESSIBLE":"Esta conta não está atualmente disponível, por exemplo, está bloqueada","ctp.errors.CODE_INVALID":"Código de verificação incorreto","ctp.errors.CODE_EXPIRED":"Este código expirou","ctp.errors.RETRIES_EXCEEDED":"Ultrapassou o limite para o número de tentativas para a geração OTP.","ctp.errors.OTP_SEND_FAILED":"Não foi possível enviar a OTP ao destinatário","ctp.errors.REQUEST_TIMEOUT":"Algo correu mal, tente de novo ou use a entrada do cartão manual","ctp.errors.UNKNOWN_ERROR":"Algo correu mal, tente de novo ou use a entrada do cartão manual","ctp.errors.SERVICE_ERROR":"Algo correu mal, tente de novo ou use a entrada do cartão manual","ctp.errors.SERVER_ERROR":"Algo correu mal, tente de novo ou use a entrada do cartão manual","ctp.errors.INVALID_PARAMETER":"Algo correu mal, tente de novo ou use a entrada do cartão manual","ctp.errors.AUTH_ERROR":"Algo correu mal, tente de novo ou use a entrada do cartão manual","paymentMethodsList.aria.label":"Escolha um método de pagamento","companyDetails.name.invalid":"Insira o nome da empresa","companyDetails.registrationNumber.invalid":"Insira o número de registo","consent.checkbox.invalid":"Tem de aceitar os termos e condições","form.instruction":"Todos os campos são obrigatórios, a menos que assinalados em contrário.","trustly.descriptor":"Pagamento bancário instantâneo","trustly.description1":"Pague diretamente a partir de qualquer uma das suas contas bancárias, com a segurança de um banco","trustly.description2":"Sem cartões, sem transferir aplicações, sem registo","ancv.input.label":"A sua identificação de ANCV","ancv.confirmPayment":"Use a sua aplicação ANCV para confirmar o pagamento.","ancv.form.instruction":"A app Cheque-Vacances é necessária para validar este pagamento.","ancv.beneficiaryId.invalid":"Insira um endereço de e-mail ou ID válido de ANCV","payme.openPayMeApp":"Conclua o seu pagamento na app PayMe autorizando o pagamento na app e aguardando a confirmação.","payme.redirectButtonLabel":"Abrir a app PayMe","payme.scanQrCode":"Conclua o seu pagamento por código QR","payme.timeToPay":"Este código QR é válido para %@","payme.instructions.steps":"Abra a app PayMe.%@Leia o código QR para autorizar o pagamento.%@Conclua o pagamento na app e aguarde a confirmação.","payme.instructions.footnote":"Não feche esta página antes de concluir o pagamento","payByBankAISDD.disclaimer.header":"Utilize Pagamento por banco para pagar instantaneamente com qualquer conta bancária.","payByBankAISDD.disclaimer.body":"Ao conectar a sua conta bancária, você autoriza débitos na sua conta de qualquer valor devido pela utilização dos nossos serviços e/ou compra dos nossos produtos, até que esta autorização seja revogada.","paymentMethodBrand.other":"outro"}},81993:function(e,a,r){r.r(a),r.d(a,{default:function(){return t}});var t={payButton:"Plačilo","payButton.redirecting":"Preusmerjanje...","payButton.with":"Plačajte %{value} z %{maskedData}","payButton.saveDetails":"Shrani podrobnosti",close:"Zapri",storeDetails:"Shrani za moje naslednje plačilo",readMore:"Preberi več","creditCard.holderName":"Ime na kartici","creditCard.holderName.placeholder":"J. Novak","creditCard.holderName.invalid":"Vnesite ime, kot je prikazano na kartici","creditCard.numberField.title":"Številka kartice","creditCard.numberField.placeholder":"1234 5678 9012 3456","creditCard.expiryDateField.title":"Datum veljavnosti","creditCard.expiryDateField.placeholder":"MM/LL","creditCard.expiryDateField.month":"Mesec","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"LL","creditCard.expiryDateField.year":"Leto","creditCard.cvcField.title":"Varnostna koda","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Zapomni si za prihodnjič","creditCard.cvcField.placeholder.4digits":"4 števke","creditCard.cvcField.placeholder.3digits":"3 števke","creditCard.taxNumber.placeholder":"LLMMDD / 0123456789",installments:"Število obrokov",installmentOption:"%{times} × %{partialValue}",installmentOptionMonths:"Št. mesecev: %{times}","installments.oneTime":"Enkratno plačilo","installments.installments":"Obročno plačilo","installments.revolving":"Revolving plačilo","sepaDirectDebit.ibanField.invalid":"Neveljavna številka računa","sepaDirectDebit.nameField.placeholder":"J. Novak","sepa.ownerName":"Ime imetnika","sepa.ibanNumber":"Številka računa (IBAN)","error.title":"Napaka","error.subtitle.redirect":"Preusmeritev ni uspela","error.subtitle.payment":"Plačilo ni uspelo","error.subtitle.refused":"Plačilo je bilo zavrnjeno","error.message.unknown":"Prišlo je do neznane napake","errorPanel.title":"Obstoječe napake","idealIssuer.selectField.title":"Banka","idealIssuer.selectField.placeholder":"Izberite svojo banko","creditCard.success":"Plačilo je bilo uspešno",loading:"Nalaganje…",continue:"Nadaljuj",continueTo:"Nadaljujte na","wechatpay.timetopay":"Plačati morate %@","sr.wechatpay.timetopay":"Časa za plačilo imate %#min.%# %#s.%#","wechatpay.scanqrcode":"Optično preberite kodo QR",personalDetails:"Osebni podatki",companyDetails:"Podrobnosti o podjetju","companyDetails.name":"Ime podjetja","companyDetails.registrationNumber":"Matična številka podjetja",socialSecurityNumber:"Številka socialnega zavarovanja",firstName:"Ime","firstName.invalid":"Vnesite svoje ime",infix:"Naziv",lastName:"Priimek","lastName.invalid":"Vnesite svoj priimek",mobileNumber:"Številka mobilnega telefona","mobileNumber.invalid":"Neveljavna številka mobilnega telefona",city:"Mesto",postalCode:"Poštna številka","postalCode.optional":"Poštna številka (neobvezno)",countryCode:"Koda države",telephoneNumber:"Telefonska številka",dateOfBirth:"Datum rojstva",shopperEmail:"Elektronski naslov",gender:"Spol","gender.notselected":"Izberite svoj spol",male:"Moški",female:"Ženski",billingAddress:"Naslov za račun",street:"Ulica",stateOrProvince:"Država ali provinca",country:"Država/regija",houseNumberOrName:"Hišna številka",separateDeliveryAddress:"Navedite ločen naslov za dostavo",deliveryAddress:"Naslov za dostavo","deliveryAddress.firstName":"Ime prejemnika","deliveryAddress.lastName":"Priimek prejemnika",zipCode:"Poštna številka",apartmentSuite:"Št. apartmaja/stanovanja",provinceOrTerritory:"Območje ali ozemlje",cityTown:"Mesto",address:"Naslov","address.placeholder":"Poiščite svoj naslov","address.errors.incomplete":"Za nadaljevanje vnesite naslov","address.enterManually":"Naslov vnesite ročno",state:"Država","field.title.optional":"(izbirno)","creditCard.cvcField.title.optional":"Varnostna koda (neobvezno)","issuerList.wallet.placeholder":"Izberite svojo denarnico",privacyPolicy:"Pravilnik o zasebnosti","afterPay.agreement":"Strinjam se s %@ ponudnika Riverty","riverty.termsAndConditions":"Strinjam se s splošnimi %#pogoji in določili%# za način plačila Riverty. Pravilnik o zasebnosti storitve Riverty je na voljo %#tukaj%#.",paymentConditions:"plačilnimi pogoji",openApp:"Odprite aplikacijo","voucher.readInstructions":"Preberite navodila","voucher.introduction":"Zahvaljujemo se vam za nakup. Za dokončanje plačila uporabite naslednji kupon.","voucher.expirationDate":"Datum poteka veljavnosti","voucher.alternativeReference":"Druga referenčna številka","dragonpay.voucher.non.bank.selectField.placeholder":"Izberite svojega ponudnika","dragonpay.voucher.bank.selectField.placeholder":"Izberite svojo banko","voucher.paymentReferenceLabel":"Referenčna številka plačila","voucher.surcharge":"Vklj. %@ doplačila","voucher.introduction.doku":"Zahvaljujemo se vam za nakup. Za dokončanje plačila uporabite naslednji kupon.","voucher.shopperName":"Ime kupca","voucher.merchantName":"Trgovec","voucher.introduction.econtext":"Zahvaljujemo se vam za nakup. Za dokončanje plačila uporabite naslednji kupon.","voucher.telephoneNumber":"Telefonska številka","voucher.shopperReference":"Referenčna številka kupca","voucher.collectionInstitutionNumber":"Številka ustanove za zbiranje","voucher.econtext.telephoneNumber.invalid":"Telefonska številka mora vsebovati 10 ali 11 števk","boletobancario.btnLabel":"Ustvari Boleto","boleto.sendCopyToEmail":"Pošlji kopijo na moj elektronski naslov","button.copy":"Kopiraj","button.download":"Prenesi","boleto.socialSecurityNumber.invalid":"Vnesite veljavno številko CPF/CNPJ","creditCard.storedCard.description.ariaLabel":"Shranjena kartica se konča na %@","voucher.entity":"Entiteta",donateButton:"Donirajte",notNowButton:"Ne zdaj",thanksForYourSupport:"Zahvaljujemo se vam za podporo!","resultMessages.preauthorized":"Podrobnosti so shranjene",preauthorizeWith:"Predhodna odobritev s/z:",confirmPreauthorization:"Potrdi predhodno odobritev",confirmPurchase:"Potrditev nakupa",applyGiftcard:"Unovči",giftcardBalance:"Stanje na darilni kartici",deductedBalance:"Odbiti znesek","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Prvi dve števki gesla za kartico","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Neveljavno geslo","creditCard.taxNumber":"Datum rojstva imetnika kartice ali matična številka podjetja","creditCard.taxNumber.label":"Datum rojstva imetnika kartice (LLMMDD) ali registracijska številka podjetja (10 števk)","creditCard.taxNumber.labelAlt":"Matična številka podjetja (10 števk)","creditCard.taxNumber.invalid":"Neveljaven datum rojstva imetnika kartice ali matična številka podjetja","storedPaymentMethod.disable.button":"Odstrani","storedPaymentMethod.disable.confirmation":"Odstrani shranjen način plačila","storedPaymentMethod.disable.confirmButton":"Da, odstrani","storedPaymentMethod.disable.cancelButton":"Prekliči","ach.bankAccount":"Bančni račun","ach.accountHolderNameField.title":"Ime imetnika računa","ach.accountHolderNameField.placeholder":"J. Novak","ach.accountHolderNameField.invalid":"Neveljavno ime imetnika računa","ach.accountNumberField.title":"Številka računa","ach.accountNumberField.invalid":"Neveljavna številka računa","ach.accountLocationField.title":"Koda banke ABA","ach.accountLocationField.invalid":"Neveljavna koda banke ABA","ach.savedBankAccount":"Shranjen bančni račun","ach.savings":"Varčevalni račun","ach.checking":"Tekoči račun","select.state":"Izberite državo","select.stateOrProvince":"Izberite državo ali provinco","select.provinceOrTerritory":"Izberite območje ali ozemlje","select.country":"Izberite državo/regijo","select.noOptionsFound":"Ni najdenih možnosti","select.filter.placeholder":"Iskanje...","telephoneNumber.invalid":"Neveljavna telefonska številka",qrCodeOrApp:"ali","paypal.processingPayment":"Obdelava plačila...",generateQRCode:"Ustvari kodo QR","await.waitForConfirmation":"Čakanje na potrditev","mbway.confirmPayment":"Potrdite svoje plačilo v aplikaciji MB WAY","shopperEmail.invalid":"Neveljaven elektronski naslov","dateOfBirth.format":"DD/MM/LLLL","dateOfBirth.invalid":"Vnesite veljaven datum rojstva, ki kaže, da ste stari vsaj 18 let.","blik.confirmPayment":"Za potrditev plačila odprite svojo bančno aplikacijo.","blik.invalid":"Vnesite 6 številk","blik.code":"6-mestna koda","blik.help":"Pridobite kodo iz bančne aplikacije.","swish.pendingMessage":"Ko optično preberete, lahko čakanje traja do 10 minut. Poskus ponovnega plačila v tem času lahko povzroči več odtegljajev.","field.valid":"Polje je veljavno","field.invalid":"Polje ni veljavno","error.va.gen.01":"Nepopolno polje","error.va.gen.02":"Polje ni veljavno","error.va.sf-cc-num.01":"Vnesite veljavno številko kartice","error.va.sf-cc-num.02":"Vnesite številko kartice","error.va.sf-cc-num.03":"Vnesite podprto blagovno znamko kartice","error.va.sf-cc-num.04":"Vnesite celotno številko kartice","error.va.sf-cc-dat.01":"Vnesite veljavni datum poteka veljavnosti","error.va.sf-cc-dat.02":"Vnesite veljavni datum poteka veljavnosti","error.va.sf-cc-dat.03":"Kreditna kartica bo kmalu potekla","error.va.sf-cc-dat.04":"Vnesite datum poteka veljavnosti","error.va.sf-cc-dat.05":"Vnesite celoten datum poteka veljavnosti","error.va.sf-cc-mth.01":"Vnesite mesec poteka veljavnosti","error.va.sf-cc-yr.01":"Vnesite leto poteka veljavnosti","error.va.sf-cc-yr.02":"Vnesite celotno leto poteka veljavnosti","error.va.sf-cc-cvc.01":"Vnesite varnostno kodo","error.va.sf-cc-cvc.02":"Vnesite celotno varnostno kodo","error.va.sf-ach-num.01":"Polje s številko bančnega računa je prazno","error.va.sf-ach-num.02":"Številka bančnega računa je napačne dolžine","error.va.sf-ach-loc.01":"Polje z bančno usmerjevalno številko je prazno","error.va.sf-ach-loc.02":"Bančna usmerjevalna številka je napačne dolžine","error.va.sf-kcp-pwd.01":"Polje z geslom je prazno","error.va.sf-kcp-pwd.02":"Geslo je napačne dolžine","error.giftcard.no-balance":"Na tej darilni kartici ni sredstev","error.giftcard.card-error":"V naši evidenci nimamo darilne kartice s to številko","error.giftcard.currency-error":"Darilne kartice so veljavne samo za valuto, za katero so bile izdane","amazonpay.signout":"Odjava iz Amazona","amazonpay.changePaymentDetails":"Sprememba podrobnosti plačila","partialPayment.warning":"Izberite drugo vrsto plačila za plačilo ostanka","partialPayment.remainingBalance":"Preostalo stanje bo %{amount}","bankTransfer.beneficiary":"Upravičenec","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referenca","bankTransfer.introduction":"Nadaljujte ustvarjanje novega plačila z bančnim nakazilom. Za dokončanje tega plačila lahko uporabite podatke na naslednjem zaslonu.","bankTransfer.instructions":"Zahvaljujemo se vam za nakup. Za dokončanje plačila uporabite naslednji kupon.","bacs.accountHolderName":"Ime imetnika bančnega računa","bacs.accountHolderName.invalid":"Neveljavno ime imetnika računa","bacs.accountNumber":"Številka bančnega računa","bacs.accountNumber.invalid":"Neveljavna številka bančnega računa","bacs.bankLocationId":"Številka banke","bacs.bankLocationId.invalid":"Neveljavna številka banke","bacs.consent.amount":"Soglašam s tem, da bo zgornji znesek odtegnjen z mojega bančnega računa.","bacs.consent.account":"Potrjujem, da je račun ustvarjen v mojem imenu in sem edini podpisnik za odobritev neposredne bremenitve za ta račun.",edit:"Uredi","bacs.confirm":"Potrdi in plačaj","bacs.result.introduction":"Prenesite navodila za neposredno bremenitev (DDI/mandat)","download.pdf":"Prenos datoteke PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe za številko kartice","creditCard.encryptedCardNumber.aria.label":"Številka kartice","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe za datum poteka veljavnosti","creditCard.encryptedExpiryDate.aria.label":"Datum veljavnosti","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe za mesec poteka veljavnosti","creditCard.encryptedExpiryMonth.aria.label":"Mesec poteka","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe za leto poteka veljavnosti","creditCard.encryptedExpiryYear.aria.label":"Leto poteka","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe za varnostno kodo","creditCard.encryptedSecurityCode.aria.label":"Varnostna koda","creditCard.encryptedPassword.aria.iframeTitle":"Iframe za geslo","creditCard.encryptedPassword.aria.label":"Prvi dve števki gesla za kartico","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe za številko kartice","giftcard.encryptedCardNumber.aria.label":"Številka kartice","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe za kodo PIN","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Za posamezno transakcijo na tej darilni kartici je dovoljeno največ %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe za številko bančnega računa","ach.encryptedBankAccountNumber.aria.label":"Številka računa","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe za usmerjevalno številko banke","ach.encryptedBankLocationId.aria.label":"Koda banke ABA","twint.saved":"shranjeno",orPayWith:"ali plačajte s/z",invalidFormatExpects:"Neveljavna oblika zapisa. Pričakovana oblika zapisa: %{format}","upi.qrCodeWaitingMessage":"Za dokončanje plačila optično preberite kodo QR z želeno aplikacijo UPI","upi.vpaWaitingMessage":"Za potrditev plačila odprite svojo aplikacijo UPI","upi.modeSelection":"Kako bi želeli uporabiti UPI?","upi.completePayment":"Dokončaj plačilo","upi.mode.enterUpiId":"Vnesite UPI ID","upi.mode.qrCode":"Koda QR","upi.mode.payByAnyUpi":"Plačajte s poljubno aplikacijo UPI","upi.collect.dropdown.label":"Vnesite UPI ID","upi.collect.field.label":"Vnesite UPI ID / VPA","onlineBanking.termsAndConditions":"Z nadaljevanjem se strinjate s %#pogoji%#","onlineBankingPL.termsAndConditions":"Z nadaljevanjem se strinjate s %#predpisi%# in %#obveznostjo obveščanja%# družbe Przelewy24","ctp.loading.poweredByCtp":"Omogoča storitev Click to Pay","ctp.loading.intro":"Preverjamo, ali imate shranjene kartice v storitvi Click to Pay...","ctp.login.title":"Nadaljuj na storitev Click to Pay","ctp.login.subtitle":"Za nadaljevanje vnesite e-poštni naslov, ki je povezan s storitvijo Click to Pay.","ctp.login.inputLabel":"E-pošta","ctp.logout.notYou":"To niste vi?","ctp.logout.notYourCards":"To niso vaše kartice?","ctp.logout.notYourCard":"To ni vaša kartica?","ctp.logout.notYourProfile":"To ni vaš profil?","ctp.otp.fieldLabel":"Enkratna koda","ctp.otp.resendCode":"Znova pošlji kodo","ctp.otp.codeResent":"Koda je bila znova poslana","ctp.otp.title":"Dostop do vaših kartic Click to Pay","ctp.otp.subtitle":"Vnesite kodo %@, ki smo jo poslali na %@, da potrdite, da ste to res vi.","ctp.otp.saveCookiesCheckbox.label":"Naslednjič preskoči preverjanje","ctp.otp.saveCookiesCheckbox.information":"Izberite če želite, da se v vaši napravi in brskalniku v sodelujočih trgovinah vaši podatki shranijo za hitrejši zaključek zakupa. Ni priporočljivo za naprave v skupni rabi.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Izberite, če želite, da se v vaši napravi in brskalniku shranijo vaši podatki","ctp.emptyProfile.message":"V tem profilu Click to Pay ni registriranih nobenih kartic","ctp.separatorText":"ali uporabite","ctp.cards.title":"Dokončajte plačilo s storitvijo Click to Pay","ctp.cards.subtitle":"Izberite kartico, ki jo želite uporabiti.","ctp.cards.expiredCard":"Veljavnost potekla","ctp.manualCardEntry":"Ročni vnos kartice","ctp.aria.infoModalButton":"Kaj je storitev Click to Pay","ctp.infoPopup.title":"Storitev Click to Pay prinaša enostavnost brezstičnega plačevanja v spletno okolje","ctp.infoPopup.subtitle":"Hiter in varen način plačila, ki ga podpirajo Mastercard, Visa in druge plačilne kartice.","ctp.infoPopup.benefit1":"Storitev Click to Pay s šifriranjem poskrbi, da so vaši podatki varni in zaščiteni","ctp.infoPopup.benefit2":"Uporabljate jo lahko pri prodajalcih po vsem svetu","ctp.infoPopup.benefit3":"Enkratna nastavitev za brezskrbno plačevanje v prihodnje","ctp.errors.AUTH_INVALID":"Preverjanje pristnosti ni veljavno","ctp.errors.NOT_FOUND":"Računa ni bilo mogoče najti, vnesite veljaven e-poštni naslov ali nadaljujte z uporabo ročnega vnosa kartice","ctp.errors.ID_FORMAT_UNSUPPORTED":"Oblika zapisa ni podprta","ctp.errors.FRAUD":"Uporabniški račun je bil zaklenjen ali onemogočen","ctp.errors.CONSUMER_ID_MISSING":"V zahtevi manjka identiteta potrošnika","ctp.errors.ACCT_INACCESSIBLE":"Ta račun trenutno ni na voljo, npr. morda je zaklenjen","ctp.errors.CODE_INVALID":"Koda za preverjanje ni pravilna","ctp.errors.CODE_EXPIRED":"Ta koda je potekla","ctp.errors.RETRIES_EXCEEDED":"Omejitev števila ponovnih poskusov za generiranje gesla OTP je bila presežena","ctp.errors.OTP_SEND_FAILED":"Gesla OTP ni bilo mogoče poslati prejemniku","ctp.errors.REQUEST_TIMEOUT":"Nekaj je šlo narobe, poskusite znova ali uporabite ročni vnos kartice","ctp.errors.UNKNOWN_ERROR":"Nekaj je šlo narobe, poskusite znova ali uporabite ročni vnos kartice","ctp.errors.SERVICE_ERROR":"Nekaj je šlo narobe, poskusite znova ali uporabite ročni vnos kartice","ctp.errors.SERVER_ERROR":"Nekaj je šlo narobe, poskusite znova ali uporabite ročni vnos kartice","ctp.errors.INVALID_PARAMETER":"Nekaj je šlo narobe, poskusite znova ali uporabite ročni vnos kartice","ctp.errors.AUTH_ERROR":"Nekaj je šlo narobe, poskusite znova ali uporabite ročni vnos kartice","paymentMethodsList.aria.label":"Izberite način plačila","companyDetails.name.invalid":"Vnesite ime podjetja","companyDetails.registrationNumber.invalid":"Vnesite registracijsko številko","consent.checkbox.invalid":"Strinjati se morate s pogoji in določili","form.instruction":"Vsa polja so obvezna, razen če ni označeno drugače.","trustly.descriptor":"Takojšnje bančno nakazilo","trustly.description1":"Plačajte neposredno s svojega poljubnega bančnega računa, pri čemer je varnost zagotovljena na ravni banke.","trustly.description2":"Brez kartic, brez prenosa aplikacije, brez registracije","ancv.input.label":"Vaša identifikacija ANCV","ancv.confirmPayment":"Za potrditev plačila uporabite aplikacijo ANCV.","ancv.form.instruction":"Za potrditev tega plačila je potrebna aplikacija Cheque-Vacances.","ancv.beneficiaryId.invalid":"Vnesite veljaven e-poštni naslov ali ANCV ID","payme.openPayMeApp":"Plačilo dokončajte v aplikaciji PayMe tako, da ga odobrite v aplikaciji in počakate na potrditev.","payme.redirectButtonLabel":"Odprite aplikacijo PayMe","payme.scanQrCode":"Izvedite plačilo s kodo QR","payme.timeToPay":"Ta koda QR velja za %@","payme.instructions.steps":"Odprite aplikacijo PayMe.%@Skenirajte kodo QR, da odobrite plačilo.%@Zaključite plačilo v aplikaciji in počakajte na potrditev.","payme.instructions.footnote":"Ne zapirajte te strani, preden je plačilo zaključeno.","payByBankAISDD.disclaimer.header":"Uporabite Pay by Bank za takojšnje plačilo s katerega koli bančnega računa.","payByBankAISDD.disclaimer.body":"S povezavo svojega bančnega računa dovoljujete bremenitev računa za vse zneske, ki jih dolgujete za uporabo naših storitev in/ali nakup naših izdelkov, dokler tega dovoljenja ne prekličete.","paymentMethodBrand.other":"Drugo"}}}]);