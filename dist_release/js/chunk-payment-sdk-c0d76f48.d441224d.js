"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[3243],{91874:function(e,n,t){t.d(n,{B2:function(){return i}});(function(e){e.Production="production",e.Sandbox="sandbox"})({}),function(e){e.Approved="Approved",e.Declined="Declined"}({}),function(e){e.NotEnoughFunds="not_enough_funds",e.InvalidPaymentSessionData="invalid_payment_session_data",e.InvalidCustomerData="invalid_customer_data",e.MerchantMisconfiguration="merchant_misconfiguration",e.Try<PERSON>gain="try_again"}({}),function(e){e.Request="Request",e.Integration="Integration",e.Submit="Submit"}({}),function(e){e.Title="title",e.Subheading="subheading",e.Body="body",e.Footnote="footnote",e.<PERSON><PERSON>="button",e.Input="input",e.Label="label"}({}),function(e){e.Bancontact="bancontact",e.Card="card",e.Eps="eps",e.Giropay="giropay",e.Applepay="applepay",e.Googlepay="googlepay",e.Ideal="ideal",e.Knet="knet",e.Multibanco="multibanco",e.P24="p24",e.Sofort="sofort",e.Payments="payments"}({});const o="https://checkout-web-components.checkout.com/index.js",i=async e=>{const n=(e=>{const n=document.querySelector(`script[src="${e}"]`);if(n)return n;const t=document.createElement("script");return t.src=e,t.async=!0,document.head.appendChild(t),t})(o);try{const t=await(async e=>new Promise((n,t)=>{e.addEventListener("load",()=>{window.CheckoutWebComponents?n(window.CheckoutWebComponents):t(new Error("CheckoutWebComponents not found on window object."))}),e.addEventListener("error",()=>{t(new Error("CheckoutWebComponents failed to load."))})}))(n);return t(e)}catch(e){throw console.error("CheckoutWebComponents: "+e),e}}},95939:function(e,n,t){t.d(n,{n:function(){return un},T:function(){return ln}});var o,i,r,a,s=t(33237),c=Object.defineProperty,l=Object.defineProperties,u=Object.getOwnPropertyDescriptors,p=Object.getOwnPropertySymbols,d=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable,f=(e,n,t)=>n in e?c(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,h=(e,n)=>{for(var t in n||(n={}))d.call(n,t)&&f(e,t,n[t]);if(p)for(var t of p(n))m.call(n,t)&&f(e,t,n[t]);return e},g=(e,n)=>l(e,u(n)),y=(e,n,t)=>new Promise((o,i)=>{var r=e=>{try{s(t.next(e))}catch(n){i(n)}},a=e=>{try{s(t.throw(e))}catch(n){i(n)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(r,a);s((t=t.apply(e,n)).next())}),w=(e=>(e["android"]="android",e["ios"]="ios",e["windows"]="windows",e["macos"]="macos",e["linux"]="linux",e["other"]="other",e))(w||{}),v=(e=>(e["production"]="prod",e["demo"]="demo",e["staging"]="staging",e))(v||{}),b="airTracker_unknown",x={appName:b,appVersion:b,env:"staging",isWebappContainer:!1,delay:2e3,errorRepeatTime:3,enableErrorMonitoring:!1,enableWebVitals:!1,assetSpeedMonitoringWhiteList:[],enableDetectPageChange:!1,assetSpeedMonitoringWhiteListByMFE:{}},D={appName:b,env:"staging",sessionId:b,deviceId:b,platform:"other"},E=e=>!!e&&(!/^\s*$/.test(e)&&(e=e.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@"),e=e.replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]"),e=e.replace(/(?:^|:|,)(?:\s*\[)+/g,""),/^[\],:{}\s]*$/.test(e))),k=()=>{const e=new WeakSet;return(n,t)=>{if(t instanceof Error)return`Error.message: ${t.message} \n  Error.stack: ${t.stack}`;if("object"===typeof t&&null!==t){if(e.has(t))return`[Circular ${n||"root"}]`;e.add(t)}return"function"===typeof t?"function":"symbol"===typeof t?"symbol":"undefined"===typeof t?null:t}},_=e=>{if("string"===typeof e)return e;try{return e instanceof Error?(JSON.stringify(e,k())||"undefined").replace(/"/gim,""):JSON.stringify(e,k())}catch(n){return'{"error":"error happen when airTracker stringify"}'}},C=({method:e="post",url:n,data:t,success:o,fail:i})=>{if(!E(t))return;const r=new XMLHttpRequest;r.addEventListener("readystatechange",()=>{4===r.readyState&&(r.status>=400||0===r.status?null==i||i(r.response):null==o||o(r.response))}),r.open(e,n),r.setRequestHeader("Content-Type","application/json"),r.send(t)},S={onInit:"onInit",onConfigInit:"onConfigInit",onConfigUpdated:"onConfigUpdated",onCommonDataInit:"onCommonDataInit",onCommonUpdated:"onCommonUpdated",onPageChange:"onPageChange",onDestroy:"onDestroy,"},L="AIR_ANALYTICS_DEVICE_ID",P=e=>"prod"==e?"https://api.airwallex.com/papluginlogs/cors-logs":"demo"==e?"https://api-demo.airwallex.com/papluginlogs/cors-logs":"https://api-staging.airwallex.com/papluginlogs/cors-logs",M=e=>"prod"==e?"https://api.airwallex.com/papluginlogs/logs":"demo"==e?"https://api-demo.airwallex.com/papluginlogs/logs":"https://api-staging.airwallex.com/papluginlogs/logs",N=()=>{const e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{const n=16*Math.random()|0,t="x"===e?n:3&n|8;return t.toString(16)});return e},I=()=>{var e,n;return(null==(n=null==(e=null==location?void 0:location.pathname)?void 0:e.split("/"))?void 0:n[2])||"unknown"},T=()=>{try{let e=window.localStorage.getItem(L);return e||(e=N(),window.localStorage.setItem(L,e)),e}catch(e){}},j=()=>{const e={android:/\bAndroid\s*([^;]+)/,ios:/\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/,windows:/\b(Windows NT)/,macos:/\b(Mac OS)/,linux:/\b(Linux)/i},n=n=>e[n].test(navigator.userAgent),t=Object.keys(e).find(n);return t?w[t]:"other"},A=()=>/^[a-zA-Z0-9.-]*.airwallex.com$/g.test(window.location.host),O=()=>{var e,n;let t="";const o=navigator.userAgent.match(/NetType\/(\w+)/);return o?[,t]=o:(null==navigator?void 0:navigator.connection)&&(t=(null==(e=null==navigator?void 0:navigator.connection)?void 0:e.effectiveType)||(null==(n=null==navigator?void 0:navigator.connection)?void 0:n.type)),t||(t="unknown"),K(t)},K=e=>(e=String(e).toLowerCase(),e.indexOf("4g")>=0?"net4g":e.indexOf("wifi")>=0?"wifi":e.indexOf("5g")>=0?"net5g":e.indexOf("3g")>=0?"net3g":e.indexOf("2g")>=0?"net2g":"unknown"),F=function(){return"undefined"!==typeof window.performance&&"function"===typeof window.performance.clearResourceTimings&&"function"===typeof window.performance.getEntriesByType&&"function"===typeof window.performance.now},W=function(){return"function"===typeof window.PerformanceObserver},B=e=>{if("string"===typeof e){const n=e.split("?")[0]||"";return n.slice(0,200)}return e},R=e=>"string"===typeof e?e.split("?")[1]||"":e,$=e=>{const n="string"===typeof e&&e.startsWith("//");return n?"undefined"!==typeof location&&"https:"===location.protocol:/^https/.test(e)},U=(e,n)=>"number"===typeof e||"string"===typeof e?e:n?"":-1,V=(e,n)=>{var t;let o=!1;if(!n.config.isWebappContainer)return o=q(e,n.config.assetSpeedMonitoringWhiteList),o;const i=I();return o||q(e,null==(t=n.config.assetSpeedMonitoringWhiteListByMFE)?void 0:t[i])},q=(e,n)=>{if(!(null==n?void 0:n.length))return!1;for(let t=0;t<n.length;t++){const o=n[t];if(o instanceof RegExp&&o.test(e))return!0;if("string"==typeof o&&e.includes(o))return!0}return!1},H=e=>n=>{var t;const o=e.config.isWebappContainer?h(h({},e.commonData||{}),(null==(t=e.MFECommonDataMap)?void 0:t[I()])||{}):e.commonData,i={commonData:o,data:Array.isArray(n)?n:[n]},r=_(i),a={method:"post",url:A()?P(e.commonData.env):M(e.commonData.env),data:r};C(a)},z=(e,n)=>{let t;const o=[],{config:i}=e;return(r,a)=>{if(o.push(r),e.lifeCycle.on(S.onCommonUpdated,()=>{o.length>0&&(null==a||a(o.splice(0,o.length)),e.lifeCycle.remove(S.onCommonUpdated),t&&clearTimeout(t))}),n&&o.length>=n)return null==a||a(o.splice(0,o.length)),void(t&&clearTimeout(t));t&&clearTimeout(t),t=setTimeout(()=>{t=null,o.length>0&&(null==a||a(o.splice(0,o.length)),e.lifeCycle.remove(S.onCommonUpdated))},i.delay)}},J=e=>{const n={};return(t,o)=>{const i="number"===typeof e.config.errorRepeatTime?e.config.errorRepeatTime:5;if(0===i)return null==o?void 0:o(t);null==o||o(t.filter(e=>"autoDetectError"!=e.severity||(n[e.error]=n[e.error]||0,n[e.error]+=1,!(n[e.error]>i))))}},G=()=>{},Q=e=>{if(!e||!e.reduce||!e.length)throw new TypeError("createPipeline need at least one function param");return 1===e.length?(n,t)=>{e[0](n,t||G)}:e.reduce((e,n)=>(t,o=G)=>e(t,e=>null==n?void 0:n(e,o)))},X=class{constructor(){this.emit=(e,n)=>{if(!this)return;let t,o=this.eventsList[e];if(null==o?void 0:o.length){o=o.slice();for(let r=0;r<o.length;r++){t=o[r];try{const o=t.callback.apply(this,[n]);if(1===t.type&&this.remove(e,t.callback),!1===o)break}catch(i){throw i}}}return this},this.eventsList={}}indexOf(e,n){for(let t=0;t<e.length;t++)if(e[t].callback===n)return t;return-1}on(e,n,t=0){if(!this)return;let o=this.eventsList[e];if(o||(this.eventsList[e]=[],o=this.eventsList[e]),-1===this.indexOf(o,n)){const i={name:e,type:t||0,callback:n};return o.push(i),this}return this}one(e,n){this.on(e,n,1)}remove(e,n){if(!this)return;const t=this.eventsList[e];if(!t)return null;if(!n){try{delete this.eventsList[e]}catch(o){}return null}if(t.length){const e=this.indexOf(t,n);t.splice(e,1)}return this}clear(){this.eventsList={}}},Y=class{constructor(e){this.name="",this.isInit=!1,this.name=e.name,this.option=e}patch(e){var n,t;this.isInit||(this.isInit=!0,null==(t=null==(n=this.option)?void 0:n.setUp)||t.call(this.option,e))}uninstall(){var e,n;null==(n=null==(e=this.option)?void 0:e.destroy)||n.apply(this),this.isInit=!1}},Z=e=>{const n=`${_(e.message)||""} @ (${_(e.filename)||""}:${e.lineno||0}:${e.colno||0})\n${_(e.error||"")}`;o.normalLogPipeLine({severity:"autoDetectError",eventName:"windowOnError",extraInfo:{error:n}})},ee=e=>{const n=e&&_(e.reason);o.normalLogPipeLine({severity:"autoDetectError",eventName:"promiseError",extraInfo:{error:n}})},ne=e=>{const n=(null==e?void 0:e.target)||(null==e?void 0:e.srcElement);if(!n)return;const t=(null==n?void 0:n.src)||(null==n?void 0:n.href),{tagName:i}=n;let r="unknown";if("string"===typeof t&&i){if(window.location.href.indexOf(t)>-1)return;if(/\.js$/.test(t))r="script";else if(/\.css$/.test(t))r="css";else switch(i.toLowerCase()){case"script":r="script";break;case"link":r="css";break;case"img":r="image";break;case"audio":case"video":r="media";break;default:return}o.normalLogPipeLine({severity:"autoDetectError",eventName:"staticFileLoadError",extraInfo:{staticFileType:r,error:`${i} load fail: ${t}`}})}},te=new Y({name:"errorDetectionPlugin",setUp:e=>{o=e,window.addEventListener("error",Z),window.addEventListener("unhandledrejection",ee),window.document.addEventListener("error",ne,!0)},destroy:()=>{window.removeEventListener("unhandledrejection",ee),window.document.removeEventListener("error",ne,!0),window.removeEventListener("error",Z)}}),oe=new Y({name:"webVitalsPlugin",setUp:e=>y(void 0,null,function*(){if(!F()||!W())return;const n=n=>{const{name:t,navigationType:o,rating:i,value:r}=n;e.normalLogPipeLine({severity:"performance",eventName:t,extraInfo:{log:{navigationType:o,rating:i,value:r}}})};(0,s.IN)(n),(0,s.lt)(n),(0,s.fK)(n)})}),ie=["img","css","script","link","audio","video","iframe"],re="resource",ae=e=>{const n=e.name;return{url:B(n),method:"get",duration:Number(e.duration.toFixed(2)),type:"static",isHttps:$(n),urlQuery:R(n),domainLookup:U(e.domainLookupEnd-e.domainLookupStart),connectTime:U(e.connectEnd-e.connectStart)}},se=(e,n)=>{for(let t=0,o=e.length;t<o;t++){const o=e[t];-1!==ie.indexOf(o.initiatorType)&&V(o.name,n)&&n.normalLogPipeLine({severity:"performance",eventName:"assets_speed",extraInfo:{log:ae(o)}})}},ce=new Y({name:"assetsSpeedPlugin",setUp:e=>{if(!F())return;let n=0;window.performance.onresourcetimingbufferfull=()=>{n=0,window.performance.clearResourceTimings()},"function"===typeof window.PerformanceObserver?(se(window.performance.getEntriesByType(re),e),r=new window.PerformanceObserver(n=>{se(n.getEntries(),e)}),r.observe({entryTypes:[re]})):i=setInterval(()=>{const t=window.performance.getEntriesByType(re),o=t.slice(n);n=t.length,se(o,e)},3e3)},destroy:()=>{null==r||r.disconnect(),i&&clearInterval(i)}}),le=new Y({name:"onPageChangePlugin",setUp:e=>y(void 0,null,function*(){let n=null==location?void 0:location.href;const t=document.querySelector("body");a=new MutationObserver(()=>{if(location.href!==n){const t=n;n=location.href,e.normalLogPipeLine({severity:"autoDetectEvent",eventName:"onPageChange",extraInfo:{prevHref:t,href:(null==location?void 0:location.href)||"",hostname:null==location?void 0:location.hostname,pathName:null==location?void 0:location.pathname,protocol:null==location?void 0:location.protocol,search:null==location?void 0:location.search}})}});const o={subtree:!0,childList:!0};a.observe(t||document,o)}),destroy:()=>{null==a||a.disconnect()}}),ue=class{constructor({config:e,plugins:n=[]}){this.config=x,this.lifeCycle=new X,this.plugins=[],this._commonData=D,this._MFECommonDataMap={},this.timeMap={},this.normalPipelineObj=Q([z(this,8),J(this),H(this)]),this.normalLogPipeLine=({severity:e,eventName:n,extraInfo:t})=>{const o=h({severity:e,eventName:n,currentHref:(null==location?void 0:location.href)||"unknown href"},t);return this.config.isWebappContainer&&(o.MFEName=I()),this.normalPipelineObj(o)},this.plugins=[...n],this.setConfig(e),this.initCommonData(e),this.lifeCycle.emit(S.onInit),e.enableErrorMonitoring&&this.plugins.push(te),e.enableWebVitals&&this.plugins.push(oe),((null==e?void 0:e.assetSpeedMonitoringWhiteList)&&e.assetSpeedMonitoringWhiteList.length>0||e.isWebappContainer)&&this.plugins.push(ce),(null==e?void 0:e.enableDetectPageChange)&&this.plugins.push(le),this.installPlugins()}installPlugins(){this.plugins.forEach(e=>{e.patch(this)})}get commonData(){return this._commonData}get MFECommonDataMap(){return this._MFECommonDataMap}initCommonData(e){this._commonData.sessionId=N(),this._commonData.deviceId=T()||b,this._commonData.platform=j(),this._commonData.networkType=O()||"unknown",this._commonData.env=e.env||this._commonData.env,this._commonData.accountId=e.accountId||b,this._commonData.appVersion=e.appVersion,this._commonData.appName=e.appName,this.lifeCycle.emit(S.onCommonDataInit,this.commonData)}updateCommonDataBasedOnConfig(e){this._commonData.env=e.env||this._commonData.env,this._commonData.appName=e.appName||this._commonData.appName,this._commonData.env=e.env||this._commonData.env,this._commonData.accountId=e.accountId||this._commonData.accountId,this._commonData.appVersion=e.appVersion||this._commonData.appVersion,this.lifeCycle.emit(S.onCommonUpdated,this._commonData)}updateCommonData(e){this._commonData=h(h({},this._commonData),e)}addToMFECommonData({MFEName:e,MFECommonData:n}){var t;this.config.isWebappContainer&&e&&n&&(this._MFECommonDataMap=g(h({},this._MFECommonDataMap||{}),{[e]:h(h({},(null==(t=this._MFECommonDataMap)?void 0:t[e])||{}),n)}))}setConfig(e){if(this.config.isWebappContainer)return;const n=(e,n)=>{this.config[e]=n};Object.entries(e).forEach(e=>{const[t,o]=e;void 0!==typeof o&&n(t,o)}),this.lifeCycle.emit(S.onConfigInit,this.config),this.updateCommonDataBasedOnConfig(e)}info(e,n){this.normalLogPipeLine({severity:"info",eventName:e,extraInfo:n})}warn(e,n){this.normalLogPipeLine({severity:"warn",eventName:e,extraInfo:n})}error(e,n){this.normalLogPipeLine({severity:"error",eventName:e,extraInfo:n})}businessLog(e,n){this.normalLogPipeLine({severity:"info",eventName:e,extraInfo:g(h({},n),{isBusinessLog:!0})})}reportDuration(e,n){"string"===typeof e?"number"===typeof n?n<0||n>6e4?console.warn("reportDuration: duration (second param) must between 0 and 60000"):this.normalLogPipeLine({severity:"speed",eventName:e,extraInfo:{duration:n}}):console.warn("reportDuration: duration (second param) must be number"):console.warn("reportDuration: eventName (first param) must be a string")}getTimerKey(e){return this.config.isWebappContainer?`${I()}_${e}`:e}timeStart(e){"string"===typeof e?(this.timeMap[this.getTimerKey(e)]&&console.warn(`Timer ${e} already exists`),this.timeMap[this.getTimerKey(e)]=Date.now()):console.warn("time: first param must be a string")}timeEnd(e,n){if("string"===typeof e)if(this.timeMap[this.getTimerKey(e)]){const t=Date.now()-this.timeMap[this.getTimerKey(e)]+(n||0);this.normalLogPipeLine({severity:"speed",eventName:e,extraInfo:{duration:t}}),delete this.timeMap[this.getTimerKey(e)]}else console.warn(`Timer key :${e} does not exist`);else console.warn("timeEnd: first param must be a string")}addToAssetSpeedWhiteListByMFE({MFEName:e,whiteList:n}){var t,o,i;this.config.isWebappContainer&&e&&(null==n?void 0:n.length)&&(this.config.assetSpeedMonitoringWhiteListByMFE||(this.config.assetSpeedMonitoringWhiteListByMFE={}),(null==(t=this.config.assetSpeedMonitoringWhiteListByMFE)?void 0:t[e])?null==(i=null==(o=this.config.assetSpeedMonitoringWhiteListByMFE)?void 0:o[e])||i.push(...n):this.config.assetSpeedMonitoringWhiteListByMFE=g(h({},this.config.assetSpeedMonitoringWhiteListByMFE),{[e]:n}))}destroy(){this.config.isWebappContainer||this.plugins.forEach(e=>{e.uninstall()})}},pe=ue,de=function(){return de=Object.assign||function(e){for(var n,t=1,o=arguments.length;t<o;t++)for(var i in n=arguments[t],n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i]);return e},de.apply(this,arguments)};function me(e,n,t,o){function i(e){return e instanceof t?e:new t(function(n){n(e)})}return new(t||(t=Promise))(function(t,r){function a(e){try{c(o.next(e))}catch(n){r(n)}}function s(e){try{c(o["throw"](e))}catch(n){r(n)}}function c(e){e.done?t(e.value):i(e.value).then(a,s)}c((o=o.apply(e,n||[])).next())})}function fe(e,n){var t,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(n){return c([e,n])}}function c(s){if(t)throw new TypeError("Generator is already executing.");while(r&&(r=0,s[0]&&(a=0)),a)try{if(t=1,o&&(i=2&s[0]?o["return"]:s[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,s[1])).done)return i;switch(o=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(i=a.trys,!(i=i.length>0&&i[i.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){a.label=s[1];break}if(6===s[0]&&a.label<i[1]){a.label=i[1],i=s;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(s);break}i[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(c){s=[6,c],o=0}finally{t=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}}function he(e,n,t){if(t||2===arguments.length)for(var o,i=0,r=n.length;i<r;i++)!o&&i in n||(o||(o=Array.prototype.slice.call(n,0,i)),o[i]=n[i]);return e.concat(o||Array.prototype.slice.call(n))}"function"===typeof SuppressedError&&SuppressedError;var ge,ye,we,ve="@airwallex/components-sdk",be="1.22.3",xe="./lib/index.js",De="./lib/index.mjs",Ee="./lib/index.d.ts",ke=["lib/**"],_e="MIT",Ce={"load-types":"node ./load-types.js && yarn prettier -w src/types/*d.ts",prebuild:"yarn clean",build:"yarn rollup --config",postbuild:"yarn bundle-types","bundle-types":"dts-bundle-generator -o ./lib/index.d.ts ./src/index.ts",clean:"rm -rf dist lib",lint:'eslint "src/**/*.ts*"',test:"jest",sonar:"sonar-scanner",release:"semantic-release","lint-stage":"lint-staged",prepare:"husky"},Se={semi:!0,singleQuote:!0,printWidth:80,trailingComma:"all"},Le={access:"public"},Pe={"@babel/preset-typescript":"^7.18.6","@rollup/plugin-commonjs":"^25.0.7","@rollup/plugin-json":"^6.1.0","@rollup/plugin-node-resolve":"^15.2.3","@rollup/plugin-terser":"^0.4.4","@rollup/plugin-typescript":"^11.1.6","@semantic-release/changelog":"^5.0.1","@semantic-release/commit-analyzer":"^8.0.1","@semantic-release/git":"^9.0.0","@semantic-release/gitlab":"^6.1.0","@semantic-release/release-notes-generator":"^9.0.0","@swc/core":"^1.3.46","@types/jest":"^29.5.0","@typescript-eslint/eslint-plugin":"^5.57.0","@typescript-eslint/parser":"^5.57.0","dts-bundle-generator":"^9.0.0",eslint:"^7.23.0","eslint-config-prettier":"^8.3.0","eslint-plugin-prettier":"^5.1.3",husky:"^9.0.11",jest:"^29.5.0","jest-environment-jsdom":"^29.5.0","lint-staged":"^15.2.7",prettier:"^3.3.2",rollup:"^4.9.6","semantic-release":"^17.3.8","sonarqube-scanner":"^3.0.1","ts-jest":"^29.0.5","ts-loader":"^9.5.1",tslib:"^2.6.2",typescript:"^4.5.2"},Me={"@airwallex/airtracker":"1.2.0"},Ne={name:ve,version:be,main:xe,module:De,types:Ee,files:ke,license:_e,scripts:Ce,prettier:Se,"lint-staged":{"src/**/*.{ts,tsx}":["yarn lint"],"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}":"prettier --write"},publishConfig:Le,devDependencies:Pe,dependencies:Me},Ie={dev:v.staging,staging:v.staging,demo:v.demo,prod:v.production},Te=function(e,n){return void 0===e&&(e=v.production),window._AirwallexSDKs.airTracker||(window._AirwallexSDKs.airTracker=new pe({config:{appName:"components-sdk",appVersion:Ne.version,env:Ie[e]||v.production}}),window._AirwallexSDKs.airTracker.updateCommonData({clientId:n})),window._AirwallexSDKs.airTracker},je=function(e){if("object"!==typeof e)return e;try{var n=JSON.parse(JSON.stringify(e));return Object.keys(n).forEach(function(e){"string"===typeof e&&["auth","code"].some(function(n){return e.includes(n)})&&(n[e]="***")}),n}catch(ge){return e}},Ae=function(e,n){var t,o;null===(o=null===(t=window._AirwallexSDKs)||void 0===t?void 0:t.airTracker)||void 0===o||o.error(e,de(de({},n),{context:"components-sdk"}))},Oe=function(e,n){var t;null===(t=window._AirwallexSDKs.airTracker)||void 0===t||t.warn(e,de(de({},n),{context:"components-sdk"}))},Ke=function(e,n){var t;null===(t=window._AirwallexSDKs.airTracker)||void 0===t||t.info(e,de(de({},n),{context:"components-sdk"}))};(function(e){e["dev"]="dev",e["staging"]="staging",e["demo"]="demo",e["prod"]="prod"})(we||(we={}));var Fe,We=(ge={},ge[we.dev]="https://checkout-staging.airwallex.com",ge[we.staging]="https://checkout-staging.airwallex.com",ge[we.demo]="https://checkout-demo.airwallex.com",ge[we.prod]="https://checkout.airwallex.com",ge),Be=(ye={},ye[we.dev]="https://static-staging.airwallex.com",ye[we.staging]="https://static-staging.airwallex.com",ye[we.demo]="https://static-demo.airwallex.com",ye[we.prod]="https://static.airwallex.com",ye),Re="sdkController",$e={kyc:"/widgets/kyc/sdk/v1/index.js",rfi:"/widgets/kycRfi/sdk/v1/index.js",paymentsKyb:"/widgets/paymentsKyb/sdk/v1/index.js",sdkController:"/widgets/sdk-controller/sdk/v1/index.js",payouts:"/widgets/payouts/sdk/v1/index.js",payments:"/assets/elements.bundle.min.js",sca:"/hosted-sca/sdk/v1/index.js",taxForm:"/widgets/taxForm/sdk/v1/index.js"},Ue={kyc:"kyc",paymentsKyb:"paymentsKyb",kycRfi:"rfi",transactionRfi:"rfi",paymentEnablementRfi:"rfi",lendingRfi:"rfi",mrmRfi:"rfi",payoutForm:"payouts",beneficiaryForm:"payouts",hpp:"payments",cvc:"payments",card:"payments",expiry:"payments",dropIn:"payments",cardNumber:"payments",applePayButton:"payments",googlePayButton:"payments",scaSetup:"sca",scaVerify:"sca",scaManagement:"sca",taxForm:"taxForm"},Ve={payments:["hpp","cvc","card","expiry","dropIn","cardNumber","applePayButton","googlePayButton"],payouts:["payoutForm","beneficiaryForm"],onboarding:["kyc","paymentsKyb"],risk:["scaSetup","scaVerify","scaManagement","transactionRfi","kycRfi","paymentEnablementRfi","lendingRfi","mrmRfi"]},qe=["payments","sca"],He=Date.now(),ze=function(e){return Object.keys(Ve).includes(e)},Je=function(e){return void 0===e&&(e=500),new Promise(function(n){return window.setTimeout(n,e)})},Ge=function(e){return Ue[e]},Qe=function(e,n){var t="payments"===e?We:Be;return t[n]||t.prod},Xe=function(e){var n=e.reduce(function(e,n){return e.concat(ze(n)?Ve[n]:n)},[]);return Array.from(new Set(n)).map(function(e){return{elementName:e,sdkName:Ge(e)}})},Ye=function(e){return Array.from(new Set(Xe(e).map(function(e){var n=e.sdkName;return n}).filter(function(e){return qe.includes(e)})))},Ze=function(e){return"payments"===e?"payment":e},en=function(e){var n=e.name,t=e.env,o=void 0===t?we.prod:t,i=Qe(n,o),r=$e[n];if(!r)throw Ae("[components-sdk] Element static resource not found",{elementName:n}),new Error("Element ".concat(n," static resource URL is invalid."));return"".concat(i).concat(r,"?ts=").concat(He)},nn=function(){return qe.reduce(function(e,n){var t;return de(de({},e),(t={},t[n]=window._AirwallexSDKs.__controller__.internalSDKs[n],t))},{})},tn=function(){return Math.floor(performance.now())},on=function(e){var n=document.createElement("script");n.src=e,n.type="module";var t=document.head||document.body;return t.appendChild(n),n},rn=3,an=function(e){return me(void 0,void 0,void 0,function(){var n,t;return fe(this,function(o){switch(o.label){case 0:if("undefined"===typeof window)throw new Error("Please load script in browser environment");n=0,t=function(){return me(void 0,void 0,void 0,function(){var n,t;return fe(this,function(o){return n=tn(),t=on(e),[2,new Promise(function(o,i){t.addEventListener("load",function(){var t=tn();Ke("[components-sdk] SDK script loaded",{scriptUrl:e,start:n,latency:t-n,end:t}),o(!0)}),t.addEventListener("error",function(o){t.remove(),console.error(o);var r=tn();Ae("[components-sdk] Failed to load script",{scriptUrl:e,error:o,start:n,latency:r-n,end:r}),i(new Error("Failed to load Airwallex SDK scripts: ".concat(e)))})})]})})},o.label=1;case 1:if(!(n<rn))return[3,7];o.label=2;case 2:return o.trys.push([2,4,,6]),[4,t()];case 3:return[2,o.sent()];case 4:return o.sent(),n++,[4,Je()];case 5:return o.sent(),[3,6];case 6:return[3,1];case 7:throw Ae("[components-sdk] Failed to load script after retry",{scriptUrl:e}),new Error("Failed to load Airwallex SDK scripts: ".concat(e))}})})},sn=new Map,cn=function(e){var n=e.env,t=e.scriptName,o=en({name:t,env:n}),i=sn.get(t);if(i)return i;var r=an(o);return sn.set(t,r),r},ln=function(e){return me(void 0,void 0,void 0,function(){return fe(this,function(n){if("undefined"===typeof window)throw new Error("Please call the `init()` function in a browser environment.");return Te(e.env,e.clientId),Fe=new Promise(function(n,t){var o,i=de(de({},e),{env:e.env&&we[e.env]?e.env:we.prod});window.AirwallexComponentsSDK.__env__=i.env;var r=cn({env:i.env,scriptName:Re}).then(function(){return window._AirwallexSDKs.__controller__.init(i)}),a=null!==(o=e.enabledElements)&&void 0!==o?o:[],s=Ye(a),c=s.map(function(e){return cn({env:i.env,scriptName:e}).then(function(){return r}).then(function(){return window._AirwallexSDKs.__controller__.registerFunctions({functionsNamespace:e,instance:window._AirwallexSDKs[Ze(e)]})})}),l=Xe(a),u=l.map(function(e){var n=e.sdkName,t=e.elementName;return cn({scriptName:n,env:i.env}).then(function(){return r}).then(function(){return window._AirwallexSDKs.__controller__.registerElement({sdkName:n,elementName:t,instance:window._AirwallexSDKs[Ze(n)]})})});Promise.all(he(he([r],c,!0),u,!0)).then(function(){Ke("[components-sdk] SDK initialized",{options:je(e),start:tn()}),n(nn())})["catch"](function(e){e.code||Ae("[components-sdk] Unexpected errors when init",{error:e}),t(e)})}),[2,Fe]})})},un=function(e,n){return me(void 0,void 0,void 0,function(){var t,o,i,r,a;return fe(this,function(s){switch(s.label){case 0:if(!e)throw new Error("Element type is missing. Please specify a valid element.");if(!Fe)throw Oe("[components-sdk] Did not call init before createElement"),new Error("Please initialize the Element before creating it.");return[4,Fe];case 1:if(s.sent(),t=Ge(e),!t)throw new Error("`createElement()` with type `".concat(e,"` is not supported. Please specify a valid Element type."));o=tn(),s.label=2;case 2:return s.trys.push([2,6,,7]),[4,cn({scriptName:t,env:window.AirwallexComponentsSDK.__env__||we.prod})];case 3:return s.sent(),[4,window._AirwallexSDKs.__controller__.registerElement({sdkName:t,elementName:e,instance:window._AirwallexSDKs[Ze(t)]})];case 4:return s.sent(),[4,window._AirwallexSDKs.__controller__.createElement(e,n)];case 5:return i=s.sent(),r=tn(),Ke("[components-sdk] SDK createElement being called",{elementName:e,options:je(n),start:o,latency:r-o,end:r}),[3,7];case 6:if(a=s.sent(),a.code)throw a;return Ae("[components-sdk] Unexpected errors when createElement",{error:a}),[3,7];case 7:return[2,i]}})})},pn=[Re],dn=function(){pn.forEach(function(e){var n=en({name:e,env:we.prod}),t=document.createElement("link");t.href=n,t.rel="modulepreload",t.as="script";var o=document.head||document.body;o.appendChild(t)})};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",dn):dn(),"undefined"!==typeof window&&(Object.defineProperties(window,{AirwallexComponentsSDK:{value:{},writable:!1},_AirwallexSDKs:{value:{},writable:!1}}),window.AirwallexComponentsSDK.init=ln,window.AirwallexComponentsSDK.createElement=un)}}]);