"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[2035],{23846:function(e,a,t){t.r(a),t.d(a,{default:function(){return r}});var r={payButton:"Πληρωμή","payButton.redirecting":"Ανακατεύθυνση...","payButton.with":"Πληρωμή %{value} με %{maskedData}","payButton.saveDetails":"Αποθήκευση στοιχείων",close:"Κλείσιμο",storeDetails:"Αποθήκευση για την επόμενη πληρωμή μου",readMore:"Ανάγνωση περισσότερων","creditCard.holderName":"Όνομα στην κάρτα","creditCard.holderName.placeholder":"Γ. Παπαδάκης","creditCard.holderName.invalid":"Εισαγάγετε το όνομα όπως αναγράφεται στην κάρτα","creditCard.numberField.title":"Αριθμός κάρτας","creditCard.numberField.placeholder":"1234 **************","creditCard.expiryDateField.title":"Ημερομηνία λήξης","creditCard.expiryDateField.placeholder":"ΜΜ/ΕΕ","creditCard.expiryDateField.month":"Μήνας","creditCard.expiryDateField.month.placeholder":"ΜΜ","creditCard.expiryDateField.year.placeholder":"ΕΕ","creditCard.expiryDateField.year":"Έτος","creditCard.cvcField.title":"Κωδικός ασφαλείας","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Απομνημόνευση για την επόμενη φορά","creditCard.cvcField.placeholder.4digits":"4ψήφιος","creditCard.cvcField.placeholder.3digits":"3ψήφιος","creditCard.taxNumber.placeholder":"ΕΕΜΜΗΗ / 0123456789",installments:"Αριθμός δόσεων",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} μήνες","installments.oneTime":"Εφάπαξ πληρωμή","installments.installments":"Πληρωμή με δόσεις","installments.revolving":"Ανακυκλούμενη πληρωμή","sepaDirectDebit.ibanField.invalid":"Μη έγκυρος αριθμός λογαριασμού","sepaDirectDebit.nameField.placeholder":"Γ. Παπαδάκης","sepa.ownerName":"Όνομα κατόχου","sepa.ibanNumber":"Αριθμός λογαριασμού (IBAN)","error.title":"Σφάλμα","error.subtitle.redirect":"Η ανακατεύθυνση απέτυχε","error.subtitle.payment":"Η πληρωμή απέτυχε","error.subtitle.refused":"Η πληρωμή απορρίφθηκε","error.message.unknown":"Προέκυψε άγνωστο σφάλμα","errorPanel.title":"Υπάρχοντα σφάλματα","idealIssuer.selectField.title":"Τράπεζα","idealIssuer.selectField.placeholder":"Επιλέξτε την τράπεζά σας","creditCard.success":"Η πληρωμή ολοκληρώθηκε επιτυχώς",loading:"Φόρτωση...",continue:"Συνέχεια",continueTo:"Μετάβαση στην","wechatpay.timetopay":"Έχετε στη διάθεσή σας %@ για την πληρωμή","sr.wechatpay.timetopay":"Έχετε %#λεπτά%# %#δευτερόλεπτα%# για να πληρώσετε","wechatpay.scanqrcode":"Σάρωση κωδικού QR",personalDetails:"Προσωπικά στοιχεία",companyDetails:"Στοιχεία εταιρείας","companyDetails.name":"Όνομα εταιρείας","companyDetails.registrationNumber":"Αριθμός μητρώου",socialSecurityNumber:"Αριθμός φορολογικού μητρώου",firstName:"Όνομα","firstName.invalid":"Πληκτρολογήστε το όνομά σας",infix:"Πρόθεμα",lastName:"Επώνυμο","lastName.invalid":"Πληκτρολογήστε το επώνυμό σας",mobileNumber:"Αριθμός κινητού","mobileNumber.invalid":"Μη έγκυρος αριθμός κινητού",city:"Πόλη",postalCode:"Ταχυδρομικός κωδικός","postalCode.optional":"Ταχυδρομικός κωδικός (προαιρετικό)",countryCode:"Κωδικός χώρας",telephoneNumber:"Αριθμός τηλεφώνου",dateOfBirth:"Ημερομηνία γέννησης",shopperEmail:"Διεύθυνση email",gender:"Φύλο","gender.notselected":"Επιλέξτε το φύλο σας",male:"Άντρας",female:"Γυναίκα",billingAddress:"Διεύθυνση τιμολόγησης",street:"Οδός",stateOrProvince:"Πολιτεία ή επαρχία",country:"Χώρα/Περιοχή",houseNumberOrName:"Αριθμός οικίας",separateDeliveryAddress:"Καθορίστε μια ξεχωριστή διεύθυνση παράδοσης",deliveryAddress:"Διεύθυνση παράδοσης","deliveryAddress.firstName":"Όνομα παραλήπτη","deliveryAddress.lastName":"Επώνυμο παραλήπτη",zipCode:"Ταχυδρομικός κώδικας",apartmentSuite:"Διαμέρισμα/Γραφείο",provinceOrTerritory:"Επαρχία ή περιφέρεια",cityTown:"Πόλη / Κοινότητα",address:"Διεύθυνση","address.placeholder":"Βρείτε τη διεύθυνσή σας","address.errors.incomplete":"Εισαγάγετε μια διεύθυνση για να συνεχίσετε","address.enterManually":"Εισαγάγετε τη διεύθυνση μη αυτόματα",state:"Πολιτεία","field.title.optional":"(προαιρετικό)","creditCard.cvcField.title.optional":"Κωδικός ασφαλείας (προαιρετικό)","issuerList.wallet.placeholder":"Επιλέξτε το πορτοφόλι σας",privacyPolicy:"Πολιτική απορρήτου","afterPay.agreement":"Αποδέχομαι τους %@ του Riverty","riverty.termsAndConditions":"Συμφωνώ με τους γενικούς %#Όρους και προϋποθέσεις%# για τη μέθοδο πληρωμής Riverty. Μπορείτε να βρείτε την πολιτική απορρήτου της Riverty %#εδώ%#.",paymentConditions:"όρους πληρωμής",openApp:"Άνοιγμα της εφαρμογής","voucher.readInstructions":"Διαβάστε τις οδηγίες","voucher.introduction":"Σας ευχαριστούμε για την αγορά. Χρησιμοποιήστε το παρακάτω κουπόνι για να ολοκληρώσετε την πληρωμή.","voucher.expirationDate":"Ημερομηνία λήξης","voucher.alternativeReference":"Εναλλακτική αναφορά","dragonpay.voucher.non.bank.selectField.placeholder":"Επιλέξτε τον πάροχό σας","dragonpay.voucher.bank.selectField.placeholder":"Επιλέξτε την τράπεζά σας","voucher.paymentReferenceLabel":"Αναφορά πληρωμής","voucher.surcharge":"Περιλαμβάνεται πρόσθετη χρέωση %@","voucher.introduction.doku":"Σας ευχαριστούμε για την αγορά. Χρησιμοποιήστε τις ακόλουθες πληροφορίες για να ολοκληρώσετε την πληρωμή.","voucher.shopperName":"Όνομα αγοραστή","voucher.merchantName":"Έμπορος","voucher.introduction.econtext":"Σας ευχαριστούμε για την αγορά. Χρησιμοποιήστε τις ακόλουθες πληροφορίες για να ολοκληρώσετε την πληρωμή.","voucher.telephoneNumber":"Αριθμός τηλεφώνου","voucher.shopperReference":"Αναφορά αγοραστή","voucher.collectionInstitutionNumber":"Αριθμός πρακτορείου εισπράξεων","voucher.econtext.telephoneNumber.invalid":"Ο αριθμός τηλεφώνου πρέπει να περιέχει 10 ή 11 ψηφία","boletobancario.btnLabel":"Δημιουργία Boleto","boleto.sendCopyToEmail":"Αποστολή αντιγράφου στη διεύθυνση email μου","button.copy":"Αντιγραφή","button.download":"Λήψη","boleto.socialSecurityNumber.invalid":"Εισαγάγετε  έγκυρο αριθμό CPF/CNPJ","creditCard.storedCard.description.ariaLabel":"Η αποθηκευμένη κάρτα τελειώνει σε %@","voucher.entity":"Οντότητα",donateButton:"Δωρεά",notNowButton:"Όχι τώρα",thanksForYourSupport:"Σας ευχαριστούμε για την υποστήριξη!","resultMessages.preauthorized":"Τα στοιχεία αποθηκεύτηκαν",preauthorizeWith:"Προεξουσιοδότηση με",confirmPreauthorization:"Επιβεβαίωση προεξουσιοδότησης",confirmPurchase:"Επιβεβαίωση αγοράς",applyGiftcard:"Εξαργύρωση",giftcardBalance:"Υπόλοιπο δωροκάρτας",deductedBalance:"Υπόλοιπο που αφαιρέθηκε","creditCard.pin.title":"Κωδικός PIN","creditCard.encryptedPassword.label":"Τα πρώτα 2 ψηφία του κωδικού πρόσβασης της κάρτας","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Μη έγκυρος κωδικός πρόσβασης","creditCard.taxNumber":"Ημερομηνία γέννησης κατόχου κάρτας ή αριθμός μητρώου εταιρειών","creditCard.taxNumber.label":"Ημερομηνία γέννησης κατόχου κάρτας (YYMMDD) ή αριθμός μητρώου εταιρειών (10 ψηφία)","creditCard.taxNumber.labelAlt":"Αριθμός μητρώου εταιρειών (10 ψηφία)","creditCard.taxNumber.invalid":"Μη έγκυρη ημερομηνία γέννησης κατόχου κάρτας ή αριθμός μητρώου εταιρειών","storedPaymentMethod.disable.button":"Αφαίρεση","storedPaymentMethod.disable.confirmation":"Αφαίρεση αποθηκευμένου τρόπου πληρωμής","storedPaymentMethod.disable.confirmButton":"Ναι, αφαίρεση","storedPaymentMethod.disable.cancelButton":"Άκυρο","ach.bankAccount":"Τραπεζικός λογαριασμός","ach.accountHolderNameField.title":"Όνομα κατόχου λογαριασμού","ach.accountHolderNameField.placeholder":"Γ. Παπαδάκης","ach.accountHolderNameField.invalid":"Μη έγκυρο όνομα κατόχου λογαριασμού","ach.accountNumberField.title":"Αριθμός λογαριασμού","ach.accountNumberField.invalid":"Μη έγκυρος αριθμός λογαριασμού","ach.accountLocationField.title":"Αριθμός δρομολόγησης ABA","ach.accountLocationField.invalid":"Μη έγκυρος αριθμός δρομολόγησης ABA","ach.savedBankAccount":"Αποθηκευμένος τραπεζικός λογαριασμός","ach.savings":"Λογαριασμός ταμιευτηρίου","ach.checking":"Τρεχούμενος λογαριασμός","select.state":"Επιλέξτε πολιτεία","select.stateOrProvince":"Επιλέξτε πολιτεία ή επαρχία","select.provinceOrTerritory":"Επιλέξτε επαρχία ή περιφέρεια","select.country":"Επιλέξτε χώρα/περιοχή","select.noOptionsFound":"Δεν βρέθηκαν επιλογές","select.filter.placeholder":"Αναζήτηση...","telephoneNumber.invalid":"Μη έγκυρος αριθμός τηλεφώνου",qrCodeOrApp:"ή","paypal.processingPayment":"Επεξεργασία πληρωμής...",generateQRCode:"Δημιουργία κωδικού QR","await.waitForConfirmation":"Αναμονή για επιβεβαίωση…","mbway.confirmPayment":"Επιβεβαιώστε την πληρωμή στην εφαρμογή MB WAY","shopperEmail.invalid":"Μη έγκυρη διεύθυνση email","dateOfBirth.format":"ΗΗ/ΜΜ/ΕΕΕΕ","dateOfBirth.invalid":"Πληκτρολογήστε έγκυρη ημερομηνία γέννησης που υποδηλώνει ότι είστε ηλικίας τουλάχιστον 18 ετών","blik.confirmPayment":"Ανοίξτε την εφαρμογή τραπεζικής σας για να επιβεβαιώσετε την πληρωμή.","blik.invalid":"Εισαγάγετε 6 ψηφία","blik.code":"6ψήφιος κωδικός","blik.help":"Λάβετε τον κωδικό από την εφαρμογή τραπεζικής σας.","swish.pendingMessage":"Μετά τη σάρωση, η κατάσταση μπορεί να είναι εκκρεμής για έως 10 λεπτά. Η απόπειρα εκ νέου πληρωμής εντός αυτού του χρονικού διαστήματος ενδέχεται να προκαλέσει πρόσθετη χρέωση.","field.valid":"Το πεδίο είναι έγκυρο","field.invalid":"Το πεδίο δεν είναι έγκυρο","error.va.gen.01":"Ελλιπές πεδίο","error.va.gen.02":"Το πεδίο δεν είναι έγκυρο","error.va.sf-cc-num.01":"Εισαγάγετε έγκυρο αριθμό κάρτας","error.va.sf-cc-num.02":"Εισαγάγετε τον αριθμό της κάρτας","error.va.sf-cc-num.03":"Εισαγάγετε μια υποστηριζόμενη επωνυμία κάρτας","error.va.sf-cc-num.04":"Εισαγάγετε τον πλήρη αριθμό της κάρτας","error.va.sf-cc-dat.01":"Εισαγάγετε έγκυρη ημερομηνία λήξης","error.va.sf-cc-dat.02":"Εισαγάγετε έγκυρη ημερομηνία λήξης","error.va.sf-cc-dat.03":"Η πιστωτική κάρτα πρόκειται να λήξει σύντομα","error.va.sf-cc-dat.04":"Εισαγάγετε την ημερομηνία λήξης","error.va.sf-cc-dat.05":"Εισαγάγετε την πλήρη ημερομηνία λήξης","error.va.sf-cc-mth.01":"Εισαγάγετε τον μήνα λήξης","error.va.sf-cc-yr.01":"Εισαγάγετε το έτος λήξης","error.va.sf-cc-yr.02":"Εισαγάγετε το πλήρες έτος λήξης","error.va.sf-cc-cvc.01":"Εισαγάγετε τον κωδικό ασφαλείας","error.va.sf-cc-cvc.02":"Εισαγάγετε τον πλήρη κωδικό ασφαλείας","error.va.sf-ach-num.01":"Το πεδίο αριθμού τραπεζικού λογαριασμού είναι κενό","error.va.sf-ach-num.02":"Ο αριθμός τραπεζικού λογαριασμού δεν περιέχει τον σωστό αριθμό χαρακτήρων","error.va.sf-ach-loc.01":"Το πεδίο αριθμού δρομολόγησης τράπεζας είναι κενό","error.va.sf-ach-loc.02":"Ο αριθμός δρομολόγησης τράπεζας δεν περιέχει τον σωστό αριθμό χαρακτήρων","error.va.sf-kcp-pwd.01":"Το πεδίο κωδικού πρόσβασης είναι κενό","error.va.sf-kcp-pwd.02":"Ο κωδικός πρόσβασης δεν περιέχει τον σωστό αριθμό χαρακτήρων","error.giftcard.no-balance":"Η συγκεκριμένη δωροκάρτα έχει μηδενικό υπόλοιπο","error.giftcard.card-error":"Στα αρχεία μας δεν υπάρχει δωροκάρτα με αυτόν τον αριθμό","error.giftcard.currency-error":"Οι δωροκάρτες ισχύουν μόνο για το νόμισμα στο οποίο εκδόθηκαν","amazonpay.signout":"Αποσύνδεση από το Amazon","amazonpay.changePaymentDetails":"Αλλαγή στοιχείων πληρωμής","partialPayment.warning":"Επιλέξτε έναν άλλο τρόπο πληρωμής για καταβολή του εναπομείναντος ποσού","partialPayment.remainingBalance":"Το υπόλοιπο θα είναι %{amount}","bankTransfer.beneficiary":"Δικαιούχος","bankTransfer.iban":"ΙΒΑΝ","bankTransfer.bic":"BIC","bankTransfer.reference":"Αναφορά","bankTransfer.introduction":"Συνεχίστε για να δημιουργήσετε νέα πληρωμή μέσω τραπεζικής μεταφοράς. Μπορείτε να χρησιμοποιήσετε τα στοιχεία στην ακόλουθη οθόνη για να ολοκληρώσετε αυτήν την πληρωμή.","bankTransfer.instructions":"Σας ευχαριστούμε για την αγορά. Χρησιμοποιήστε τις ακόλουθες πληροφορίες για να ολοκληρώσετε την πληρωμή.","bacs.accountHolderName":"Όνομα δικαιούχου τραπεζικού λογαριασμού","bacs.accountHolderName.invalid":"Μη έγκυρο όνομα δικαιούχου τραπεζικού λογαριασμού","bacs.accountNumber":"Αριθμός τραπεζικού λογαριασμού","bacs.accountNumber.invalid":"Μη έγκυρος αριθμός τραπεζικού λογαριασμού","bacs.bankLocationId":"Κωδικός τράπεζας","bacs.bankLocationId.invalid":"Μη έγκυρος κωδικός τράπεζας","bacs.consent.amount":"Αποδέχομαι ότι το πιο πάνω ποσό θα αφαιρεθεί από τον τραπεζικό λογαριασμό μου.","bacs.consent.account":"Επιβεβαιώνω ότι ο λογαριασμός είναι στο όνομά μου και πως είμαι ο/η μοναδικός/μοναδική υπογράφων/υπογράφουσα που απαιτείται για εξουσιοδότηση της Άμεσης Χρέωσης σε αυτόν τον λογαριασμό.",edit:"Επεξεργασία","bacs.confirm":"Επιβεβαίωση και πληρωμή","bacs.result.introduction":"Κατεβάστε την Εντολή Άμεσης Χρέωσης (DDI/Εντολή)","download.pdf":"Λήψη PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe για αριθμό κάρτας","creditCard.encryptedCardNumber.aria.label":"Αριθμός κάρτας","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe για ημερομηνία λήξης","creditCard.encryptedExpiryDate.aria.label":"Ημερομηνία λήξης","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe για μήνα λήξης","creditCard.encryptedExpiryMonth.aria.label":"Μήνας λήξης","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe για έτος λήξης","creditCard.encryptedExpiryYear.aria.label":"Έτος λήξης","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe για κωδικό ασφαλείας","creditCard.encryptedSecurityCode.aria.label":"Κωδικός ασφαλείας","creditCard.encryptedPassword.aria.iframeTitle":"Iframe για κωδικό πρόσβασης","creditCard.encryptedPassword.aria.label":"Τα πρώτα 2 ψηφία του κωδικού πρόσβασης της κάρτας","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe για αριθμό κάρτας","giftcard.encryptedCardNumber.aria.label":"Αριθμός κάρτας","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe για PIN","giftcard.encryptedSecurityCode.aria.label":"Κωδικός PIN",giftcardTransactionLimit:"Το μέγιστο επιτρεπόμενο ποσό ανά συναλλαγή σε αυτήν τη δωροκάρτα είναι %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe για αριθμό τραπεζικού λογαριασμού","ach.encryptedBankAccountNumber.aria.label":"Αριθμός λογαριασμού","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe για αριθμό κωδικού υποκαταστήματος τράπεζας","ach.encryptedBankLocationId.aria.label":"Αριθμός δρομολόγησης ABA","twint.saved":"αποθηκεύτηκε",orPayWith:"ή πληρώστε με",invalidFormatExpects:"Μη έγκυρη μορφή. Αναμενόμενη μορφή: %{format}","upi.qrCodeWaitingMessage":"Σαρώστε τον κωδικό QR χρησιμοποιώντας την προτιμώμενη εφαρμογή UPI για ολοκλήρωση της πληρωμής","upi.vpaWaitingMessage":"Ανοίξτε την εφαρμογή UPI για επιβεβαίωση της πληρωμής","upi.modeSelection":"Πώς θα θέλατε να χρησιμοποιήσετε το UPI;","upi.completePayment":"Ολοκλήρωση της πληρωμής","upi.mode.enterUpiId":"Εισαγωγή αναγνωριστικού UPI","upi.mode.qrCode":"Κωδικός QR","upi.mode.payByAnyUpi":"Πληρώστε με οποιαδήποτε εφαρμογή UPI","upi.collect.dropdown.label":"Εισαγωγή αναγνωριστικού UPI","upi.collect.field.label":"Εισαγωγή αναγνωριστικού UPI / VPA","onlineBanking.termsAndConditions":"Αν συνεχίσετε, συνεπάγεται ότι αποδέχεστε τους %#Όρους και προϋποθέσεις%#","onlineBankingPL.termsAndConditions":"Συνεχίζοντας, συμφωνείτε με τους %#κανονισμούς%# και την %#υποχρέωση ενημέρωσης%# της Przelewy24","ctp.loading.poweredByCtp":"Με την υποστήριξη του Click to Pay","ctp.loading.intro":"Έλεγχος για να διαπιστωθεί αν υπάρχουν αποθηκευμένες κάρτες με Click to Pay...","ctp.login.title":"Συνέχεια στο Click to Pay","ctp.login.subtitle":"Εισαγάγετε τη διεύθυνση email που είναι συνδεδεμένη με το Click to Pay για να συνεχίσετε.","ctp.login.inputLabel":"Email","ctp.logout.notYou":"Δεν είστε εσείς αυτό το άτομο;","ctp.logout.notYourCards":"Αυτές δεν είναι οι κάρτες σας;","ctp.logout.notYourCard":"Δεν είναι η κάρτα σας;","ctp.logout.notYourProfile":"Δεν είναι το προφίλ σας;","ctp.otp.fieldLabel":"Κωδικός μιας χρήσης","ctp.otp.resendCode":"Εκ νέου αποστολή κωδικού","ctp.otp.codeResent":"Ο κωδικός στάλθηκε εκ νέου","ctp.otp.title":"Πρόσβαση στις κάρτες Click to Pay σας","ctp.otp.subtitle":"Εισαγάγετε τον κωδικό %@ που στάλθηκε στο %@ για να επαληθεύσετε ότι είστε εσείς.","ctp.otp.saveCookiesCheckbox.label":"Παράβλεψη επαλήθευσης την επόμενη φορά","ctp.otp.saveCookiesCheckbox.information":"Επιλέξτε να απομνημονεύεται στη συσκευή και στο πρόγραμμα περιήγησής σας στα συμμετέχοντα καταστήματα για ταχύτερη ολοκλήρωση της πληρωμής. Δεν συνιστάται για κοινόχρηστες συσκευές.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Επιλέξτε να απομνημονεύεται στη συσκευή και στο πρόγραμμα περιήγησής σας","ctp.emptyProfile.message":"Δεν υπάρχουν καταχωρισμένες κάρτες σε αυτό το προφίλ Click to Pay","ctp.separatorText":"ή χρησιμοποιήστε το","ctp.cards.title":"Ολοκληρώστε την πληρωμή με το Click to Pay","ctp.cards.subtitle":"Επιλέξτε μια κάρτα προς χρήση.","ctp.cards.expiredCard":"Έληξε","ctp.manualCardEntry":"Χειροκίνητη καταχώριση κάρτας","ctp.aria.infoModalButton":"Τι είναι το Click to Pay","ctp.infoPopup.title":"Το Click to Pay προσφέρει την ευκολία ανέπαφων διαδικτυακών","ctp.infoPopup.subtitle":"Ένας γρήγορος, ασφαλής τρόπος πληρωμής που υποστηρίζεται από τις κάρτες Mastercard, Visa και άλλες κάρτες πληρωμών.","ctp.infoPopup.benefit1":"Το Click to Pay χρησιμοποιεί κρυπτογράφηση για να διατηρεί τις πληροφορίες σας ασφαλείς","ctp.infoPopup.benefit2":"Χρησιμοποιήστε τη δυνατότητα σε εμπόρους ανά τον κόσμο","ctp.infoPopup.benefit3":"Εύκολη διαμόρφωση για άνετες πληρωμές ανά πάσα στιγμή","ctp.errors.AUTH_INVALID":"Ο έλεγχος ταυτότητας δεν ήταν έγκυρος","ctp.errors.NOT_FOUND":"Δεν βρέθηκε λογαριασμός, εισαγάγετε μια έγκυρη διεύθυνση email ή συνεχίστε για χειροκίνητη εισαγωγή κάρτας","ctp.errors.ID_FORMAT_UNSUPPORTED":"Η μορφή δεν υποστηρίζεται","ctp.errors.FRAUD":"Ο λογαριασμός χρήστη κλειδώθηκε ή απενεργοποιήθηκε","ctp.errors.CONSUMER_ID_MISSING":"Η ταυτότητα καταναλωτή λείπει στο αίτημα","ctp.errors.ACCT_INACCESSIBLE":"Αυτός ο λογαριασμός δεν είναι διαθέσιμος τώρα, π.χ. είναι κλειδωμένος","ctp.errors.CODE_INVALID":"Λανθασμένος κωδικός επαλήθευσης","ctp.errors.CODE_EXPIRED":"Αυτός ο κωδικός έχει λήξει","ctp.errors.RETRIES_EXCEEDED":"Σημειώθηκε υπέρβαση του αριθμού νέων αποπειρών δημιουργίας OTP","ctp.errors.OTP_SEND_FAILED":"Δεν ήταν δυνατή η αποστολή του OTP στον παραλήπτη","ctp.errors.REQUEST_TIMEOUT":"Παρουσιάστηκε κάποιο πρόβλημα, δοκιμάστε ξανά ή χρησιμοποιήστε τη χειροκίνητη εισαγωγή κάρτας","ctp.errors.UNKNOWN_ERROR":"Παρουσιάστηκε κάποιο πρόβλημα, δοκιμάστε ξανά ή χρησιμοποιήστε τη χειροκίνητη εισαγωγή κάρτας","ctp.errors.SERVICE_ERROR":"Παρουσιάστηκε κάποιο πρόβλημα, δοκιμάστε ξανά ή χρησιμοποιήστε τη χειροκίνητη εισαγωγή κάρτας","ctp.errors.SERVER_ERROR":"Παρουσιάστηκε κάποιο πρόβλημα, δοκιμάστε ξανά ή χρησιμοποιήστε τη χειροκίνητη εισαγωγή κάρτας","ctp.errors.INVALID_PARAMETER":"Παρουσιάστηκε κάποιο πρόβλημα, δοκιμάστε ξανά ή χρησιμοποιήστε τη χειροκίνητη εισαγωγή κάρτας","ctp.errors.AUTH_ERROR":"Παρουσιάστηκε κάποιο πρόβλημα, δοκιμάστε ξανά ή χρησιμοποιήστε τη χειροκίνητη εισαγωγή κάρτας","paymentMethodsList.aria.label":"Επιλέξτε έναν τρόπο πληρωμής","companyDetails.name.invalid":"Εισαγάγετε το όνομα της εταιρείας","companyDetails.registrationNumber.invalid":"Εισαγάγετε τον αριθμό μητρώου","consent.checkbox.invalid":"Πρέπει να συμφωνήσετε με τους όρους και τις προϋποθέσεις","form.instruction":"Όλα τα πεδία είναι υποχρεωτικά, εκτός εάν επισημαίνεται διαφορετικά.","trustly.descriptor":"Άμεση τραπεζική πληρωμή","trustly.description1":"Πληρώστε απευθείας από οποιονδήποτε τραπεζικό λογαριασμό σας, με ασφάλεια τραπεζικού επιπέδου","trustly.description2":"Χωρίς κάρτες, χωρίς λήψη εφαρμογής, χωρίς εγγραφή","ancv.input.label":"Η ταυτότητά σας ANCV","ancv.confirmPayment":"Χρησιμοποιήστε την εφαρμογή ANCV για επιβεβαίωση της πληρωμής.","ancv.form.instruction":"Η εφαρμογή Cheque-Vacances είναι απαραίτητη για επικύρωση της πληρωμής αυτής.","ancv.beneficiaryId.invalid":"Εισαγάγετε έγκυρη διεύθυνση email ή αναγνωριστικό ANCV","payme.openPayMeApp":"Ολοκληρώστε την πληρωμή σας στην εφαρμογή PayMe εξουσιοδοτώντας την πληρωμή στην εφαρμογή και περιμένετε την επιβεβαίωση.","payme.redirectButtonLabel":"Ανοίξτε την εφαρμογή PayMe","payme.scanQrCode":"Ολοκληρώστε την πληρωμή σας με κωδικό QR","payme.timeToPay":"Αυτός ο κωδικός QR ισχύει για %@","payme.instructions.steps":"Ανοίξτε την εφαρμογή PayMe.%@Σκανάρετε τον κωδικό QR για να εξουσιοδοτήσετε την πληρωμή.%@Ολοκληρώστε την πληρωμή στην εφαρμογή και περιμένετε την επιβεβαίωση.","payme.instructions.footnote":"Μην κλείσετε αυτήν τη σελίδα προτού ολοκληρωθεί η πληρωμή.","payByBankAISDD.disclaimer.header":"Χρησιμοποιήστε το Pay by Bank για άμεση πληρωμή από οποιονδήποτε τραπεζικό λογαριασμό.","payByBankAISDD.disclaimer.body":"Συνδέοντας τον τραπεζικό λογαριασμό σας, εξουσιοδοτείτε χρεώσεις στον λογαριασμό σας για οποιοδήποτε ποσό οφείλεται για τη χρήση των υπηρεσιών μας ή/και την αγορά των προϊόντων μας, έως ότου ανακληθεί αυτή η εξουσιοδότηση.","paymentMethodBrand.other":"άλλο"}},25415:function(e,a,t){t.r(a),t.d(a,{default:function(){return r}});var r={payButton:"Zaplatit","payButton.redirecting":"Přesměrování...","payButton.with":"Zaplatit %{value} pomocí %{maskedData}","payButton.saveDetails":"Uložit podrobnosti",close:"Zavřít",storeDetails:"Uložit pro příští platby",readMore:"Přečtěte si více","creditCard.holderName":"Jméno na kartě","creditCard.holderName.placeholder":"Jan Novák","creditCard.holderName.invalid":"Zadejte jméno, jak je uvedeno na kartě","creditCard.numberField.title":"Číslo karty","creditCard.numberField.placeholder":"1234 **************","creditCard.expiryDateField.title":"Konec platnosti","creditCard.expiryDateField.placeholder":"MM/RR","creditCard.expiryDateField.month":"Měsíc","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"RR","creditCard.expiryDateField.year":"Rok","creditCard.cvcField.title":"Bezpečnostní kód","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Zapamatovat si pro příště","creditCard.cvcField.placeholder.4digits":"4 číslice","creditCard.cvcField.placeholder.3digits":"3 číslice","creditCard.taxNumber.placeholder":"RRMMDD / 0123456789",installments:"Počet splátek",installmentOption:"%{times}× %{partialValue}",installmentOptionMonths:"%{times} měsíců","installments.oneTime":"Jednorázová platba","installments.installments":"Platba na splátky","installments.revolving":"Opakující se platba","sepaDirectDebit.ibanField.invalid":"Neplatné číslo účtu","sepaDirectDebit.nameField.placeholder":"Jan Novák","sepa.ownerName":"Jméno držitele účtu","sepa.ibanNumber":"Číslo účtu (IBAN)","error.title":"Chyba","error.subtitle.redirect":"Přesměrování selhalo","error.subtitle.payment":"Platba selhala","error.subtitle.refused":"Platba zamítnuta","error.message.unknown":"Došlo k neznámé chybě","errorPanel.title":"Stávající chyby","idealIssuer.selectField.title":"Banka","idealIssuer.selectField.placeholder":"Vyberte svou banku","creditCard.success":"Platba proběhla úspěšně",loading:"Načítání…",continue:"Pokračovat",continueTo:"Pokračovat k","wechatpay.timetopay":"Na zaplacení vám zbývá %@","sr.wechatpay.timetopay":"Na zaplacení máte %#minut%# %#sekund%#","wechatpay.scanqrcode":"Naskenovat QR kód",personalDetails:"Osobní údaje",companyDetails:"Údaje o společnosti","companyDetails.name":"Název společnosti","companyDetails.registrationNumber":"Registrační číslo",socialSecurityNumber:"Rodné číslo",firstName:"Jméno","firstName.invalid":"Zadejte své křestní jméno",infix:"Prefix",lastName:"Příjmení","lastName.invalid":"Zadejte své příjmení",mobileNumber:"Číslo na mobil","mobileNumber.invalid":"Neplatné číslo mobilního telefonu",city:"Město",postalCode:"PSČ","postalCode.optional":"Poštovní směrovací číslo (nepovinné)",countryCode:"Kód země",telephoneNumber:"Telefonní číslo",dateOfBirth:"Datum narození",shopperEmail:"E-mailová adresa",gender:"Pohlaví","gender.notselected":"Vyberte pohlaví",male:"Muž",female:"Žena",billingAddress:"Fakturační adresa",street:"Ulice",stateOrProvince:"Kraj nebo okres",country:"Země/region",houseNumberOrName:"Číslo popisné",separateDeliveryAddress:"Zadat dodací adresu odlišnou od fakturační",deliveryAddress:"Dodací adresa","deliveryAddress.firstName":"Jméno příjemce","deliveryAddress.lastName":"Příjmení příjemce",zipCode:"PSČ",apartmentSuite:"Byt",provinceOrTerritory:"Provincie nebo teritorium",cityTown:"Město",address:"Adresa","address.placeholder":"Najděte svou adresu","address.errors.incomplete":"Chcete-li pokračovat, zadejte adresu","address.enterManually":"Zadejte adresu ručně",state:"Stát","field.title.optional":"(nepovinné)","creditCard.cvcField.title.optional":"Bezpečnostní kód (volitelný)","issuerList.wallet.placeholder":"Vyberte svou peněženku",privacyPolicy:"Zásady ochrany osobních údajů","afterPay.agreement":"Souhlasím s %@ of Riverty","riverty.termsAndConditions":"Souhlasím s obecnými %#Smluvními podmínkami%# platební metody Riverty. Zásady ochrany osobních údajů společnosti Riverty najdete %#tady%#.",paymentConditions:"platebními podmínkami",openApp:"Otevřete aplikaci","voucher.readInstructions":"Přečtěte si pokyny","voucher.introduction":"Děkujeme za nákup. K dokončení platby použijte prosím následující kupón.","voucher.expirationDate":"Datum konce platnosti","voucher.alternativeReference":"Náhradní číslo","dragonpay.voucher.non.bank.selectField.placeholder":"Vyberte svého poskytovatele","dragonpay.voucher.bank.selectField.placeholder":"Vyberte svou banku","voucher.paymentReferenceLabel":"Číslo platby","voucher.surcharge":"Včetně přirážky %@","voucher.introduction.doku":"Děkujeme za nákup. K dokončení platby použijte prosím následující informace.","voucher.shopperName":"Jméno kupujícího","voucher.merchantName":"Obchodník","voucher.introduction.econtext":"Děkujeme za nákup. K dokončení platby použijte prosím následující informace.","voucher.telephoneNumber":"Telefonní číslo","voucher.shopperReference":"Číslo kupujícího","voucher.collectionInstitutionNumber":"Číslo inkasní instituce","voucher.econtext.telephoneNumber.invalid":"Telefonní číslo musí obsahovat 10 nebo 11 číslic.","boletobancario.btnLabel":"Vygenerovat Boleto","boleto.sendCopyToEmail":"Poslat mi kopii na e-mail","button.copy":"Kopírovat","button.download":"Stáhnout","boleto.socialSecurityNumber.invalid":"Zadejte platné číslo CPF/CNPJ","creditCard.storedCard.description.ariaLabel":"Uložená karta končí na %@","voucher.entity":"Subjekt",donateButton:"Přispět",notNowButton:"Teď ne",thanksForYourSupport:"Děkujeme vám za podporu!","resultMessages.preauthorized":"Uložené podrobnosti",preauthorizeWith:"Předautorizovat pomocí",confirmPreauthorization:"Potvrdit předautorizaci",confirmPurchase:"Potvrdit nákup",applyGiftcard:"Uplatnit",giftcardBalance:"Zůstatek na dárkové kartě",deductedBalance:"Odečtený zůstatek","creditCard.pin.title":"Pin","creditCard.encryptedPassword.label":"První 2 číslice hesla karty","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Neplatné heslo","creditCard.taxNumber":"Datum narození držitele karty nebo registrační číslo společnosti","creditCard.taxNumber.label":"Datum narození držitele karty (RRMMDD) nebo registrační číslo společnosti (10 číslic)","creditCard.taxNumber.labelAlt":"Registrační číslo společnosti (10 číslic)","creditCard.taxNumber.invalid":"Neplatné datum narození držitele karty nebo registrační číslo společnosti","storedPaymentMethod.disable.button":"Odebrat","storedPaymentMethod.disable.confirmation":"Odebrat uložený způsob platby","storedPaymentMethod.disable.confirmButton":"Ano, odebrat","storedPaymentMethod.disable.cancelButton":"Zrušit","ach.bankAccount":"Bankovní účet","ach.accountHolderNameField.title":"Jméno držitele účtu","ach.accountHolderNameField.placeholder":"Jan Novák","ach.accountHolderNameField.invalid":"Neplatné jméno držitele účtu","ach.accountNumberField.title":"Číslo účtu","ach.accountNumberField.invalid":"Neplatné číslo účtu","ach.accountLocationField.title":"Směrovací tranzitní číslo ABA","ach.accountLocationField.invalid":"Neplatné směrovací tranzitní číslo ABA","ach.savedBankAccount":"Uložený bankovní účet","ach.savings":"Spořicí účet","ach.checking":"Běžný účet","select.state":"Vyberte stát","select.stateOrProvince":"Vyberte kraj nebo okres","select.provinceOrTerritory":"Vyberte provincii nebo teritorium","select.country":"Vyberte zemi/region","select.noOptionsFound":"Nebyly nalezeny žádné možnosti","select.filter.placeholder":"Hledat...","telephoneNumber.invalid":"Neplatné telefonní číslo",qrCodeOrApp:"nebo","paypal.processingPayment":"Zpracování platby...",generateQRCode:"Vygenerovat QR kód","await.waitForConfirmation":"Čeká se na potvrzení","mbway.confirmPayment":"Potvrďte platbu v aplikaci MB WAY","shopperEmail.invalid":"Neplatná e-mailová adresa","dateOfBirth.format":"DD/MM/RRRR","dateOfBirth.invalid":"Zadejte platné datum narození, ze kterého vyplývá, že je vám alespoň 18 let","blik.confirmPayment":"Spusťte bankovní aplikaci a potvrďte platbu.","blik.invalid":"Zadejte 6 čísel","blik.code":"Šestimístný kód","blik.help":"Získejte kód z bankovní aplikace.","swish.pendingMessage":"Po naskenování QR kódu může trvat až 10 minut, než se stav změní. Pokud budete zkoušet během této doby platbu opakovat, může být částka zaplacena vícekrát.","field.valid":"Platné pole","field.invalid":"Pole není platné","error.va.gen.01":"Neúplné pole","error.va.gen.02":"Pole není platné","error.va.sf-cc-num.01":"Zadejte platné číslo karty","error.va.sf-cc-num.02":"Zadejte číslo karty","error.va.sf-cc-num.03":"Zadejte podporovanou značku karty","error.va.sf-cc-num.04":"Zadejte celé číslo karty","error.va.sf-cc-dat.01":"Zadejte platné datum vypršení platnosti","error.va.sf-cc-dat.02":"Zadejte platné datum vypršení platnosti","error.va.sf-cc-dat.03":"Platnost kreditní karty brzy vyprší","error.va.sf-cc-dat.04":"Zadejte datum vypršení platnosti","error.va.sf-cc-dat.05":"Zadejte celé datum vypršení platnosti","error.va.sf-cc-mth.01":"Zadejte měsíc vypršení platnosti","error.va.sf-cc-yr.01":"Zadejte rok vypršení platnosti","error.va.sf-cc-yr.02":"Zadejte celý rok vypršení platnosti","error.va.sf-cc-cvc.01":"Zadejte bezpečnostní kód","error.va.sf-cc-cvc.02":"Zadejte celý bezpečnostní kód","error.va.sf-ach-num.01":"Pole čísla bankovního účtu je prázdné","error.va.sf-ach-num.02":"Číslo bankovního účtu má nesprávnou délku","error.va.sf-ach-loc.01":"Pole bankovního směrovacího čísla je prázdné","error.va.sf-ach-loc.02":"Bankovní směrovací číslo má nesprávnou délku","error.va.sf-kcp-pwd.01":"Pole hesla je prázdné","error.va.sf-kcp-pwd.02":"Heslo má nesprávnou délku","error.giftcard.no-balance":"Na dárkové kartě je nulový zůstatek","error.giftcard.card-error":"V našich záznamech není žádná dárková karta s tímto číslem","error.giftcard.currency-error":"Dárkové karty jsou platné jenom v měně, ve které byly vystavené","amazonpay.signout":"Odhlásit se z Amazonu","amazonpay.changePaymentDetails":"Změnit údaje o platbě","partialPayment.warning":"Zvolte jiný způsob platby pro platbu zbývajících","partialPayment.remainingBalance":"Zbývající zůstatek bude %{amount}","bankTransfer.beneficiary":"Příjemce","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Odkaz","bankTransfer.introduction":"Vytvořte novou platbu bankovním převodem. K dokončení této platby můžete použít údaje na následující obrazovce.","bankTransfer.instructions":"Děkujeme za nákup. K dokončení platby použijte prosím následující informace.","bacs.accountHolderName":"Jméno držitele bankovního účtu","bacs.accountHolderName.invalid":"Neplatné jméno držitele bankovního účtu","bacs.accountNumber":"Číslo bankovního účtu","bacs.accountNumber.invalid":"Neplatné číslo bankovního účtu","bacs.bankLocationId":"Kód Sort","bacs.bankLocationId.invalid":"Neplatný kód Sort","bacs.consent.amount":"Souhlasím s tím, že mi bude následující částka odečtena z bankovního účtu.","bacs.consent.account":"Potvrzuji, že účet je veden na moje jméno a jsem jediným disponentem, jehož podpis je nutný ke schválení přímého inkasa.",edit:"Editovat","bacs.confirm":"Potvrdit a zaplatit","bacs.result.introduction":"Stáhněte si pokyny k přímému inkasu (DDI / podpisové právo)","download.pdf":"Stáhnout PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe pro číslo karty","creditCard.encryptedCardNumber.aria.label":"Číslo karty","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe pro datum vypršení platnosti","creditCard.encryptedExpiryDate.aria.label":"Konec platnosti","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe pro měsíc vypršení platnosti","creditCard.encryptedExpiryMonth.aria.label":"Měsíc konce platnosti","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe pro rok vypršení platnosti","creditCard.encryptedExpiryYear.aria.label":"Rok konce platnosti","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe pro bezpečnostní kód","creditCard.encryptedSecurityCode.aria.label":"Bezpečnostní kód","creditCard.encryptedPassword.aria.iframeTitle":"Iframe pro heslo","creditCard.encryptedPassword.aria.label":"První 2 číslice hesla karty","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe pro číslo karty","giftcard.encryptedCardNumber.aria.label":"Číslo karty","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe pro pin","giftcard.encryptedSecurityCode.aria.label":"Pin",giftcardTransactionLimit:"Maximální povolená částka za jednu transakci touto dárkovou kartou je %{amount}.","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe pro číslo bankovního účtu","ach.encryptedBankAccountNumber.aria.label":"Číslo účtu","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe pro směrovací číslo banky","ach.encryptedBankLocationId.aria.label":"Směrovací tranzitní číslo ABA","twint.saved":"uloženo",orPayWith:"nebo zaplaťte pomocí",invalidFormatExpects:"Neplatný formát. Očekávaný formát: %{format}","upi.qrCodeWaitingMessage":"Platbu dokončete naskenováním QR kódu ve své oblíbené platební UPI aplikaci","upi.vpaWaitingMessage":"Otevřete platební aplikaci UPI a potvrďte platbu","upi.modeSelection":"Jak chcete použít rozhraní UPI?","upi.completePayment":"Dokončete platbu","upi.mode.enterUpiId":"Zadejte UPI ID","upi.mode.qrCode":"QR kód","upi.mode.payByAnyUpi":"Plaťte pomocí kterékoli aplikace UPI","upi.collect.dropdown.label":"Zadejte UPI ID","upi.collect.field.label":"Zadejte UPI ID / VPA","onlineBanking.termsAndConditions":"Pokračováním souhlasíte se %#smluvními podmínkami%#","onlineBankingPL.termsAndConditions":"Pokračováním souhlasíte s %#pravidly%# a %#informační povinností%# společnosti Przelewy24.","ctp.loading.poweredByCtp":"Prováděno prostřednictvím Click to Pay","ctp.loading.intro":"Zjišťujeme, zda máte uložené karty s funkcí Click to Pay...","ctp.login.title":"Pokračujte na Click to Pay","ctp.login.subtitle":"Pro pokračování zadejte e-mailovou adresu, která je připojena k účtu Click to Pay.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Nejste to vy?","ctp.logout.notYourCards":"Nejsou to vaše karty?","ctp.logout.notYourCard":"Není to vaše karta?","ctp.logout.notYourProfile":"Není to váš profil?","ctp.otp.fieldLabel":"Jednorázový kód","ctp.otp.resendCode":"Znovu odeslat kód","ctp.otp.codeResent":"Kód odeslán","ctp.otp.title":"Získejte přístup ke svým kartám Click to Pay","ctp.otp.subtitle":"Zadejte kód %@, který jsme vám odeslali na %@ a ověřte sami sebe.","ctp.otp.saveCookiesCheckbox.label":"Příště přeskočte ověření","ctp.otp.saveCookiesCheckbox.information":"Uložte si svoje zařízení a prohlížeč pro všechny obchody používající tuto platební metodu, a plaťte tak rychleji. Nedoporučuje se pro sdílená zařízení.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Uložte si svoje údaje na tomto zařízení a prohlížeči","ctp.emptyProfile.message":"V tomto profilu Click to Pay nejsou zaregistrovány žádné karty","ctp.separatorText":"nebo použijte","ctp.cards.title":"Dokončete platbu pomocí Click to Pay","ctp.cards.subtitle":"Vyberte kartu, kterou chcete použít.","ctp.cards.expiredCard":"Platnost vypršela","ctp.manualCardEntry":"Ruční zadávání karty","ctp.aria.infoModalButton":"Co je Click to Pay","ctp.infoPopup.title":"Click to Pay přináší jednoduchost bezkontaktní platby, online","ctp.infoPopup.subtitle":"Rychlý a bezpečný způsob platby podporovaný kartami Mastercard, Visa a dalšími platebními kartami.","ctp.infoPopup.benefit1":"Služba Click to Pay používá šifrování, aby vaše informace byly zabezpečené.","ctp.infoPopup.benefit2":"Používejte ji u obchodníků po celém světě","ctp.infoPopup.benefit3":"Nastavte jednou pro bezproblémové platby v budoucnu","ctp.errors.AUTH_INVALID":"Ověřování neplatné","ctp.errors.NOT_FOUND":"Nebyl nalezen žádný účet, zadejte platný e-mail nebo pokračujte ručním zadáním karty.","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formát není podporován","ctp.errors.FRAUD":"Uživatelský účet byl uzamčen nebo vypnut","ctp.errors.CONSUMER_ID_MISSING":"V požadavku chybí identita spotřebitele","ctp.errors.ACCT_INACCESSIBLE":"Tento účet je momentálně nedostupný, např. je uzamčen","ctp.errors.CODE_INVALID":"Nesprávný ověřovací kód","ctp.errors.CODE_EXPIRED":"Platnost tohoto kódu vypršela","ctp.errors.RETRIES_EXCEEDED":"Limit pro počet opakování pro generování jednorázového hesla byl překročen","ctp.errors.OTP_SEND_FAILED":"Jednorázové heslo nebylo možné odeslat příjemci","ctp.errors.REQUEST_TIMEOUT":"Něco se pokazilo, zkuste to znovu nebo kartu zadejte ručně","ctp.errors.UNKNOWN_ERROR":"Něco se pokazilo, zkuste to znovu nebo kartu zadejte ručně","ctp.errors.SERVICE_ERROR":"Něco se pokazilo, zkuste to znovu nebo kartu zadejte ručně","ctp.errors.SERVER_ERROR":"Něco se pokazilo, zkuste to znovu nebo kartu zadejte ručně","ctp.errors.INVALID_PARAMETER":"Něco se pokazilo, zkuste to znovu nebo kartu zadejte ručně","ctp.errors.AUTH_ERROR":"Něco se pokazilo, zkuste to znovu nebo kartu zadejte ručně","paymentMethodsList.aria.label":"Zvolte způsob platby","companyDetails.name.invalid":"Zadejte název společnosti","companyDetails.registrationNumber.invalid":"Zadejte registrační číslo","consent.checkbox.invalid":"Musíte souhlasit se smluvními podmínkami","form.instruction":"Všechna pole jsou povinná, pokud není uvedeno jinak.","trustly.descriptor":"Okamžitá bankovní platba","trustly.description1":"Plaťte přímo z libovolného bankovního účtu a se zabezpečením na úrovni banky","trustly.description2":"Žádné karty, žádné stahování aplikací, žádná registrace","ancv.input.label":"Vaše identifikace ANCV","ancv.confirmPayment":"Pro potvrzení platby použijte aplikaci ANCV.","ancv.form.instruction":"K potvrzení této platby je nutná aplikace Cheque-Vacances.","ancv.beneficiaryId.invalid":"Zadejte platnou e-mailovou adresu nebo ID ANCV","payme.openPayMeApp":"Dokončete platbu autorizací v aplikaci PayMe a počkejte na potvrzení.","payme.redirectButtonLabel":"Otevřete aplikaci PayMe","payme.scanQrCode":"Dokončete platbu pomocí QR kódu","payme.timeToPay":"Tento QR kód je platný pro %@","payme.instructions.steps":"Otevřete aplikaci PayMe.%@Autorizujte platbu naskenováním QR kódu.%@Dokončete platbu v aplikaci a počkejte na potvrzení.","payme.instructions.footnote":"Nezavírejte prosím tuto stránku před dokončením platby.","payByBankAISDD.disclaimer.header":"Použijte Pay by Bank k okamžité platbě z jakéhokoli bankovního účtu.","payByBankAISDD.disclaimer.body":"Připojením svého bankovního účtu vyjadřujete souhlas s tím, aby z účtu byly strženy všechny dlužné částky za využívání našich služeb a nákup našich produktů, a to až do odvolání tohoto souhlasu.","paymentMethodBrand.other":"Ostatní"}},72724:function(e,a,t){t.r(a),t.d(a,{default:function(){return r}});var r={payButton:"Pagar","payButton.redirecting":"Redirigiendo...","payButton.with":"Pague %{value} con %{maskedData}","payButton.saveDetails":"Guardar los detalles",close:"Cerrar",storeDetails:"Recordar para mi próximo pago",readMore:"Leer más","creditCard.holderName":"Nombre en la tarjeta","creditCard.holderName.placeholder":"Juan Pérez","creditCard.holderName.invalid":"Introduzca el nombre como se muestra en la tarjeta","creditCard.numberField.title":"Número de tarjeta","creditCard.expiryDateField.title":"Fecha de expiración","creditCard.expiryDateField.placeholder":"MM/AA","creditCard.expiryDateField.month":"Mes","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"AA","creditCard.expiryDateField.year":"Año","creditCard.cvcField.title":"Código de seguridad","creditCard.storeDetailsButton":"Recordar para la próxima vez","creditCard.cvcField.placeholder.4digits":"4 dígitos","creditCard.cvcField.placeholder.3digits":"3 dígitos","creditCard.taxNumber.placeholder":"AAMMDD / 0123456789",installments:"Número de plazos",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} meses","installments.oneTime":"Pago único","installments.installments":"Pago fraccionado","installments.revolving":"Pago rotativo","sepaDirectDebit.ibanField.invalid":"Número de cuenta no válido","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"Nombre del titular de cuenta","sepa.ibanNumber":"Número de cuenta (IBAN)","error.title":"Error","error.subtitle.redirect":"Redirección fallida","error.subtitle.payment":"Pago fallido","error.subtitle.refused":"Pago rechazado","error.message.unknown":"Se ha producido un error desconocido","errorPanel.title":"Errores existentes","idealIssuer.selectField.title":"Banco","idealIssuer.selectField.placeholder":"Seleccione su banco","creditCard.success":"Pago realizado correctamente",loading:"Cargando...",continue:"Continuar",continueTo:"Continuar a","wechatpay.timetopay":"Tiene %@ para pagar","sr.wechatpay.timetopay":"Tiene %#minutos%# %#segundos%# para pagar","wechatpay.scanqrcode":"Escanear código QR",personalDetails:"Datos personales",companyDetails:"Datos de la empresa","companyDetails.name":"Nombre de la empresa","companyDetails.registrationNumber":"Número de registro",socialSecurityNumber:"Número de seguridad social",firstName:"Nombre","firstName.invalid":"Introduzca su nombre",infix:"Prefijo",lastName:"Apellidos","lastName.invalid":"Introduzca su apellido",mobileNumber:"Teléfono móvil","mobileNumber.invalid":"Número de móvil no válido",city:"Ciudad",postalCode:"Código postal","postalCode.optional":"Código postal (opcional)",countryCode:"Prefijo internacional",telephoneNumber:"Número de teléfono",dateOfBirth:"Fecha de nacimiento",shopperEmail:"Dirección de correo electrónico",gender:"Género","gender.notselected":"Seleccione su género",male:"Masculino",female:"Femenino",billingAddress:"Dirección de facturación",street:"Calle",stateOrProvince:"Provincia o estado",country:"País o región",houseNumberOrName:"Número de vivienda",separateDeliveryAddress:"Especificar otra dirección de envío",deliveryAddress:"Dirección de envío","deliveryAddress.firstName":"Nombre del destinatario","deliveryAddress.lastName":"Apellidos del destinatario",zipCode:"Código postal",apartmentSuite:"Apartamento/suite",provinceOrTerritory:"Provincia o territorio",cityTown:"Ciudad/población",address:"Dirección","address.placeholder":"Encuentre su dirección","address.errors.incomplete":"Introduzca una dirección para continuar","address.enterManually":"Introduzca la dirección manualmente",state:"Estado","field.title.optional":"(opcional)","creditCard.cvcField.title.optional":"Código de seguridad (opcional)","issuerList.wallet.placeholder":"Seleccione su monedero electrónico",privacyPolicy:"Política de privacidad","afterPay.agreement":"Sí, acepto las %@ de Riverty","riverty.termsAndConditions":"Acepto los %#términos y condiciones%# generales para el método de pago Riverty. Puede consultar la política de privacidad de Riverty %#aquí%#.",paymentConditions:"condiciones de pago",openApp:"Abrir la aplicación","voucher.readInstructions":"Leer instrucciones","voucher.introduction":"Gracias por su compra. Use el siguiente cupón para completar su pago.","voucher.expirationDate":"Fecha de caducidad","voucher.alternativeReference":"Referencia alternativa","dragonpay.voucher.non.bank.selectField.placeholder":"Seleccione su proveedor","dragonpay.voucher.bank.selectField.placeholder":"Seleccione su banco","voucher.paymentReferenceLabel":"Referencia de pago","voucher.surcharge":"Incluye recargo de %@","voucher.introduction.doku":"Gracias por su compra. Use la información siguiente para completar su pago.","voucher.shopperName":"Nombre del comprador","voucher.merchantName":"Vendedor","voucher.introduction.econtext":"Gracias por su compra. Use la información siguiente para completar su pago.","voucher.telephoneNumber":"Número de teléfono","voucher.shopperReference":"Referencia cliente","voucher.collectionInstitutionNumber":"Número de institución de cobro","voucher.econtext.telephoneNumber.invalid":"El número de teléfono debe tener 10 u 11 dígitos","boletobancario.btnLabel":"Generar Boleto","boleto.sendCopyToEmail":"Enviar copia a mi correo electrónico","button.copy":"Copiar","button.download":"Descargar","boleto.socialSecurityNumber.invalid":"Introduzca un número CPF/CNPJ válido","creditCard.storedCard.description.ariaLabel":"La tarjeta almacenada termina en %@","voucher.entity":"Entidad",donateButton:"Donar",notNowButton:"Ahora no",thanksForYourSupport:"¡Gracias por su contribución!","resultMessages.preauthorized":"Se han guardado los detalles",preauthorizeWith:"Preautorizar con",confirmPreauthorization:"Confirmar preautorización",confirmPurchase:"Confirmar compra",applyGiftcard:"Canjear",giftcardBalance:"Saldo de la tarjeta regalo",deductedBalance:"Saldo deducido","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Primeros 2 dígitos de la contraseña de la tarjeta","creditCard.encryptedPassword.invalid":"Contraseña incorrecta","creditCard.taxNumber":"Fecha de nacimiento del titular o número de registro comercial","creditCard.taxNumber.label":"Fecha de nacimiento del titular de la tarjeta (AAMMDD) o número de registro comercial (10 dígitos)","creditCard.taxNumber.labelAlt":"Número de registro comercial (10 dígitos)","creditCard.taxNumber.invalid":"Fecha de nacimiento del titular o número de registro comercial incorrectos","storedPaymentMethod.disable.button":"Eliminar","storedPaymentMethod.disable.confirmation":"Eliminar método de pago almacenado","storedPaymentMethod.disable.confirmButton":"Sí, eliminar","storedPaymentMethod.disable.cancelButton":"Cancelar","ach.bankAccount":"Cuenta bancaria","ach.accountHolderNameField.title":"Nombre del titular de la cuenta","ach.accountHolderNameField.placeholder":"Juan Pérez","ach.accountHolderNameField.invalid":"El nombre del titular de la cuenta no es válido","ach.accountNumberField.title":"Número de cuenta","ach.accountNumberField.invalid":"Número de cuenta no válido","ach.accountLocationField.title":"Número de ruta ABA","ach.accountLocationField.invalid":"El número de ruta ABA no es válido","ach.savedBankAccount":"Se ha guardado la cuenta bancaria","ach.savings":"Cuenta de ahorros","ach.checking":"Cuenta corriente","select.state":"Seleccione el estado","select.stateOrProvince":"Seleccione el estado o provincia","select.provinceOrTerritory":"Seleccione la provincia o territorio","select.country":"Seleccione el país o la región","select.noOptionsFound":"No se encontraron opciones","select.filter.placeholder":"Buscar...","telephoneNumber.invalid":"El número de teléfono no es válido",qrCodeOrApp:"o","paypal.processingPayment":"Procesando pago...",generateQRCode:"Generar código QR","await.waitForConfirmation":"Esperando confirmación","mbway.confirmPayment":"Confirme su pago en la aplicación MB WAY","shopperEmail.invalid":"La dirección de correo electrónico no es válida","dateOfBirth.format":"DD/MM/AAAA","dateOfBirth.invalid":"Introduzca una fecha de nacimiento válida que indique que tiene al menos 18 años","blik.confirmPayment":"Abra la aplicación de su banco para confirmar el pago.","blik.invalid":"Introduzca 6 dígitos","blik.code":"Código de 6 dígitos","blik.help":"Consiga el código en la aplicación de su banco.","swish.pendingMessage":"Tras escanearlo, su estado puede seguir en pendiente hasta 10 minutos. Podrían realizarse varios cargos si se intenta pagar de nuevo durante este periodo.","field.valid":"Campo válido","field.invalid":"Campo no válido","error.va.gen.01":"Campo incompleto","error.va.gen.02":"Campo no válido","error.va.sf-cc-num.01":"Introduzca un número de tarjeta válido","error.va.sf-cc-num.02":"Introduzca el número de tarjeta","error.va.sf-cc-num.03":"Introduzca una marca de tarjeta admitida","error.va.sf-cc-num.04":"Introduzca el número de tarjeta completo","error.va.sf-cc-dat.01":"Introduzca una fecha de caducidad válida","error.va.sf-cc-dat.02":"Introduzca una fecha de caducidad válida","error.va.sf-cc-dat.03":"La tarjeta de crédito está a punto de caducar","error.va.sf-cc-dat.04":"Introduzca la fecha de caducidad","error.va.sf-cc-dat.05":"Introduzca la fecha de caducidad completa","error.va.sf-cc-mth.01":"Introduzca el mes de caducidad","error.va.sf-cc-yr.01":"Introduzca el año de caducidad","error.va.sf-cc-yr.02":"Introduzca el año de caducidad completo","error.va.sf-cc-cvc.01":"Introduzca el código de seguridad","error.va.sf-cc-cvc.02":"Introduzca el código de seguridad completo","error.va.sf-ach-num.01":"El campo del número de cuenta bancaria está vacío","error.va.sf-ach-num.02":"El número de cuenta bancaria no tiene la longitud correcta","error.va.sf-ach-loc.01":"El campo del número de ruta bancaria está vacío","error.va.sf-ach-loc.02":"El número de ruta bancaria no tiene la longitud correcta","error.va.sf-kcp-pwd.01":"El campo de la contraseña está vacío","error.va.sf-kcp-pwd.02":"La contraseña no tiene la longitud correcta","error.giftcard.no-balance":"Esta tarjeta regalo no tiene saldo","error.giftcard.card-error":"No tenemos ninguna tarjeta regalo con este número en nuestros registros.","error.giftcard.currency-error":"Las tarjetas regalo solo son válidas en la moneda en que fueron emitidas","amazonpay.signout":"Salir de Amazon","amazonpay.changePaymentDetails":"Cambiar detalles de pago","partialPayment.warning":"Seleccione otro método de pago para pagar la cantidad restante","partialPayment.remainingBalance":"El saldo restante será %{amount}","bankTransfer.beneficiary":"Beneficiario","bankTransfer.reference":"referencia","bankTransfer.introduction":"Continúe para crear un nuevo pago mediante transferencia bancaria. Puede utilizar los detalles en la siguiente pantalla para finalizar este pago.","bankTransfer.instructions":"Gracias por su compra. Use la siguiente información para completar su pago.","bacs.accountHolderName":"Nombre del titular de la cuenta bancaria","bacs.accountHolderName.invalid":"El nombre del titular de la cuenta bancaria no es válido","bacs.accountNumber":"Número de cuenta bancaria","bacs.accountNumber.invalid":"El número de cuenta bancaria no es válido","bacs.bankLocationId":"Código de sucursal","bacs.bankLocationId.invalid":"El código de sucursal no es válido","bacs.consent.amount":"Estoy de acuerdo con que la cantidad anterior se deduzca de mi cuenta bancaria.","bacs.consent.account":"Confirmo que la cuenta está a mi nombre y soy el único firmante necesario para autorizar débitos directos en esta cuenta.",edit:"Editar","bacs.confirm":"Confirmar y pagar","bacs.result.introduction":"Descargue su instrucción de débito directo (IDD/mandato)","download.pdf":"Descargar PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Utilice Iframe para el número de tarjeta","creditCard.encryptedCardNumber.aria.label":"Número de tarjeta","creditCard.encryptedExpiryDate.aria.iframeTitle":"Utilice iframe para la fecha de caducidad","creditCard.encryptedExpiryDate.aria.label":"Fecha de expiración","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Utilice iframe para el mes de caducidad","creditCard.encryptedExpiryMonth.aria.label":"Mes de caducidad","creditCard.encryptedExpiryYear.aria.iframeTitle":"Utilice iframe para el año de caducidad","creditCard.encryptedExpiryYear.aria.label":"Año de caducidad","creditCard.encryptedSecurityCode.aria.iframeTitle":"Utilice iframe para el código de seguridad","creditCard.encryptedSecurityCode.aria.label":"Código de seguridad","creditCard.encryptedPassword.aria.iframeTitle":"Utilice iframe para la contraseña","creditCard.encryptedPassword.aria.label":"Primeros 2 dígitos de la contraseña de la tarjeta","giftcard.encryptedCardNumber.aria.iframeTitle":"Utilice iframe para el número de tarjeta","giftcard.encryptedCardNumber.aria.label":"Número de tarjeta","giftcard.encryptedSecurityCode.aria.iframeTitle":"Utilice iframe para el PIN","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Se permite un máximo de %{amount} por transacción en esta tarjeta regalo","ach.encryptedBankAccountNumber.aria.iframeTitle":"Utilice iframe para el número de cuenta bancaria","ach.encryptedBankAccountNumber.aria.label":"Número de cuenta","ach.encryptedBankLocationId.aria.iframeTitle":"Utilice iframe para el número de ruta bancaria","ach.encryptedBankLocationId.aria.label":"Número de ruta ABA","twint.saved":"guardado",orPayWith:"o pague con",invalidFormatExpects:"Formato no válido. Formato correcto: %{format}","upi.qrCodeWaitingMessage":"Escanee el código QR con la aplicación UPI que prefiera para completar el pago","upi.vpaWaitingMessage":"Abra la aplicación UPI para confirmar el pago","upi.modeSelection":"¿Cómo le gustaría usar la UPI?","upi.completePayment":"Complete su pago","upi.mode.enterUpiId":"Introduzca el ID de la UPI","upi.mode.qrCode":"Código QR","upi.mode.payByAnyUpi":"Pague con cualquier aplicación UPI","upi.collect.dropdown.label":"Introduzca el ID de la UPI","upi.collect.field.label":"Introduzca el ID de la UPI/VPA","onlineBanking.termsAndConditions":"Al continuar, usted acepta los %#Términos y condiciones%#","onlineBankingPL.termsAndConditions":"Al continuar, acepta las %#regulaciones%# y la %#obligación de información%# de Przelewy24","ctp.loading.poweredByCtp":"Con tecnología de Click to Pay","ctp.loading.intro":"Estamos comprobando si tiene alguna tarjeta guardada con Click to Pay...","ctp.login.title":"Continúe con Click to Pay","ctp.login.subtitle":"Introduzca la dirección de correo electrónico que tiene conectada a Click to Pay para continuar.","ctp.login.inputLabel":"Correo electrónico","ctp.logout.notYou":"¿No es usted?","ctp.logout.notYourCards":"¿No son sus tarjetas?","ctp.logout.notYourCard":"¿No es su tarjeta?","ctp.logout.notYourProfile":"¿No es su perfil?","ctp.otp.fieldLabel":"Código único","ctp.otp.resendCode":"Reenviar código","ctp.otp.codeResent":"Código reenviado","ctp.otp.title":"Acceda a sus tarjetas Click to Pay","ctp.otp.subtitle":"Introduzca el código %@ que le hemos enviado a %@ para verificar que es usted.","ctp.otp.saveCookiesCheckbox.label":"Omitir verificación la próxima vez","ctp.otp.saveCookiesCheckbox.information":"Seleccione esta opción para recordarle en su dispositivo y navegador en las tiendas participantes para agilizar el proceso de pago. No lo recomendamos para dispositivos compartidos.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Seleccione esta opción para recordarle en su dispositivo y navegador","ctp.emptyProfile.message":"No hay tarjetas registradas en este perfil de Click to Pay","ctp.separatorText":"o utilice","ctp.cards.title":"Completar el pago con Click to Pay","ctp.cards.subtitle":"Seleccione una tarjeta para usar.","ctp.cards.expiredCard":"Caducada","ctp.manualCardEntry":"Introducción manual de tarjeta","ctp.aria.infoModalButton":"Qué es Click to Pay","ctp.infoPopup.title":"Click to Pay ofrece la comodidad de la función sin contacto en internet","ctp.infoPopup.subtitle":"Un método de pago rápido y seguro compatible con Mastercard, Visa y otras tarjetas de pago.","ctp.infoPopup.benefit1":"Click to Pay utiliza cifrado para mantener su información segura y protegida","ctp.infoPopup.benefit2":"Utilícelo con vendedores de todo el mundo","ctp.infoPopup.benefit3":"Configúrela una vez para realizar pagos sin complicaciones en el futuro","ctp.errors.AUTH_INVALID":"Autenticación no válida","ctp.errors.NOT_FOUND":"No se ha encontrado ninguna cuenta, escriba un correo electrónico válido o continúe usando la escritura manual de la tarjeta","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formato no compatible","ctp.errors.FRAUD":"Se ha bloqueado o desactivado la cuenta de usuario","ctp.errors.CONSUMER_ID_MISSING":"Falta la identidad del consumidor en la solicitud","ctp.errors.ACCT_INACCESSIBLE":"Esta cuenta no está disponible en este momento, por ejemplo, está bloqueada","ctp.errors.CODE_INVALID":"Código de verificación incorrecto","ctp.errors.CODE_EXPIRED":"Este código ha caducado","ctp.errors.RETRIES_EXCEEDED":"Se ha excedido el límite de reintentos para la generación de una contraseña de un solo uso","ctp.errors.OTP_SEND_FAILED":"No se ha podido enviar la contraseña de un solo uso al destinatario","ctp.errors.REQUEST_TIMEOUT":"Se ha producido un error, inténtelo de nuevo o utilice la escritura manual de la tarjeta","ctp.errors.UNKNOWN_ERROR":"Se ha producido un error, inténtelo de nuevo o utilice la escritura manual de la tarjeta","ctp.errors.SERVICE_ERROR":"Se ha producido un error, inténtelo de nuevo o utilice la escritura manual de la tarjeta","ctp.errors.SERVER_ERROR":"Se ha producido un error, inténtelo de nuevo o utilice la escritura manual de la tarjeta","ctp.errors.INVALID_PARAMETER":"Se ha producido un error, inténtelo de nuevo o utilice la escritura manual de la tarjeta","ctp.errors.AUTH_ERROR":"Se ha producido un error, inténtelo de nuevo o utilice la escritura manual de la tarjeta","paymentMethodsList.aria.label":"Elija un método de pago","companyDetails.name.invalid":"Introduzca el nombre de la empresa","companyDetails.registrationNumber.invalid":"Introduzca el número de registro","consent.checkbox.invalid":"Debe aceptar los términos y condiciones","form.instruction":"Todos los campos son obligatorios a menos que se indique lo contrario.","trustly.descriptor":"Pago bancario instantáneo","trustly.description1":"Pague directamente desde cualquiera de sus cuentas, respaldadas por seguridad de nivel bancario","trustly.description2":"Sin tarjetas, sin descargarse ninguna aplicación, sin registrarse","ancv.input.label":"Su identificación de la ANCV","ancv.confirmPayment":"Utilice su solicitud de la ANCV para confirmar el pago.","ancv.form.instruction":"La aplicación de Cheque-Vacances es necesaria para validar este pago.","ancv.beneficiaryId.invalid":"Introduzca una dirección de correo electrónico válida o un documento de identidad de la ANCV","payme.openPayMeApp":"Completa tu pago en la aplicación PayMe autorizando el pago en la aplicación y espera por la confirmación.","payme.redirectButtonLabel":"Abrir aplicación PayMe","payme.scanQrCode":"Completa tu pago con código QR","payme.timeToPay":"Este código QR es válido para %@","payme.instructions.steps":"Abre la aplicación PayMe.%@Escanea el código QR para autorizar el pago.%@Completa el pago en la aplicación y espera por la confirmación.","payme.instructions.footnote":"No cierres esta página antes de que se complete el pago","payByBankAISDD.disclaimer.header":"Utilice la opción de pago bancario para pagar al instante desde cualquier cuenta bancaria.","payByBankAISDD.disclaimer.body":"Al conectar su cuenta bancaria, autoriza los cargos en su cuenta de cualquier importe que deba pagar por el uso de nuestros servicios o la compra de nuestros productos hasta que se revoque esta autorización.","paymentMethodBrand.other":"otro"}},75778:function(e,a,t){t.r(a),t.d(a,{default:function(){return r}});var r={payButton:"Maksa","payButton.redirecting":"Uudelleenohjataan...","payButton.with":"Maksa %{value} käyttäen maksutapaa %{maskedData}","payButton.saveDetails":"Tallenna tiedot",close:"Sulje",storeDetails:"Tallenna seuraavaa maksuani varten",readMore:"Lue lisää","creditCard.holderName":"Nimi kortilla","creditCard.holderName.placeholder":"J. Smith","creditCard.holderName.invalid":"Syötä nimi sellaisena kuin se on kortissa","creditCard.numberField.title":"Kortin numero","creditCard.numberField.placeholder":"1234 **************","creditCard.expiryDateField.title":"Voimassaolopäivämäärä","creditCard.expiryDateField.placeholder":"KK / VV","creditCard.expiryDateField.month":"Kuukausi","creditCard.expiryDateField.month.placeholder":"KK","creditCard.expiryDateField.year.placeholder":"VV","creditCard.expiryDateField.year":"Vuosi","creditCard.cvcField.title":"Turvakoodi","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Muista seuraavalla kerralla","creditCard.cvcField.placeholder.4digits":"4 lukua","creditCard.cvcField.placeholder.3digits":"3 lukua","creditCard.taxNumber.placeholder":"VVKKPP / 0123456789",installments:"Asennusten määrä",installmentOption:"% {kertaa} x% {osittainenarvo}",installmentOptionMonths:"% {kertaa} kuukautta","installments.oneTime":"Kertamaksu","installments.installments":"Erämaksu","installments.revolving":"Toistuva maksu","sepaDirectDebit.ibanField.invalid":"Väärä tilin numero","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"Haltijan nimi","sepa.ibanNumber":"Tilinumero (IBAN)","error.title":"Virhe","error.subtitle.redirect":"Uuteen kohteeseen siirto epäonnistui","error.subtitle.payment":"Maksu epännistui","error.subtitle.refused":"Maksu hylätty","error.message.unknown":"Tapahtui tuntematon virhe","errorPanel.title":"Olemassa olevat virheet","idealIssuer.selectField.title":"Pankki","idealIssuer.selectField.placeholder":"Valitse pankkisi","creditCard.success":"Maksu onnistui",loading:"Ladataan...",continue:"Jatka",continueTo:"Jatka kohteeseen","wechatpay.timetopay":"Sinulla on %@ maksettavana","sr.wechatpay.timetopay":"Maksuaikaa on jäljellä %#minuuttia%# %#sekuntia%#","wechatpay.scanqrcode":"Skannaa QR-koodi",personalDetails:"Henkilötiedot",companyDetails:"Yhtiön tiedot","companyDetails.name":"Yhtiön nimi","companyDetails.registrationNumber":"Rekisterinumero",socialSecurityNumber:"Sosiaaliturvatunnus",firstName:"Etunimi","firstName.invalid":"Syötä etunimesi",infix:"Etuliite",lastName:"Sukunimi","lastName.invalid":"Syötä sukunimesi",mobileNumber:"Matkapuhelinnumero","mobileNumber.invalid":"Ei-kelvollinen matkapuhelinnumero",city:"Kaupunki",postalCode:"Postinumero","postalCode.optional":"Postinumero (valinnainen)",countryCode:"Maakoodi",telephoneNumber:"Puhelinnumero",dateOfBirth:"Syntymäaika",shopperEmail:"Sähköpostiosoite",gender:"Sukupuoli","gender.notselected":"Valitse sukupuoli",male:"Mies",female:"Nainen",billingAddress:"Laskutusosoite",street:"Katu",stateOrProvince:"Osavaltio tai lääni",country:"Maa/alue",houseNumberOrName:"Talon numero",separateDeliveryAddress:"Määritä erillinen toimitusosoite",deliveryAddress:"Toimitusosoite","deliveryAddress.firstName":"Vastaanottajan etunimi","deliveryAddress.lastName":"Vastaanottajan sukunimi",zipCode:"Postinumero",apartmentSuite:"Huoneisto / sviitti",provinceOrTerritory:"Maakunta tai alue",cityTown:"Kaupunki / taajama",address:"Osoite","address.placeholder":"Löydä osoitteesi","address.errors.incomplete":"Syötä osoite jatkaaksesi","address.enterManually":"Syötä osoite manuaalisesti",state:"Osavaltio","field.title.optional":"(valinnainen)","creditCard.cvcField.title.optional":"Turvakoodi (valinnainen)","issuerList.wallet.placeholder":"Valitse lompakkosi",privacyPolicy:"Tietosuojamenettely","afterPay.agreement":"Hyväksyn Rivertyn %@","riverty.termsAndConditions":"Hyväksyn Riverty-maksutavan yleiset %#ehdot%#. Rivertyn tietosuojakäytäntö löytyy %#täältä%#.",paymentConditions:"maksuehdot",openApp:"Avaa sovellus","voucher.readInstructions":"Lue ohjeet","voucher.introduction":"Kiitos hankinnastasi, käytä seuraavaa kuponkia viedäksesi maksusi päätökseen.","voucher.expirationDate":"Vanhenemispäivämäärä","voucher.alternativeReference":"Vaihtoehtoinen viite","dragonpay.voucher.non.bank.selectField.placeholder":"Valitse toimittajasi","dragonpay.voucher.bank.selectField.placeholder":"Valitse pankkisi","voucher.paymentReferenceLabel":"Maksun viite","voucher.surcharge":"Sis. %@ lisämaksun","voucher.introduction.doku":"Kiitos hankinnastasi, käytä seuraavia tietoja päättääksesi maksusi.","voucher.shopperName":"Ostajan nimi","voucher.merchantName":"Kauppias","voucher.introduction.econtext":"Kiitos hankinnastasi, käytä seuraavia tietoja päättääksesi maksusi.","voucher.telephoneNumber":"Puhelinnumero","voucher.shopperReference":"Ostajan viite","voucher.collectionInstitutionNumber":"Keräävän laitoksen numero","voucher.econtext.telephoneNumber.invalid":"Puhelinnumeron on oltava 10 tai 11 numeroa pitkä","boletobancario.btnLabel":"Luo Boleto","boleto.sendCopyToEmail":"Lähetä kopio sähköpostiini","button.copy":"Kopio","button.download":"Lataa","boleto.socialSecurityNumber.invalid":"Anna kelvollinen CPF- tai CNPJ-numero.","creditCard.storedCard.description.ariaLabel":"Tallennetun kortin viimeiset luvut ovat %@","voucher.entity":"Kokonaisuus",donateButton:"Lahjoita",notNowButton:"Ei nyt",thanksForYourSupport:"Kiitos tuestasi!","resultMessages.preauthorized":"Tiedot tallennettu",preauthorizeWith:"Ennkkolupa käyttäjän kanssa",confirmPreauthorization:"Vahvista ennakkolupa",confirmPurchase:"Vahvista hankinta",applyGiftcard:"Lunasta",giftcardBalance:"Lahjakortin saldo",deductedBalance:"Vähennetty saldo","creditCard.pin.title":"Pin-tunnus","creditCard.encryptedPassword.label":"Kortin salasanan ensimmäiset 2 lukua","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Kelpaamaton salasana","creditCard.taxNumber":"Kortinhaltijan syntymäpäivä (VVKKPP) tai yrityksen rekisterinumero","creditCard.taxNumber.label":"Kortinhaltijan syntymäpäivä (VVKKPP) tai yrityksen rekisterinumero (10 lukua)","creditCard.taxNumber.labelAlt":"Yrityksen rekisterinumero (10 lukua)","creditCard.taxNumber.invalid":"Kelpaamaton kortinhaltijan syntymäpäivä (VVKKPP) tai yrityksen rekisterinumero","storedPaymentMethod.disable.button":"Poista","storedPaymentMethod.disable.confirmation":"Poista tallennettu maksutapa","storedPaymentMethod.disable.confirmButton":"Kyllä, poista","storedPaymentMethod.disable.cancelButton":"Peruuta","ach.bankAccount":"Pankkitili","ach.accountHolderNameField.title":"Tilinhaltijan nimi","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Ei-kelvollinen tilinhaltijan nimi","ach.accountNumberField.title":"Tilinumero","ach.accountNumberField.invalid":"Väärä tilin numero","ach.accountLocationField.title":"ABA-reititysnumero","ach.accountLocationField.invalid":"Ei-kelvollinen ABA-reititysnumero","ach.savedBankAccount":"Tallennettu pankkitili","ach.savings":"Säästötili","ach.checking":"Käyttötili","select.state":"Valitse osavaltio","select.stateOrProvince":"Valitse osavaltio tai lääni","select.provinceOrTerritory":"Valitse maakunta tai alue","select.country":"Valitse maa/alue","select.noOptionsFound":"Vaihtoehtoja ei löytynyt","select.filter.placeholder":"Hae...","telephoneNumber.invalid":"Ei-kelvollinen puhelinnumero",qrCodeOrApp:"tai","paypal.processingPayment":"Maksua käsitellään...",generateQRCode:"Tuota QR-koodi","await.waitForConfirmation":"Odottaa vahvistusta","mbway.confirmPayment":"Vahvista maksusi MB WAY -sovelluksella","shopperEmail.invalid":"Ei-kelvollinen sähköpostiosoite","dateOfBirth.format":"PP/KK/VVVV","dateOfBirth.invalid":"Anna kelvollinen syntymäaika, joka osoittaa, että olet vähintään 18-vuotias","blik.confirmPayment":"Avaa pankkisovelluksesi vahvistaaksesi maksun.","blik.invalid":"Syötä 6 lukua","blik.code":"6-numeroinen koodi","blik.help":"Hanki koodi pankkisovelluksestasi.","swish.pendingMessage":"Skannattuasi, tila voi odottaa jopa 10 minuuttia. Yritys maksaa uudelleen tässä ajassa voi tuottaa moninkertaisia maksuja.","field.valid":"Kenttä kelvollinen","field.invalid":"Kenttä ei kelpaa","error.va.gen.01":"Täydentämätön kenttä","error.va.gen.02":"Kenttä ei kelpaa","error.va.sf-cc-num.01":"Syötä kelvollinen kortin numero","error.va.sf-cc-num.02":"Syötä kortin numero","error.va.sf-cc-num.03":"Syötä tuettu korttimerkki","error.va.sf-cc-num.04":"Syötä kortin numero kokonaan","error.va.sf-cc-dat.01":"Syötä kelvollinen viimeinen voimassaolopäivä","error.va.sf-cc-dat.02":"Syötä kelvollinen viimeinen voimassaolopäivä","error.va.sf-cc-dat.03":"Luottokortin voimassaolo päättyy pian","error.va.sf-cc-dat.04":"Syötä viimeinen voimassaolopäivä","error.va.sf-cc-dat.05":"Syötä viimeinen voimassaolopäivä kokonaan","error.va.sf-cc-mth.01":"Syötä viimeinen voimassaolokuukausi","error.va.sf-cc-yr.01":"Syötä viimeinen voimassaolovuosi","error.va.sf-cc-yr.02":"Syötä viimeinen voimassaolovuosi kokonaan","error.va.sf-cc-cvc.01":"Syötä turvakoodi","error.va.sf-cc-cvc.02":"Syötä turvakoodi kokonaan","error.va.sf-ach-num.01":"Pankkitilin numero -kenttä on tyhjä","error.va.sf-ach-num.02":"Pankkitilin numero on väärän pituinen","error.va.sf-ach-loc.01":"Pankin reititysnumeron kenttä on tyhjä","error.va.sf-ach-loc.02":"Pankin reititysnumero on väärän pituinen","error.va.sf-kcp-pwd.01":"Salasanakenttä on tyhjä","error.va.sf-kcp-pwd.02":"Salasana on väärän pituinen","error.giftcard.no-balance":"Lahjakortin saldo on nolla","error.giftcard.card-error":"Asiakirjoissamme ei ole tämän numeron lahjakorttia","error.giftcard.currency-error":"Gift cards are only valid in the currency they were issued in","amazonpay.signout":"Kirjaudu ulos Amazonista","amazonpay.changePaymentDetails":"Muuta maksutietoja","partialPayment.warning":"Valitse toinen maksutapa jäännöksen maksamiseksi","partialPayment.remainingBalance":"Jäljellä oleva saldo on %{summa}","bankTransfer.beneficiary":"Edunsaaja","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Viite","bankTransfer.introduction":"Jatka uuden pankkisiirtomaksun luomista. Voit viimeistellä tämän maksun seuraavan näytön tietojen avulla.","bankTransfer.instructions":"Kiitos hankinnastasi, käytä seuraavia tietoja päättääksesi maksusi.","bacs.accountHolderName":"Tilinhaltijan nimi","bacs.accountHolderName.invalid":"Ei-kelvollinen tilinhaltijan nimi","bacs.accountNumber":"Pankkitilinumero","bacs.accountNumber.invalid":"Väärä tilin numero","bacs.bankLocationId":"Lajittelukoodi","bacs.bankLocationId.invalid":"Ei-kelvollinen lajittelukoodi","bacs.consent.amount":"Hyväksyn, että alla oleva summa veloitetaan pankkitililtäni.","bacs.consent.account":"Vahvistan, että tili on nimessäni ja olen ainoa allekirjoittaja, joka vaaditaan valtuuttamaan suoraveloitus tällä tilillä.",edit:"Muokkaa","bacs.confirm":"Vahvista ja maksa","bacs.result.introduction":"Lataa suoraveloitusohjeet (DDI / Mandate)","download.pdf":"Lataa PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe kortin numerolle","creditCard.encryptedCardNumber.aria.label":"Kortin numero","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe viimeiselle voimassaolopäivälle","creditCard.encryptedExpiryDate.aria.label":"Voimassaolopäivämäärä","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe viimeiselle voimassaolokuukaudelle","creditCard.encryptedExpiryMonth.aria.label":"Vanhenemiskuukausi","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe viimeiselle voimassaolovuodelle","creditCard.encryptedExpiryYear.aria.label":"Vanhenemisvuosi","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe turvakoodille","creditCard.encryptedSecurityCode.aria.label":"Turvakoodi","creditCard.encryptedPassword.aria.iframeTitle":"Iframe salasanalle","creditCard.encryptedPassword.aria.label":"Kortin salasanan ensimmäiset 2 lukua","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe kortin numerolle","giftcard.encryptedCardNumber.aria.label":"Kortin numero","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe pin-koodille","giftcard.encryptedSecurityCode.aria.label":"Pin-tunnus",giftcardTransactionLimit:"Maks. % {summa} sallittu tapahtumaa kohti tällä lahjakortilla","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe pankkitilinumeroa varten","ach.encryptedBankAccountNumber.aria.label":"Tilinumero","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe pankin reititysnumeroa varten","ach.encryptedBankLocationId.aria.label":"ABA-reititysnumero","twint.saved":"tallennettu",orPayWith:"tai maksa",invalidFormatExpects:"Ei-kelvollinen muoto. Odotettu muoto: %{format}","upi.qrCodeWaitingMessage":"Suorita maksu loppuun skannaamalla QR-koodi haluamallasi UPI-sovelluksella","upi.vpaWaitingMessage":"Avaa UPI-sovellus vahvistaaksesi maksun","upi.modeSelection":"Miten haluaisit käyttää UPI:a?","upi.completePayment":"Suorita maksusi loppuun","upi.mode.enterUpiId":"Syötä UPI-tunnus","upi.mode.qrCode":"QR-koodi","upi.mode.payByAnyUpi":"Maksa millä tahansa UPI-sovelluksella","upi.collect.dropdown.label":"Syötä UPI-tunnus","upi.collect.field.label":"Syötä UPI-tunnus/VPA","onlineBanking.termsAndConditions":"Jatkamalla hyväksyt %#käyttöehdot%#","onlineBankingPL.termsAndConditions":"Jatkamalla hyväksyt Przelewy24:n %#säännöt%# ja %#tiedonantovelvollisuuden%#","ctp.loading.poweredByCtp":"Palvelun tarjoaa Click to Pay","ctp.loading.intro":"Tarkistamme, onko sinulla tallennettuja Click to Pay -kortteja...","ctp.login.title":"Jatka Click to Pay -palveluun","ctp.login.subtitle":"Jatka antamalla Click to Pay -palveluun liitetty sähköpostiosoite.","ctp.login.inputLabel":"Sähköposti","ctp.logout.notYou":"Etkö tämä ole sinä?","ctp.logout.notYourCards":"Eikö tämä ole korttisi?","ctp.logout.notYourCard":"Eikö tämä ole korttisi?","ctp.logout.notYourProfile":"Eikö tämä ole profiilisi?","ctp.otp.fieldLabel":"Kertakoodi","ctp.otp.resendCode":"Lähetä koodi uudelleen","ctp.otp.codeResent":"Koodi lähetetty uudelleen","ctp.otp.title":"Hanki pääsy Click to Pay -kortteihisi","ctp.otp.subtitle":"Syötä koodi, jonka %@ lähetti osoitteeseen %@ vahvistaaksesi, että kyseessä olet sinä.","ctp.otp.saveCookiesCheckbox.label":"Ohita vahvistus seuraavalla kerralla","ctp.otp.saveCookiesCheckbox.information":"Valitse, että sinut muistetaan laitteellasi ja selaimessa osallistuvissa myymälöissä, jotta voit maksaa nopeammin. Ei suositella jaetuilla laitteilla.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Valitse, jotta sinut muistetaan laitteellasi ja selaimessa","ctp.emptyProfile.message":"Tähän Click to Pay -profiiliin ei ole rekisteröity kortteja","ctp.separatorText":"tai käytä","ctp.cards.title":"Suorita maksu Click to Paylla","ctp.cards.subtitle":"Valitse käytettävä kortti.","ctp.cards.expiredCard":"Vanhentunut","ctp.manualCardEntry":"Manuaalinen kortin syöttö","ctp.aria.infoModalButton":"Mikä on Click to Pay","ctp.infoPopup.title":"Click to Pay tuo lähimaksujen helppouden verkkoon","ctp.infoPopup.subtitle":"Nopea ja turvallinen maksutapa, jota tukevat Mastercard, Visa ja muut maksukortit.","ctp.infoPopup.benefit1":"Click to Pay käyttää salausta tietojesi turvaamiseen","ctp.infoPopup.benefit2":"Käytä sitä kauppiailla kaikkialla maailmassa","ctp.infoPopup.benefit3":"Määritä kerran, jotta voit maksaa vaivattomasti jatkossa","ctp.errors.AUTH_INVALID":"Virheellinen todennus","ctp.errors.NOT_FOUND":"Tiliä ei löytynyt. Anna kelvollinen sähköpostiosoite tai jatka manuaalista kortin syöttöä","ctp.errors.ID_FORMAT_UNSUPPORTED":"Muoto ei tuettu","ctp.errors.FRAUD":"Käyttäjätili oli lukittu tai poistettu käytöstä","ctp.errors.CONSUMER_ID_MISSING":"Pyynnöstä puuttuu kuluttajan henkilöllisyys","ctp.errors.ACCT_INACCESSIBLE":"Tämä tili ei ole tällä hetkellä käytettävissä, se voi esimerkiksi olla lukittu","ctp.errors.CODE_INVALID":"Virheellinen vahvistuskoodi","ctp.errors.CODE_EXPIRED":"Tämä koodi on vanhentunut","ctp.errors.RETRIES_EXCEEDED":"Kertakäyttöisen salasanan luomisen uusintayritysten määrä ylittyi","ctp.errors.OTP_SEND_FAILED":"Kertakäyttöistä salasanaa ei voitu lähettää vastaanottajalle","ctp.errors.REQUEST_TIMEOUT":"Jokin meni vikaan. Yritä uudelleen tai käytä manuaalista kortin syöttöä","ctp.errors.UNKNOWN_ERROR":"Jokin meni vikaan. Yritä uudelleen tai käytä manuaalista kortin syöttöä","ctp.errors.SERVICE_ERROR":"Jokin meni vikaan. Yritä uudelleen tai käytä manuaalista kortin syöttöä","ctp.errors.SERVER_ERROR":"Jokin meni vikaan. Yritä uudelleen tai käytä manuaalista kortin syöttöä","ctp.errors.INVALID_PARAMETER":"Jokin meni vikaan. Yritä uudelleen tai käytä manuaalista kortin syöttöä","ctp.errors.AUTH_ERROR":"Jokin meni vikaan. Yritä uudelleen tai käytä manuaalista kortin syöttöä","paymentMethodsList.aria.label":"Valitse maksutapa","companyDetails.name.invalid":"Syötä yrityksen nimi","companyDetails.registrationNumber.invalid":"Syötä rekisterinumero","consent.checkbox.invalid":"Sinun on hyväksyttävä käyttöehdot","form.instruction":"Kaikki kentät ovat pakollisia, ellei toisin ole merkitty.","trustly.descriptor":"Välitön pankkimaksu","trustly.description1":"Maksa suoraan miltä tahansa pankkitililtäsi pankkitason suojauksella","trustly.description2":"Ei kortteja, ei sovellusten lataamista, ei rekisteröintiä","ancv.input.label":"ANCV-tunnuksesi","ancv.confirmPayment":"Vahvista maksusi ANCV-sovelluksella.","ancv.form.instruction":"Tämän maksun vahvistaminen edellyttää Cheque-Vacances -sovelluksen.","ancv.beneficiaryId.invalid":"Anna kelvollinen sähköpostiosoite tai ANCV-tunnus","payme.openPayMeApp":"Viimeistele maksu PayMe-sovelluksessa hyväksymällä maksu sovelluksessa, ja odota vahvistusta.","payme.redirectButtonLabel":"Avaa PayMe-sovellus","payme.scanQrCode":"Viimeistele maksusi QR-koodilla","payme.timeToPay":"Tämä QR-koodi on voimassa %@","payme.instructions.steps":"Avaa PayMe-sovellus.%@Hyväksy maksu skannaamalla QR-koodi.%@Viimeistele maksu sovelluksessa, ja odota vahvistusta.","payme.instructions.footnote":"Älä sulje tätä sivua ennen kuin maksu on suoritettu","payByBankAISDD.disclaimer.header":"Pankkimaksutoiminnolla voit maksaa heti miltä tahansa pankkitililtä.","payByBankAISDD.disclaimer.body":"Kun yhdistät pankkitilisi, annat luvan veloittaa tililtäsi kaikki palveluidemme käytöstä ja/tai tuotteidemme ostamisesta aiheutuvat kulut. Tämä valtuutus on voimassa siihen saakka, kunnes peruutat sen.","paymentMethodBrand.other":"muu"}},84224:function(e,a,t){t.r(a),t.d(a,{default:function(){return r}});var r={payButton:"Betal","payButton.redirecting":"Omdirigerer...","payButton.with":"Betal %{value} med %{maskedData}","payButton.saveDetails":"Gem oplysninger",close:"Luk",storeDetails:"Gem til min næste betaling",readMore:"Læs mere","creditCard.holderName":"Navn på kort","creditCard.holderName.placeholder":"J. Hansen","creditCard.holderName.invalid":"Indtast navn som vist på kortet","creditCard.numberField.title":"Kortnummer","creditCard.numberField.placeholder":"1234 **************","creditCard.expiryDateField.title":"Udløbsdato","creditCard.expiryDateField.placeholder":"MM/ÅÅ","creditCard.expiryDateField.month":"Måned","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"ÅÅ","creditCard.expiryDateField.year":"År","creditCard.cvcField.title":"Sikkerhedskode","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Husk til næste gang","creditCard.cvcField.placeholder.4digits":"4 cifre","creditCard.cvcField.placeholder.3digits":"3 cifre","creditCard.taxNumber.placeholder":"ÅÅMMDD/0123456789",installments:"Antal rater",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} måneder","installments.oneTime":"Engangsbetaling","installments.installments":"Afdragsbetaling","installments.revolving":"Løbende betaling","sepaDirectDebit.ibanField.invalid":"Ugyldigt kontonummer","sepaDirectDebit.nameField.placeholder":"J. Smith","sepa.ownerName":"Kontohavernavn","sepa.ibanNumber":"Kontonummer (IBAN)","error.title":"Fejl","error.subtitle.redirect":"Omdirigering fejlede","error.subtitle.payment":"Betaling fejlede","error.subtitle.refused":"Betaling afvist","error.message.unknown":"Der opstod en ukendt fejl","errorPanel.title":"Eksisterende fejl","idealIssuer.selectField.title":"Bank","idealIssuer.selectField.placeholder":"Vælg din bank","creditCard.success":"Betaling gennemført",loading:"Indlæser…",continue:"Fortsæt",continueTo:"Fortsæt til","wechatpay.timetopay":"Du har %@ at betale","sr.wechatpay.timetopay":"Du har %#minutes%# %#seconds%# til at betale","wechatpay.scanqrcode":"Scan QR-kode",personalDetails:"Personlige oplysninger",companyDetails:"Virksomhedsoplysninger","companyDetails.name":"Virksomhedsnavn","companyDetails.registrationNumber":"CVR-nummer",socialSecurityNumber:"CPR-nummer",firstName:"Fornavn","firstName.invalid":"Indtast dit fornavn",infix:"Præfiks",lastName:"Efternavn","lastName.invalid":"Indtast dit efternavn",mobileNumber:"Mobilnummer","mobileNumber.invalid":"Ugyldigt mobilnummer",city:"By",postalCode:"Postnummer","postalCode.optional":"Postnummer (valgfrit)",countryCode:"Landekode",telephoneNumber:"Telefonnummer",dateOfBirth:"Fødselsdato",shopperEmail:"E-mailadresse",gender:"Køn","gender.notselected":"Vælg dit køn",male:"Mand",female:"Kvinde",billingAddress:"Faktureringsadresse",street:"Gade",stateOrProvince:"Region eller kommune",country:"Land/region",houseNumberOrName:"Husnummer",separateDeliveryAddress:"Angiv en separat leveringsadresse",deliveryAddress:"Leveringsadresse","deliveryAddress.firstName":"Modtagers fornavn","deliveryAddress.lastName":"Modtagers efternavn",zipCode:"Postnummer",apartmentSuite:"Lejlighed/suite",provinceOrTerritory:"Provins eller territorium",cityTown:"By",address:"Adresse","address.placeholder":"Find din adresse","address.errors.incomplete":"Indtast en adresse for at fortsætte","address.enterManually":"Indtast adresse manuelt",state:"Stat","field.title.optional":"(valgfrit)","creditCard.cvcField.title.optional":"Sikkerhedskode (valgfrit)","issuerList.wallet.placeholder":"Vælg tegnebog",privacyPolicy:"Politik om privatlivets fred","afterPay.agreement":"Jeg accepterer %@ fra Riverty","riverty.termsAndConditions":"Jeg accepterer de generelle %#vilkår og betingelser%# for Riverty-betalingsmåden. Rivertys privatlivspolitik kan findes %#her%#.",paymentConditions:"betalingsbetingelser",openApp:"Åbn appen","voucher.readInstructions":"Læs anvisningerne","voucher.introduction":"Tak for dit køb. Brug følgende kupon til at gennemføre din betaling.","voucher.expirationDate":"Udløbsdato","voucher.alternativeReference":"Alternativ reference","dragonpay.voucher.non.bank.selectField.placeholder":"Vælg din udbyder","dragonpay.voucher.bank.selectField.placeholder":"Vælg din bank","voucher.paymentReferenceLabel":"Betalingsreference","voucher.surcharge":"Inkl. tillægsbegyr på %@","voucher.introduction.doku":"Tak for dit køb. Brug følgende oplysninger til at gennemføre din betaling.","voucher.shopperName":"Kundenavn","voucher.merchantName":"Sælger","voucher.introduction.econtext":"Tak for dit køb. Brug følgende oplysninger til at gennemføre din betaling.","voucher.telephoneNumber":"Telefonnummer","voucher.shopperReference":"Købers reference","voucher.collectionInstitutionNumber":"Id-nummer til opkrævningsinstitution","voucher.econtext.telephoneNumber.invalid":"Telefonnummer skal bestå af 10 eller 11 cifre","boletobancario.btnLabel":"Generér Boleto","boleto.sendCopyToEmail":"Send en kopi til min e-mail","button.copy":"Kopiér","button.download":"Download","boleto.socialSecurityNumber":"CPF / CNPJ","boleto.socialSecurityNumber.invalid":"Indtast et gyldigt CPF/CNPJ-nummer","creditCard.storedCard.description.ariaLabel":"Gemt kort ender på %@","voucher.entity":"Enhed",donateButton:"Giv et bidrag",notNowButton:"Ikke nu",thanksForYourSupport:"Tak for din støtte!","resultMessages.preauthorized":"Oplysningerne er gemt",preauthorizeWith:"Forhåndsgodkend med",confirmPreauthorization:"Bekræft forhåndsgodkendelse",confirmPurchase:"Bekræft køb",applyGiftcard:"Indløs",giftcardBalance:"Saldo på gavekort",deductedBalance:"Fratrukket saldo","creditCard.pin.title":"Pinkode","creditCard.encryptedPassword.label":"Første 2 cifre i adgangskode til kort","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Ugyldig adgangskode","creditCard.taxNumber":"Kortholders fødselsdag eller virksomhedsregistreringsnummer","creditCard.taxNumber.label":"Kortholders fødselsdato (ÅÅMMDD) eller virksomheds registreringsnummer (10 cifre)","creditCard.taxNumber.labelAlt":"Virksomheds registreringsnummer (10 cifre)","creditCard.taxNumber.invalid":"Ugyldig fødselsdato for kortholder eller virksomheds registreringsnummer","storedPaymentMethod.disable.button":"Fjern","storedPaymentMethod.disable.confirmation":"Fjern gemt betalingsmåde","storedPaymentMethod.disable.confirmButton":"Ja, fjern","storedPaymentMethod.disable.cancelButton":"Annuller","ach.bankAccount":"Bankkonto","ach.accountHolderNameField.title":"Kontohavers navn","ach.accountHolderNameField.placeholder":"J. Smith","ach.accountHolderNameField.invalid":"Ugyldigt kontohavernavn","ach.accountNumberField.title":"Kontonummer","ach.accountNumberField.invalid":"Ugyldigt kontonummer","ach.accountLocationField.title":"ABA-registreringsnummer","ach.accountLocationField.invalid":"Ugyldigt ABA-registreringsnummer","ach.savedBankAccount":"Gemt bankkonto","ach.savings":"Opsparingskonto","ach.checking":"Lønkonto","select.state":"Vælg stat","select.stateOrProvince":"Vælg region eller kommune","select.provinceOrTerritory":"Vælg provins eller territorium","select.country":"Vælg land/region","select.noOptionsFound":"Ingen muligheder fundet","select.filter.placeholder":"Søg...","telephoneNumber.invalid":"Ugyldigt telefonnummer",qrCodeOrApp:"eller","paypal.processingPayment":"Behandler betaling...",generateQRCode:"Generér QR-kode","await.waitForConfirmation":"Venter på bekræftelse","mbway.confirmPayment":"Bekræft din betaling på appen MB WAY","shopperEmail.invalid":"Ugyldig e-mailadresse","dateOfBirth.format":"DD/MM/ÅÅÅÅ","dateOfBirth.invalid":"Indtast en gyldig fødselsdato, der viser, at du er mindst 18 år gammel","blik.confirmPayment":"Åbn din bankapp for at bekræfte betalingen.","blik.invalid":"Indtast 6 tal","blik.code":"6-cifret kode","blik.help":"Få koden fra din bankapp.","swish.pendingMessage":"Visning af status kan tage op til 10 minutter efter scanning. Et nyt forsøg på betaling inden for dette tidsrum kan muligvis medføre flere gebyrer.","field.valid":"Gyldigt felt","field.invalid":"Ugyldigt felt","error.va.gen.01":"Felt ikke udfyldt","error.va.gen.02":"Ugyldigt felt","error.va.sf-cc-num.01":"Indtast et gyldigt kortnummer","error.va.sf-cc-num.02":"Indtast kortnummeret","error.va.sf-cc-num.03":"Indtast et understøttet kort","error.va.sf-cc-num.04":"Indtast det fulde kortnummer","error.va.sf-cc-dat.01":"Indtast en gyldig udløbsdato","error.va.sf-cc-dat.02":"Indtast en gyldig udløbsdato","error.va.sf-cc-dat.03":"Kreditkort udløber snart","error.va.sf-cc-dat.04":"Indtast udløbsdatoen","error.va.sf-cc-dat.05":"Indtast den fulde udløbsdato","error.va.sf-cc-mth.01":"Indtast udløbsmåneden","error.va.sf-cc-yr.01":"Indtast udløbsåret","error.va.sf-cc-yr.02":"Indtast det fulde udløbsår","error.va.sf-cc-cvc.01":"Indtast sikkerhedskoden","error.va.sf-cc-cvc.02":"Indtast den fulde sikkerhedskode","error.va.sf-ach-num.01":"Feltet til bankkontonummer er tomt","error.va.sf-ach-num.02":"Bankkontonummer har den forkerte længde","error.va.sf-ach-loc.01":"Feltet til registreringsnummeret er tomt","error.va.sf-ach-loc.02":"Registreringsnummeret har den forkerte længde","error.va.sf-kcp-pwd.01":"Feltet til adgangskode er tomt","error.va.sf-kcp-pwd.02":"Adgangskoden har den forkerte længde","error.giftcard.no-balance":"Saldoen på gavekortet er 0","error.giftcard.card-error":"Vi har ikke et gavekort med dette nummer i vores optegnelser","error.giftcard.currency-error":"Gavekort er kun gyldige i udstedelsesvalutaen","amazonpay.signout":"Log ud af Amazon","amazonpay.changePaymentDetails":"Skift betalingsoplysninger","partialPayment.warning":"Vælg en anden betalingsmåde til betaling af restbeløbet","partialPayment.remainingBalance":"Restbeløbet vil være %{amount}","bankTransfer.beneficiary":"Betalingsmodtager","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Reference","bankTransfer.introduction":"Fortsæt med at oprette ny betalingsoverførsel via bank. Du kan bruge oplysningerne på den følgende skærm til at afslutte betalingen.","bankTransfer.instructions":"Tak for dit køb. Brug følgende oplysninger til at gennemføre din betaling.","bacs.accountHolderName":"Bankkontohavers navn","bacs.accountHolderName.invalid":"Ugyldigt navn på bankkontohaver","bacs.accountNumber":"Bankkontonummer","bacs.accountNumber.invalid":"Ugyldigt bankkontonummer","bacs.bankLocationId":"Registreringsnummer","bacs.bankLocationId.invalid":"Ugyldigt registreringsnummer","bacs.consent.amount":"Jeg accepterer, at beløbet ovenfor trækkes på min bankkonto.","bacs.consent.account":"Jeg bekræfter, at kontoen er i mit navn, og at jeg er den eneste underskriver, der kræves for at godkende direkte debitering af kontoen.",edit:"Rediger","bacs.confirm":"Bekræft, og betal","bacs.result.introduction":"Download vejledningen til direkte debitering (fuldmagt til direkte debitering)","download.pdf":"Download PDF","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe til kortnummer","creditCard.encryptedCardNumber.aria.label":"Kortnummer","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe til udløbsdato","creditCard.encryptedExpiryDate.aria.label":"Udløbsdato","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe til udløbsmåned","creditCard.encryptedExpiryMonth.aria.label":"Udløbsmåned","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe til udløbsår","creditCard.encryptedExpiryYear.aria.label":"Udløbsår","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe til sikkerhedskode","creditCard.encryptedSecurityCode.aria.label":"Sikkerhedskode","creditCard.encryptedPassword.aria.iframeTitle":"Iframe til adgangskode","creditCard.encryptedPassword.aria.label":"Første 2 cifre i adgangskode til kort","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe til kortnummer","giftcard.encryptedCardNumber.aria.label":"Kortnummer","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe til pin","giftcard.encryptedSecurityCode.aria.label":"Pinkode",giftcardTransactionLimit:"Maks. %{amount} tilladt pr. transaktion på dette gavekort","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe til bankkontonummer","ach.encryptedBankAccountNumber.aria.label":"Kontonummer","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe til bankroutingnummer","ach.encryptedBankLocationId.aria.label":"ABA-registreringsnummer","twint.saved":"gemt",orPayWith:"eller betal med",invalidFormatExpects:"Ugyldigt format. Forventet format: %{format}","upi.qrCodeWaitingMessage":"Scan QR-koden via din foretrukne UPI-app for at gennemføre betalingen","upi.vpaWaitingMessage":"Åbn din UPI-app for at bekræfte betalingen","upi.modeSelection":"Hvordan vil du bruge UPI?","upi.completePayment":"Gennemfør din betaling","upi.mode.enterUpiId":"Indtast UPI-id","upi.mode.qrCode":"QR-kode","upi.mode.payByAnyUpi":"Betal med enhver UPI-app","upi.collect.dropdown.label":"Indtast UPI-id","upi.collect.field.label":"Indtast UPI-id/VPA","onlineBanking.termsAndConditions":"Ved at fortsætte accepterer du %#vilkår og betingelser%#","onlineBankingPL.termsAndConditions":"Hvis du fortsætter, accepterer du %#regulativer%# og %#informationspligten%# for Przelewy24","ctp.loading.poweredByCtp":"Drevet af Click to Pay","ctp.loading.intro":"Vi tjekker, om du har gemte kort med Click to Pay...","ctp.login.title":"Fortsæt med Click to Pay","ctp.login.subtitle":"Indtast den e-mailadresse, der er knyttet til Click to Pay, for at fortsætte.","ctp.login.inputLabel":"E-mail","ctp.logout.notYou":"Ikke dig?","ctp.logout.notYourCards":"Ikke dine kort?","ctp.logout.notYourCard":"Ikke dit kort?","ctp.logout.notYourProfile":"Ikke din profil?","ctp.otp.fieldLabel":"Engangskode","ctp.otp.resendCode":"Send kode igen","ctp.otp.codeResent":"Kode er sendt igen","ctp.otp.title":"Få adgang til dine Click to Pay-kort","ctp.otp.subtitle":"Indtast den kode, vi har sendt til %@ for at bekræfte, at det er dig.","ctp.otp.saveCookiesCheckbox.label":"Spring bekræftelse over næste gang","ctp.otp.saveCookiesCheckbox.information":"Vælg dette for at blive husket på din enhed og browser i deltagende butikker for hurtigere betaling. Anbefales ikke på delte enheder.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Vælg dette for at blive husket på din enhed og browser","ctp.emptyProfile.message":"Ingen kort registreret i denne Click to Pay-profil","ctp.separatorText":"eller brug","ctp.cards.title":"Gennemfør betaling med Click to Pay","ctp.cards.subtitle":"Vælg et kort, der skal bruges.","ctp.cards.expiredCard":"Udløbet","ctp.manualCardEntry":"Manuel kortindtastning","ctp.aria.infoModalButton":"Hvad er Click to Pay?","ctp.infoPopup.title":"Med Click to Pay får du letheden fra kontaktløs betaling, online","ctp.infoPopup.subtitle":"En hurtig og sikker betalingsmetode, der understøttes af Mastercard, Visa og andre betalingskort.","ctp.infoPopup.benefit1":"Click to Pay bruger kryptering for at holde dine oplysninger sikre og beskyttede","ctp.infoPopup.benefit2":"Brug det med forhandlere i hele verden","ctp.infoPopup.benefit3":"Konfigurer én gang for betalinger uden problemer i fremtiden","ctp.errors.AUTH_INVALID":"Ugyldig godkendelse","ctp.errors.NOT_FOUND":"Ingen konto fundet, indtast en gyldig e-mail eller fortsæt med manuel kortindtastning","ctp.errors.ID_FORMAT_UNSUPPORTED":"Formatet er ikke understøttet","ctp.errors.FRAUD":"Brugerkontoen er låst eller deaktiveret","ctp.errors.CONSUMER_ID_MISSING":"Forbrugeridentitet mangler i anmodningen","ctp.errors.ACCT_INACCESSIBLE":"Denne konto er i øjeblikket ikke tilgængelig, f.eks. fordi den er låst","ctp.errors.CODE_INVALID":"Forkert bekræftelsekode","ctp.errors.CODE_EXPIRED":"Denne kode er udløbet","ctp.errors.RETRIES_EXCEEDED":"Grænsen for antallet af forsøg til generering af engangsadgangskode er overskredet","ctp.errors.OTP_SEND_FAILED":"Engangsadgangskoden kunne ikke sendes til modtageren","ctp.errors.REQUEST_TIMEOUT":"Noget gik galt, prøv igen, eller brug den manuelle kortindtastning","ctp.errors.UNKNOWN_ERROR":"Noget gik galt, prøv igen, eller brug den manuelle kortindtastning","ctp.errors.SERVICE_ERROR":"Noget gik galt, prøv igen, eller brug den manuelle kortindtastning","ctp.errors.SERVER_ERROR":"Noget gik galt, prøv igen, eller brug den manuelle kortindtastning","ctp.errors.INVALID_PARAMETER":"Noget gik galt, prøv igen, eller brug den manuelle kortindtastning","ctp.errors.AUTH_ERROR":"Noget gik galt, prøv igen, eller brug den manuelle kortindtastning","paymentMethodsList.aria.label":"Vælg en betalingsmetode","companyDetails.name.invalid":"Indtast virksomhedsnavnet","companyDetails.registrationNumber.invalid":"Indtast registreringsnummeret","consent.checkbox.invalid":"Du skal acceptere vilkår og betingelser","form.instruction":"Alle felter er obligatoriske, medmindre andet er markeret.","trustly.descriptor":"Øjeblikkelig bankbetaling","trustly.description1":"Betal direkte fra alle dine bankkonti med sikkerhed på bankniveau","trustly.description2":"Ingen kort, ingen appdownload, ingen registrering","ancv.input.label":"Din ANCV-identifikation","ancv.confirmPayment":"Brug din ANCV-applikation til at bekræfte betalingen.","ancv.form.instruction":"Cheque-Vacances-applikationen er nødvendig for at validere denne betaling.","ancv.beneficiaryId.invalid":"Indtast en gyldig e-mailadresse eller ANCV-id","payme.openPayMeApp":"Gennemfør betalingen i PayMe-appen ved at godkende betalingen i appen og afvente bekræftelsen.","payme.redirectButtonLabel":"Åbn PayMe-appen","payme.scanQrCode":"Gennemfør din betaling med QR-kode","payme.timeToPay":"Denne QR-kode er gyldig i %@","payme.instructions.steps":"Åbn PayMe-appen.%@Scan QR-koden for at godkende betalingen.%@Gennemfør betalingen i appen, og afvent bekræftelse.","payme.instructions.footnote":"Luk ikke denne side, før betalingen er gennemført","payByBankAISDD.disclaimer.header":"Brug Pay by Bank til at betale med det samme fra enhver bankkonto.","payByBankAISDD.disclaimer.body":"Ved at tilknytte din bankkonto tillader du debiteringer på din konto for ethvert skyldigt beløb for brug af vores tjenester og/eller køb af vores produkter, indtil denne tilladelse tilbagekaldes.","paymentMethodBrand.other":"andet"}},98090:function(e,a,t){t.r(a),t.d(a,{default:function(){return r}});var r={payButton:"Zahle","payButton.redirecting":"Umleiten…","payButton.with":"%{value} mit %{maskedData} zahlen","payButton.saveDetails":"Angaben speichern",close:"Schließen",storeDetails:"Für zukünftige Zahlvorgänge speichern",readMore:"Mehr lesen","creditCard.holderName":"Name auf der Karte","creditCard.holderName.placeholder":"A. Müller","creditCard.holderName.invalid":"Geben Sie den Namen wie auf der Karte gezeigt ein","creditCard.numberField.title":"Kartennummer","creditCard.numberField.placeholder":"1234 **************","creditCard.expiryDateField.title":"Ablaufdatum","creditCard.expiryDateField.placeholder":"MM/JJ","creditCard.expiryDateField.month":"Monat","creditCard.expiryDateField.month.placeholder":"MM","creditCard.expiryDateField.year.placeholder":"JJ","creditCard.expiryDateField.year":"Jahr","creditCard.cvcField.title":"Sicherheitscode","creditCard.cvcField.placeholder":"123","creditCard.storeDetailsButton":"Für das nächste Mal speichern","creditCard.cvcField.placeholder.4digits":"4 Stellen","creditCard.cvcField.placeholder.3digits":"3 Stellen","creditCard.taxNumber.placeholder":"JJMMTT/0123456789",installments:"Anzahl der Raten",installmentOption:"%{times}x %{partialValue}",installmentOptionMonths:"%{times} Monate","installments.oneTime":"Einmalige Zahlung","installments.installments":"Ratenzahlung","installments.revolving":"Ratenzahlung","sepaDirectDebit.ibanField.invalid":"Ungültige Kontonummer","sepaDirectDebit.nameField.placeholder":"A. Müller","sepa.ownerName":"Name des Kontoinhabers","sepa.ibanNumber":"Kontonummer (IBAN)","error.title":"Fehler","error.subtitle.redirect":"Weiterleitung fehlgeschlagen","error.subtitle.payment":"Zahlung fehlgeschlagen","error.subtitle.refused":"Zahlvorgang verweigert","error.message.unknown":"Es ist ein unbekannter Fehler aufgetreten.","errorPanel.title":"Bestehende Fehler","idealIssuer.selectField.title":"Bank","idealIssuer.selectField.placeholder":"Bank auswählen","creditCard.success":"Zahlung erfolgreich",loading:"Laden…",continue:"Weiter",continueTo:"Weiter zu","wechatpay.timetopay":"Sie haben noch %@ um zu zahlen","sr.wechatpay.timetopay":"Sie haben %#Minuten%# %#Sekunden%# Zeit, um zu bezahlen","wechatpay.scanqrcode":"QR-Code scannen",personalDetails:"Persönliche Angaben",companyDetails:"Unternehmensdaten","companyDetails.name":"Unternehmensname","companyDetails.registrationNumber":"Registrierungsnummer",socialSecurityNumber:"Sozialversicherungsnummer",firstName:"Vorname","firstName.invalid":"Geben Sie Ihren Vornamen ein",infix:"Vorwahl",lastName:"Nachname","lastName.invalid":"Geben Sie Ihren Nachnamen ein",mobileNumber:"Handynummer","mobileNumber.invalid":"Ungültige Handynummer",city:"Stadt",postalCode:"Postleitzahl","postalCode.optional":"Postleitzahl (optional)",countryCode:"Landesvorwahl",telephoneNumber:"Telefonnummer",dateOfBirth:"Geburtsdatum",shopperEmail:"E-Mail-Adresse",gender:"Geschlecht","gender.notselected":"Wählen Sie Ihr Geschlecht",male:"Männlich",female:"Weiblich",billingAddress:"Rechnungsadresse",street:"Straße",stateOrProvince:"Bundesland",country:"Land/Region",houseNumberOrName:"Hausnummer",separateDeliveryAddress:"Abweichende Lieferadresse angeben",deliveryAddress:"Lieferadresse","deliveryAddress.firstName":"Vorname des Empfängers","deliveryAddress.lastName":"Nachname des Empfängers",zipCode:"PLZ",apartmentSuite:"Wohnung/Geschoss",provinceOrTerritory:"Provinz oder Territorium",cityTown:"Ort",address:"Straße und Hausnummer","address.placeholder":"Suchen Sie Ihre Adresse","address.errors.incomplete":"Geben Sie eine Adresse ein, um fortzufahren","address.enterManually":"Geben Sie die Adresse manuell ein",state:"Bundesstaat","field.title.optional":"(optional)","creditCard.cvcField.title.optional":"Sicherheitscode (optional)","issuerList.wallet.placeholder":"Virtuelle Brieftasche auswählen",privacyPolicy:"Datenschutz","afterPay.agreement":"Ich stimme den %@ von Riverty zu","riverty.termsAndConditions":"Ich bin mit den allgemeinen %#Geschäftsbedingungen%# für die Zahlungsmethode Riverty einverstanden. Die Datenschutzerklärung von Riverty finden Sie %#hier%#.",paymentConditions:"Zahlungsbedingungen",openApp:"App öffnen","voucher.readInstructions":"Anweisungen lesen","voucher.introduction":"Vielen Dank für Ihren Kauf. Bitte schließen Sie Ihre Zahlung unter Verwendung des folgenden Gutscheins ab.","voucher.expirationDate":"Gültig bis","voucher.alternativeReference":"Alternative Referenz","dragonpay.voucher.non.bank.selectField.placeholder":"Anbieter auswählen","dragonpay.voucher.bank.selectField.placeholder":"Bank auswählen","voucher.paymentReferenceLabel":"Zahlungsreferenz","voucher.surcharge":"Inkl. % @Zuschlag","voucher.introduction.doku":"Vielen Dank für Ihren Kauf. Bitte schließen Sie Ihre Zahlung unter Verwendung der folgenden Informationen ab.","voucher.shopperName":"Name des Käufers","voucher.merchantName":"Händler","voucher.introduction.econtext":"Vielen Dank für Ihren Kauf. Bitte schließen Sie Ihre Zahlung unter Verwendung der folgenden Informationen ab.","voucher.telephoneNumber":"Telefonnummer","voucher.shopperReference":"Kundenreferenz","voucher.collectionInstitutionNumber":"Nummer der Zahlungsannahmestelle","voucher.econtext.telephoneNumber.invalid":"Die Telefonnummer muss 10- oder 11-stellig sein","boletobancario.btnLabel":"Boleto generieren","boleto.sendCopyToEmail":"Eine Kopie an meine E-Mail-Adresse senden","button.copy":"Kopieren","button.download":"Herunterladen","boleto.socialSecurityNumber.invalid":"Geben Sie eine gültige CPF-/CNPJ-Nummer ein","creditCard.storedCard.description.ariaLabel":"Gespeicherte Karte endet auf %@","voucher.entity":"Entität",donateButton:"Spenden",notNowButton:"Nicht jetzt",thanksForYourSupport:"Danke für Ihre Unterstützung!","resultMessages.preauthorized":"Angaben gespeichert",preauthorizeWith:"Vorautorisieren mit",confirmPreauthorization:"Vorautorisierung bestätigen",confirmPurchase:"Kauf bestätigen",applyGiftcard:"Einlösen",giftcardBalance:"Saldo der Geschenkkarte",deductedBalance:"Abgezogener Betrag","creditCard.pin.title":"PIN","creditCard.encryptedPassword.label":"Die ersten zwei Ziffern des Kartenpassworts","creditCard.encryptedPassword.placeholder":"12","creditCard.encryptedPassword.invalid":"Ungültiges Passwort","creditCard.taxNumber":"Geburtsdatum des Karteninhabers oder der Unternehmensregistrierungsnummer","creditCard.taxNumber.label":"Geburtsdatum des Karteninhabers (JJMMTT) oder Unternehmensregistrierungsnummer (10-stellig)","creditCard.taxNumber.labelAlt":"Unternehmensregistrierungsnummer (10-stellig)","creditCard.taxNumber.invalid":"Ungültiges Geburtsdatum des Karteninhabers oder ungültige Unternehmensregistrierungsnummer","storedPaymentMethod.disable.button":"Entfernen","storedPaymentMethod.disable.confirmation":"Gespeicherte Zahlungsmethode entfernen","storedPaymentMethod.disable.confirmButton":"Ja, entfernen","storedPaymentMethod.disable.cancelButton":"Abbrechen","ach.bankAccount":"Bankkonto","ach.accountHolderNameField.title":"Name des Kontoinhabers","ach.accountHolderNameField.placeholder":"A. Müller","ach.accountHolderNameField.invalid":"Ungültiger Kontoinhabername","ach.accountNumberField.title":"Kontonummer","ach.accountNumberField.invalid":"Ungültige Kontonummer","ach.accountLocationField.title":"ABA-Nummer","ach.accountLocationField.invalid":"Ungültige ABA-Nummer","ach.savedBankAccount":"Gespeichertes Bankkonto","ach.savings":"Sparkonto","ach.checking":"Girokonto","select.state":"Bundesstaat auswählen","select.stateOrProvince":"Bundesland oder Provinz/Region auswählen","select.provinceOrTerritory":"Provinz oder Territorium auswählen","select.country":"Land/Region auswählen","select.noOptionsFound":"Keine Optionen gefunden","select.filter.placeholder":"Suche…","telephoneNumber.invalid":"Ungültige Telefonnummer",qrCodeOrApp:"oder","paypal.processingPayment":"Zahlung wird verarbeitet…",generateQRCode:"QR-Code generieren","await.waitForConfirmation":"Warten auf Bestätigung","mbway.confirmPayment":"Bestätigen Sie Ihre Zahlung in der MB WAY-App","shopperEmail.invalid":"Ungültige E-Mail-Adresse","dateOfBirth.format":"TT.MM.JJJJ","dateOfBirth.invalid":"Geben Sie ein gültiges Geburtsdatum ein, das angibt, dass Sie mindestens 18 Jahre alt sind.","blik.confirmPayment":"Öffnen Sie Ihre Banking-App, um die Zahlung zu bestätigen.","blik.invalid":"6 Zahlen eingeben","blik.code":"6-stelliger Code","blik.help":"Rufen Sie den Code über Ihre Banking-App ab.","swish.pendingMessage":"Es kann sein, dass der Status bis zu 10 Minuten nach dem Scan „ausstehend“ lautet. Wenn Sie währenddessen einen neuen Zahlungsversuch unternehmen, kann es sein, dass Ihnen mehrere Beträge in Rechnung gestellt werden.","field.valid":"Feld gültig","field.invalid":"Feld ungültig","error.va.gen.01":"Feld nicht ausgefüllt","error.va.gen.02":"Feld ungültig","error.va.sf-cc-num.01":"Geben Sie eine gültige Kartennummer ein","error.va.sf-cc-num.02":"Geben Sie die Kartennummer ein","error.va.sf-cc-num.03":"Geben Sie eine unterstützte Kartenmarke ein","error.va.sf-cc-num.04":"Geben Sie die vollständige Kartennummer ein","error.va.sf-cc-dat.01":"Geben Sie ein gültiges Ablaufdatum ein","error.va.sf-cc-dat.02":"Geben Sie ein gültiges Ablaufdatum ein","error.va.sf-cc-dat.03":"Kreditkarte läuft bald ab","error.va.sf-cc-dat.04":"Geben Sie das Ablaufdatum ein","error.va.sf-cc-dat.05":"Geben Sie das vollständige Ablaufdatum ein","error.va.sf-cc-mth.01":"Geben Sie den Ablaufmonat ein","error.va.sf-cc-yr.01":"Geben Sie das Ablaufjahr ein","error.va.sf-cc-yr.02":"Geben Sie das vollständige Ablaufjahr ein","error.va.sf-cc-cvc.01":"Geben Sie den Sicherheitscode ein","error.va.sf-cc-cvc.02":"Geben Sie den vollständigen Sicherheitscode ein","error.va.sf-ach-num.01":"Das Feld für die Bankkontonummer ist leer","error.va.sf-ach-num.02":"Die Bankkontonummer hat die falsche Länge","error.va.sf-ach-loc.01":"Das Feld für die Bankleitzahl ist leer","error.va.sf-ach-loc.02":"Die Bankleitzahl hat die falsche Länge","error.va.sf-kcp-pwd.01":"Das Passwortfeld ist leer","error.va.sf-kcp-pwd.02":"Das Passwort hat die falsche Länge","error.giftcard.no-balance":"Auf dieser Geschenkkarte ist kein Guthaben vorhanden","error.giftcard.card-error":"Es gibt in unserem System keine Geschenkkarte mit dieser Nummer","error.giftcard.currency-error":"Geschenkkarten sind nur in der Währung gültig, in der sie ausgestellt wurden","amazonpay.signout":"Von Amazon abmelden","amazonpay.changePaymentDetails":"Zahlungsinformationen ändern","partialPayment.warning":"Wählen Sie eine andere Zahlungsmethode zur Zahlung des Restbetrags","partialPayment.remainingBalance":"Es verbleibt ein Restbetrag von %{amount}","bankTransfer.beneficiary":"Empfänger","bankTransfer.iban":"IBAN","bankTransfer.bic":"BIC","bankTransfer.reference":"Referenz","bankTransfer.introduction":"Fortfahren, um eine neue Banküberweisungszahlung zu erstellen. Sie können die Informationen auf dem nächsten Bildschirm verwenden, um diese Zahlung abzuschließen.","bankTransfer.instructions":"Vielen Dank für Ihren Kauf. Bitte schließen Sie Ihre Zahlung unter Verwendung der folgenden Informationen ab.","bacs.accountHolderName":"Name des Bankkontoinhabers","bacs.accountHolderName.invalid":"Ungültiger Bankkontoinhabername","bacs.accountNumber":"Bankkontonummer","bacs.accountNumber.invalid":"Ungültige Bankkontonummer","bacs.bankLocationId":"Bankleitzahl","bacs.bankLocationId.invalid":"Ungültige Bankleitzahl","bacs.consent.amount":"Ich bin damit einverstanden, dass der oben genannte Betrag von meinem Bankkonto abgebucht wird.","bacs.consent.account":"Ich bestätige, dass das Konto unter meinem Namen läuft und ich der einzige erforderliche Unterzeichner bin, um die Lastschrift für dieses Konto zu autorisieren.",edit:"Bearbeiten","bacs.confirm":"Bestätigen und bezahlen","bacs.result.introduction":"Laden Sie Ihre Lastschriftanweisung (DDI/Einzugsermächtigung) herunter","download.pdf":"PDF herunterladen","creditCard.encryptedCardNumber.aria.iframeTitle":"Iframe für Kartennummer","creditCard.encryptedCardNumber.aria.label":"Kartennummer","creditCard.encryptedExpiryDate.aria.iframeTitle":"Iframe für Ablaufdatum","creditCard.encryptedExpiryDate.aria.label":"Ablaufdatum","creditCard.encryptedExpiryMonth.aria.iframeTitle":"Iframe für Ablaufmonat","creditCard.encryptedExpiryMonth.aria.label":"Monat des Ablaufdatums","creditCard.encryptedExpiryYear.aria.iframeTitle":"Iframe für Ablaufjahr","creditCard.encryptedExpiryYear.aria.label":"Jahr des Ablaufdatums","creditCard.encryptedSecurityCode.aria.iframeTitle":"Iframe für Sicherheitscode","creditCard.encryptedSecurityCode.aria.label":"Sicherheitscode","creditCard.encryptedPassword.aria.iframeTitle":"Iframe für Passwort","creditCard.encryptedPassword.aria.label":"Die ersten zwei Ziffern des Kartenpassworts","giftcard.encryptedCardNumber.aria.iframeTitle":"Iframe für Kartennummer","giftcard.encryptedCardNumber.aria.label":"Kartennummer","giftcard.encryptedSecurityCode.aria.iframeTitle":"Iframe für Stift","giftcard.encryptedSecurityCode.aria.label":"PIN",giftcardTransactionLimit:"Der zulässige Höchstbetrag pro Transaktion für diese Geschenkkarte ist %{amount}","ach.encryptedBankAccountNumber.aria.iframeTitle":"Iframe für Bankkontonummer","ach.encryptedBankAccountNumber.aria.label":"Kontonummer","ach.encryptedBankLocationId.aria.iframeTitle":"Iframe für Bankleitzahl","ach.encryptedBankLocationId.aria.label":"ABA-Nummer","twint.saved":"hinterlegt",orPayWith:"oder bezahlen Sie mit",invalidFormatExpects:"Ungültiges Format. Erwartetes Format: %{format}","upi.qrCodeWaitingMessage":"Scannen Sie den QR-Code mit Ihrer bevorzugten UPI-App, um die Zahlung abzuschließen.","upi.vpaWaitingMessage":"Öffnen Sie Ihre UPI-App, um die Zahlung zu bestätigen.","upi.modeSelection":"Wie möchten Sie UPI verwenden?","upi.completePayment":"Schließen Sie Ihre Zahlung ab","upi.mode.enterUpiId":"UPI-ID eingeben","upi.mode.qrCode":"QR-Code","upi.mode.payByAnyUpi":"Zahlen Sie mit einer beliebigen UPI-App","upi.collect.dropdown.label":"UPI-ID eingeben","upi.collect.field.label":"UPI-ID/VPA eingeben","onlineBanking.termsAndConditions":"Wenn Sie fortfahren, stimmen Sie den %#Allgemeinen Geschäftsbedingungen%# zu","onlineBankingPL.termsAndConditions":"Indem Sie fortfahren, stimmen Sie den %#Vorschriften%# und der %#Auskunftspflicht%# von Przelewy24 zu","ctp.loading.poweredByCtp":"Unterstützt von Click to Pay","ctp.loading.intro":"Wir überprüfen gerade, ob Sie bereits gespeicherte Click-to-Pay-Karten haben…","ctp.login.title":"Weiter zu Click to Pay","ctp.login.subtitle":"Geben Sie die mit Click to Pay verbundene E-Mail-Adresse ein, um fortzufahren.","ctp.login.inputLabel":"E-Mail-Adresse","ctp.logout.notYou":"Sind das nicht Sie?","ctp.logout.notYourCards":"Sind das nicht Ihre Karten?","ctp.logout.notYourCard":"Das ist nicht Ihre Karte?","ctp.logout.notYourProfile":"Das ist nicht Ihr Profil?","ctp.otp.fieldLabel":"Einmaliger Code","ctp.otp.resendCode":"Code erneut senden","ctp.otp.codeResent":"Code erneut gesendet","ctp.otp.title":"Zugriff auf Ihre Click-to-Pay-Karten","ctp.otp.subtitle":"Geben Sie den Code ein, der von %@ an %@ gesendet wurde, um Ihre Identität zu bestätigen.","ctp.otp.saveCookiesCheckbox.label":"Verifizierung beim nächsten Mal überspringen","ctp.otp.saveCookiesCheckbox.information":"Wählen Sie diese Option, um bei teilnehmenden Geschäften auf Ihrem Gerät und Browser gespeichert zu werden, um den Bestellvorgang zu beschleunigen. Nicht für gemeinsam genutzte Geräte empfohlen.","ctp.otp.saveCookiesCheckbox.shorterInfo":"Wählen Sie diese Option aus, um auf Ihrem Gerät und Browser gespeichert zu werden","ctp.emptyProfile.message":"In diesem Click-to-Pay-Profil sind keine Karten registriert","ctp.separatorText":"oder verwenden","ctp.cards.title":"Zahlung mit Click to Pay abschließen","ctp.cards.subtitle":"Wählen Sie die Karte aus, die Sie verwenden möchten.","ctp.cards.expiredCard":"Abgelaufen","ctp.manualCardEntry":"Manuelle Karteneingabe","ctp.aria.infoModalButton":"Was ist Click to Pay?","ctp.infoPopup.title":"Click to Pay ist so einfach wie kontaktloses Bezahlen, nur online","ctp.infoPopup.subtitle":"Eine schnelle, sichere Zahlungsmethode mit Mastercard, Visa und anderen Zahlungskarten.","ctp.infoPopup.benefit1":"Click to Pay nutzt Verschlüsselung zum Schutz Ihrer Daten","ctp.infoPopup.benefit2":"Verfügbar bei Händlern auf der ganzen Welt","ctp.infoPopup.benefit3":"Nach einmaliger Einrichtung bezahlen Sie in Zukunft mit nur einem Klick.","ctp.errors.AUTH_INVALID":"Authentifizierung ungültig","ctp.errors.NOT_FOUND":"Kein Konto gefunden, geben Sie eine gültige E-Mail-Adresse ein oder fahren Sie mit der manuellen Karteneingabe fort.","ctp.errors.ID_FORMAT_UNSUPPORTED":"Format wird nicht unterstützt","ctp.errors.FRAUD":"Das Benutzerkonto wurde gesperrt oder deaktiviert","ctp.errors.CONSUMER_ID_MISSING":"Die Identität des Verbrauchers fehlt in der Anfrage","ctp.errors.ACCT_INACCESSIBLE":"Dieses Konto ist derzeit nicht verfügbar; es kann z. B. gesperrt sein.","ctp.errors.CODE_INVALID":"Falscher Verifizierungscode","ctp.errors.CODE_EXPIRED":"Dieser Code ist abgelaufen","ctp.errors.RETRIES_EXCEEDED":"Das Limit für die Anzahl der Wiederholungen für das Generieren von Einmalpasswörtern (OTPs) wurde überschritten","ctp.errors.OTP_SEND_FAILED":"Das Einmalpasswort (OTP) konnte nicht an den Empfänger gesendet werden","ctp.errors.REQUEST_TIMEOUT":"Ein Fehler ist aufgetreten, versuchen Sie es erneut oder nutzen Sie die manuelle Karteneingabe.","ctp.errors.UNKNOWN_ERROR":"Ein Fehler ist aufgetreten, versuchen Sie es erneut oder nutzen Sie die manuelle Karteneingabe.","ctp.errors.SERVICE_ERROR":"Ein Fehler ist aufgetreten, versuchen Sie es erneut oder nutzen Sie die manuelle Karteneingabe.","ctp.errors.SERVER_ERROR":"Ein Fehler ist aufgetreten, versuchen Sie es erneut oder nutzen Sie die manuelle Karteneingabe.","ctp.errors.INVALID_PARAMETER":"Ein Fehler ist aufgetreten, versuchen Sie es erneut oder nutzen Sie die manuelle Karteneingabe.","ctp.errors.AUTH_ERROR":"Ein Fehler ist aufgetreten, versuchen Sie es erneut oder nutzen Sie die manuelle Karteneingabe.","paymentMethodsList.aria.label":"Wählen Sie eine Zahlungsmethode aus","companyDetails.name.invalid":"Geben Sie den Firmennamen ein","companyDetails.registrationNumber.invalid":"Geben Sie die Registrierungsnummer ein","consent.checkbox.invalid":"Sie müssen den Geschäftsbedingungen zustimmen","form.instruction":"Alle Felder sind Pflichtfelder, sofern nicht anders gekennzeichnet.","trustly.descriptor":"Sofortige Bankzahlung","trustly.description1":"Bezahlen Sie direkt von einem Ihrer Bankkonten, abgesichert durch Sicherheiten auf Bankebene","trustly.description2":"Keine Karten, kein App-Download, keine Registrierung","ancv.input.label":"Ihre ANCV-Identifikation","ancv.confirmPayment":"Bestätigen Sie die Zahlung mit Ihrem ANCV-Antrag.","ancv.form.instruction":"Zur Validierung dieser Zahlung ist der Antrag „Cheque-Vacances“ erforderlich.","ancv.beneficiaryId.invalid":"Geben Sie eine gültige E-Mail-Adresse oder ANCV-ID ein","payme.openPayMeApp":"Schließen Sie Ihre Zahlung in der PayMe-App ab, indem Sie die Zahlung in der App autorisieren und auf die Bestätigung warten.","payme.redirectButtonLabel":"Öffnen Sie die PayMe-App","payme.scanQrCode":"Schließen Sie Ihre Zahlung per QR-Code ab","payme.timeToPay":"Dieser QR-Code gilt für %@","payme.instructions.steps":"Öffnen Sie die PayMe-App.%@Scannen Sie den QR-Code, um die Zahlung zu autorisieren.%@Schließen Sie die Zahlung in der App ab und warten Sie auf eine Bestätigung.","payme.instructions.footnote":"Bitte schließen Sie diese Seite nicht, bevor die Zahlung abgeschlossen ist","payByBankAISDD.disclaimer.header":"Verwenden Sie Pay by Bank, um sofort von jedem Bankkonto zu bezahlen.","payByBankAISDD.disclaimer.body":"Durch die Verbindung Ihres Bankkontos autorisieren Sie bis auf Widerruf Abbuchungen von Ihrem Konto für alle Beträge, die für die Nutzung unserer Dienste und/oder den Kauf unserer Produkte geschuldet werden.","paymentMethodBrand.other":"andere"}}}]);