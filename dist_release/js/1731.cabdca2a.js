"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[1731],{61731:function(o,e,n){n.d(e,{loadSDK2Theme:function(){return a},loadTheme:function(){return c}});const t={};async function c(o){const e=t[o];if(e)try{await e(),console.log(`Theme loaded for ${o}`)}catch(n){console.warn(`Failed to load theme for ${o}:`,n)}else console.log(`No theme file for ${o}`)}async function a(o){if("koa"===o)return void console.log("Skipping SDK2 theme for koa game");const e=["ss","dc","foundation"];if(e.includes(o))try{await n.e(7325).then(n.bind(n,97325)),console.log("SDK2 theme loaded")}catch(t){console.warn("Failed to load SDK2 theme:",t)}else console.log(`SDK2 theme not supported for ${o}`)}}}]);