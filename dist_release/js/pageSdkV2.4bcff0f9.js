"use strict";(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[6442],{64925:function(t,e,s){s.r(e),s.d(e,{default:function(){return M}});var o=function(){var t=this,e=t._self._c;return e("div",{class:["shopping-wrapper",t.$gameName]},[e("sdk2-header"),e("div",{staticClass:"body-wrapper"},[e("sdk2-user-and-game-info"),e("div",{staticClass:"scroll-wrapper"},[e("div",{staticClass:"scroll-content"},[e("direct-gift-package",{staticStyle:{display:"none"}}),e("sdk2-package-info"),e("coupon-choose",{directives:[{name:"show",rawName:"v-show",value:t.hasCoupon,expression:"hasCoupon"}]}),e("channel-choose"),e("login-module",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]}),e("sdk2-tip")],1)])],1),e("checkout-footer",{style:{"z-index":t.showCouponPop?-1:1},attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(e){return t.judgeRisk()}}})],1)},a=[],i=s(95044),n=s(45662),c=s(69139),r=s(33960),p=s(13626),u=s(87007),l=function(){var t=this,e=t._self._c;return e("header",[e("div",{staticClass:"btn-back",on:{click:t.backAppGame}},[e("i")]),e("div",{staticClass:"fp-logo"}),e("div",{staticClass:"toggle-btn",on:{click:t.goStore}},[e("i",{staticClass:"diamond-toggle"}),t._v(" Topup "),e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.$gcbk("images.iconDiamond"),expression:"$gcbk('images.iconDiamond')"}],staticClass:"diamond-icon"})])])},d=[],_=s(52112),m=s(46838),h={name:"sdk2Header",methods:{goStore(){(0,_.bn)({NODE_ENV:"production",VUE_APP_PROD_ENV:"RELEASE",VUE_APP_PREFIX_TOKEN_KOA:"https://koa-store-release.kingsgroup.cn/api",VUE_APP_PREFIX_API_KOA:"http://**************:8990/api",VUE_APP_PREFIX_TOKEN_KOACN:"http://**************:12001/api",VUE_APP_OlD_STORE_URL_KOA:"http://**************:10085",VUE_APP_CN_ADDRESS_KOA:"http://**************:10301",VUE_APP_ROM_ADDRESS_KOA:"http://**************:10305",VUE_APP_PREFIX_AME_KOA:"https://ame-test.funplus.com",VUE_APP_PREFIX_AME:"https://ame-test.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account-stage.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid-test.funplus.com/login",VUE_APP_VipIntroducePageUrl_koa:"http://vip-test.funplus.com.cn/koa",VUE_APP_VipIntroducePageUrl_aof:"http://vip-test.funplus.com.cn/vip",VUE_APP_VipIntroducePageUrl_rom:"http://vip-test.funplus.com.cn/rom",VUE_APP_VipIntroducePageUrl_koaCn:"http://vip-test.funplus.com.cn/koa-cn",BASE_URL:"/res/"}[`VUE_APP_PREFIX_STORE_${this.$gameName.toUpperCase()}`])},backAppGame:m.Z}},f=h,P=s(81656),g=(0,P.A)(f,l,d,!1,null,"696a94da",null),v=g.exports,k=function(){var t=this,e=t._self._c;return e("section",{staticClass:"info-wrapper"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.gameLogo,expression:"gameLogo"}],staticClass:"logo"}),e("div",{staticClass:"info"},[e("div",{staticClass:"game-name"},[t._v(t._s(t.gameName))]),e("div",{staticClass:"user-name"},[t._v(t._s(t.$t("my-role"))+": "+t._s(t.$store.state.userinfo.name||"-"))])])])},C=[],A={name:"sdk2UserAndGameInfo",data(){return{gameName:this.$gcbk("gameinfo.gameName"),gameLogo:this.$gcbk("images.logoPath")}}},y=A,E=(0,P.A)(y,k,C,!1,null,"a3f70132",null),$=E.exports,w=function(){var t=this,e=t._self._c;return e("common-part",{class:["package-part",t.$gameName],attrs:{"label-font":t.$t("sdk2_product_name"),id:"package-part"}},[e("div",{staticClass:"package-wrapper"},[e("div",{staticClass:"package-icon"}),e("div",{staticClass:"info-wrapper"},[e("div",{staticClass:"name"},[t._v(t._s(t.goodsName||"-"))]),e("div",{staticClass:"price"},[t._v(t._s(t.chosenDiamond.no_tax_price)+t._s(t.currencyUnit))])])]),t.calState.isShow?e("div",{staticClass:"default-coupon"},[e("div",{staticClass:"coupon-icon"}),e("div",{staticClass:"coupon-desc"},[e("over-size-scale",[t._v(t._s(t.calState.description))])],1),e("div",{staticClass:"tips-btn",on:{click:function(e){return t.$root.$emit("showPop","sdk2Tips",{type:"constructions"})}}}),t.calState.discountPrice?e("div",{staticClass:"discount"},[t._v(t._s(t.calState.discountPrice))]):t._e()]):t._e()])},S=[],U=s(49933),b=s(95353),N=s(44374),V={name:"sdk2PackageInfo",components:{OverSizeScale:N.A,CommonPart:U.A},data(){return{goodsName:"-"}},computed:{...(0,b.aH)(["currencyUnit"]),...(0,b.L8)("formdata",["FinalPriceState"]),...(0,b.aH)("formdata",["chosenCoupon","defaultRebateInfo","defaultDiscountInfo","chosenDiamond"]),calState(){const t={type:"",isShow:!1,description:"",discountPrice:""},e=this.chosenCoupon,s=this.defaultDiscountInfo;if("direct_first_pay"===e.type||!e.feType&&"direct_fixed_discount"===s.type){const o="direct_first_pay"===e.type?e:s;t.isShow=!0,t.discountPrice=`- ${o.discount_amount}${this.currencyUnit}`,t.description=this.$t(`sdk2_bonus_${o.type}`,{0:`${o.rateWidthOutPercent}% OFF`}),t.type=o.type}const o=this.defaultRebateInfo;if("direct_first_pay_rebate"===e.type||!e.feType&&"direct_fixed_rebate"===o.type){const s="direct_first_pay_rebate"===e.type?e:o;t.isShow=!0;const a=Math.floor(s.coin-s.level_coin),i=`${a} ${this.$vt("tokenName")}`;t.description=this.$t(`sdk2_bonus_${s.type}`,{0:i}),t.type=s.type}return t}},created(){this.$root.$on("updateSdk2PackageName",t=>{window.defaultPackageName=this.goodsName=t}),window._calState=()=>this.calState}},I=V,O=(0,P.A)(I,w,S,!1,null,"251b8dc8",null),R=O.exports,x=function(){var t=this,e=t._self._c;return e("common-part",{staticClass:"tips-part"},[e("div",{staticClass:"tips"},[t._v(" You are purchasing a digital license for this product. For full terms, see "),e("span",{on:{click:function(e){return t.$root.$emit("showPop","sdk2Tips",{type:"policy"})}}},[t._v("purchase policy")]),t._v(". ")])])},D=[],F={name:"sdk2Tip",components:{CommonPart:U.A}},L=F,T=(0,P.A)(L,x,D,!1,null,"fd054c46",null),K=T.exports,G=s(58411),z={name:"Pay",mixins:[u.A],components:{Sdk2Tip:K,Sdk2PackageInfo:R,sdk2UserAndGameInfo:$,Sdk2Header:v,CheckoutFooter:p.A,CouponChoose:c.A,ChannelChoose:n.A,LoginModule:i.A,DirectGiftPackage:r.A},computed:{...(0,b.aH)("formdata",["chosenCoupon"]),...(0,b.aH)(["urlParams"])},data(){return{hasCoupon:!1,showCouponPop:!1}},created(){this.$root.$on("updateSdk2CouponList",t=>{this.chosenCoupon.type&&this.chosenCoupon.type.includes("direct_first_pay")?this.hasCoupon=!1:this.hasCoupon=t.length>0}),this.$root.$on("showCouponPop",t=>{this.showCouponPop=t}),this.$root.$on("loginEnd",t=>{const e={};this.urlParams.oid&&(e.oid=this.urlParams.oid),t&&(0,G.a5)(e)})}},X=z,H=(0,P.A)(X,o,a,!1,null,"42b0224f",null),M=H.exports}}]);