(self["webpackChunkweb_pay_unique_common"]=self["webpackChunkweb_pay_unique_common"]||[]).push([[4402],{712:function(t,e,n){var i={"./login_reward_0.png":7568,"./login_reward_1.png":4473,"./login_reward_2.png":39938,"./login_reward_3.png":61067};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id=712},4283:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return d}});var i=function(){var t=this,e=t._self._c;return e("container-v2",{staticClass:"custom-pop"},[e("div",{staticClass:"header"},[e("span",[t._v("DURCHLESEN！")]),e("i",{on:{click:t.closePop}})]),e("div",{staticClass:"content"},[e("div",{staticClass:"strong"},[t._v("KEIN WIDERRUFSRECHT！")]),t._v(' Wenn du auf "Kaufen" tippst, stimmst du der unmittelbaren Erfüllung des Vertrags zu und akzeptierst, dass du dadurch dein Widerrufsrecht verlierst .(siehe auch '),e("a",{attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Rückerstattungsbedingungen")]),t._v("). ")]),e("div",{staticClass:"footer"},[e("div",{staticClass:"btn-cancel btn",on:{click:t.closePop}},[t._v("ABBRECHEN")]),e("div",{staticClass:"btn-confirm btn",on:{click:t.confirm}},[t._v("Zustimmen und fortfahren.")])])])},s=[],o=n(34870),r={name:"privateConfirmPop",components:{ContainerV2:o.A},props:{option:{type:Object,default:()=>{}}},methods:{closePop(){this.option.no&&this.option.no(),this.$root.$emit("closePop")},confirm(){this.option.ok&&this.option.ok(),this.$root.$emit("changeDePopPrivacy"),this.$root.$emit("closePop")}}},a=r,l=n(81656),c=(0,l.A)(a,i,s,!1,null,"27acb0ae",null),d=c.exports},5161:function(t,e,n){var i={"./aof/boon/install_reward_0.png":98301,"./aof/boon/install_reward_1.png":77172,"./aof/boon/install_reward_2.png":99631,"./aof/boon/install_reward_3.png":30886};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id=5161},8157:function(t){"use strict";t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2Jvb24taW5zdGFsbC1hd2FyZC44ZWRkNTRmMC5wbmciOw=="},9192:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return c}});var i=function(){var t=this;t._self._c;return t._m(0)},s=[function(){var t=this,e=t._self._c;return e("div",[e("a",{attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Refund Policy")]),t._v(". ")])}],o={name:"RefundPolicy"},r=o,a=n(81656),l=(0,a.A)(r,i,s,!1,null,"6d6cc831",null),c=l.exports},10700:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return h}});var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,t.$gameName],attrs:{title:t.$t("text_tips"),"hide-close":!0},scopedSlots:t._u([{key:"footerBtn",fn:function(){return[e("div",{staticClass:"custom-btn btn-ok",on:{click:t.close}},[t._v(t._s(t.$t("confirm-btn")))])]},proxy:!0}])},[e("div",{staticClass:"desc"},[t._v(t._s(t.$t("settings_terms_of_service_agreement_description")))]),e("div",{staticClass:"private"},[e("span",{on:{click:function(e){return t.go(0)}}},[t._v("《"+t._s(t.$t("agreement"))+"》")]),e("span",{on:{click:function(e){return t.go(1)}}},[t._v("《"+t._s(t.$t("privacy"))+"》")])])])},s=[],o=n(81135),r=n(82851),a=n(87367),l={name:"PrivacyPolicy",props:{option:Object},components:{Container:o.A},methods:{go(t){const e=(this.$store.state.country||"").toLowerCase(),n=["https://funplus.com/terms-conditions","https://funplus.com/privacy-policy/"],i={kr:["https://funplus.com/terms-conditions-en-as/kr/","https://funplus.com/privacy-policy-en-as/kr/"],jp:["https://funplus.com/terms-conditions-en-as/ja/","https://funplus.com/privacy-policy-en-as/ja/ "]};let s=n[t];i[e]&&(s=i[e][t]),this.$store.getters["gameinfo/isPuzalaGame"]&&(s=["https://www.puzala.com/terms-of-service","https://www.puzala.com/privacy-policy"][t]),window.open(s,"_blank")},close(){r.b.setLocalStorage("confirmPrivacyPolicy",1),this.$store.commit("setPrivacyPolicyStatus",!0);const t={p0:"web",p1:7,p2:"1096",silence:!0};(0,a.Pp)(t),this.$root.$emit("closePop")}}},c=l,d=n(81656),u=(0,d.A)(c,i,s,!1,null,"09682632",null),h=u.exports},12170:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return v}});var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,this.$store.state.gameinfo.gameCode,t.$gameName],attrs:{"hide-close":!0,option:t.config,title:t.$vt("howToUseDiamond"),id:"what-is-diamond-wrapper"}},[e("div",{staticClass:"description"},[t._v(t._s(t.$vt("whatIsDiamondTitle"))+" ")]),e("poster-swiper")],1)},s=[],o=n(81135),r=function(){var t=this,e=t._self._c;return e("Swiper",{staticClass:"my-swiper-wrapper",class:[t.$gameName],attrs:{options:t.swiperOptions}},[t.webCashierBanner.length?[t._l(t.webCashierBanner,function(n){return e("SwiperSlide",{key:n.imageUrl},[e("img",{attrs:{src:n.imageUrl,alt:""}}),n.jumpUrl?e("a",{attrs:{href:n.jumpUrl,target:"_blank"}}):t._e()])}),e("div",{staticClass:"swiper-pagination",attrs:{slot:"pagination"},slot:"pagination"},t._l(Object.keys(t.webCashierBanner),function(n){return e("div",{key:n,class:["pagination-dot",{"pagination-dot_active":t.swiperIndex===+n}]})}),0)]:t._e()],2)},a=[],l=n(24276),c=(n(13561),{name:"PosterSwiper",data(){const t=this;return{swiperOptions:{autoplay:{disableOnInteraction:!1},on:{slideChangeTransitionStart:function(){t.swiperIndex=this.activeIndex}}},swiperIndex:0,webCashierBanner:this.$imageLoader("whatsDiamond",[])}},components:{Swiper:l.Swiper,SwiperSlide:l.SwiperSlide}}),d=c,u=n(81656),h=(0,u.A)(d,r,a,!1,null,"44ae424a",null),p=h.exports,f={name:"WhatIsDiamond",props:{option:Object},data(){return{config:{confirmBtnTxt:this.$t("got-it")}}},components:{PosterSwiper:p,Container:o.A}},m=f,g=(0,u.A)(m,i,s,!1,null,"381d0692",null),v=g.exports},18374:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return d}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"checkbox"},[e("label",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.isCheck,expression:"isCheck"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.isCheck)?t._i(t.isCheck,null)>-1:t.isCheck},on:{change:[function(e){var n=t.isCheck,i=e.target,s=!!i.checked;if(Array.isArray(n)){var o=null,r=t._i(n,o);i.checked?r<0&&(t.isCheck=n.concat([o])):r>-1&&(t.isCheck=n.slice(0,r).concat(n.slice(r+1)))}else t.isCheck=s},function(e){return t.notifyServer("check")}]}}),t._v(" Ich habe "),e("a",{attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Rückerstattungsrichtlinie")]),t._v(" gelesen und stimme zu. ")])])},s=[],o=n(87367),r={name:"privatePermission",computed:{calcPop(){return!this.isCheck||!this.isPop}},watch:{calcPop(t){window.__needDEPop=t}},data(){return{isCheck:!1,isPop:!1}},methods:{initState(){const t={p0:"web",p1:9,p2:2531,p3:"api",game:this.$store.state.gameinfo.gameProject.split("_")[0]};(0,o.Pp)(t).then(t=>{const{data:e,code:n}=t;0===n&&(window.__needDEPop=!0,this.isCheck=e.check,this.isPop=e.popup)})},notifyServer(t){const e={p0:"web",p1:9,p2:2532,p3:"api",game:this.$store.state.gameinfo.gameProject.split("_")[0]};"check"===t&&(e.set_type=0,e.set_status=Number(this.isCheck)),"pop"===t&&(e.set_type=1,e.set_status=1,this.isCheck=!0),(0,o.Pp)(e).then(e=>{const{code:n}=e;0!==n&&("pop"===t&&(this.isPop=!1),"check"===t&&(this.isCheck=!this.isCheck))})}},created(){this.initState(),this.$root.$on("changeDePopPrivacy",()=>this.notifyServer("pop"))}},a=r,l=n(81656),c=(0,l.A)(a,i,s,!1,null,"92f4ff72",null),d=c.exports},30839:function(t,e,n){var i={"./boon/boon-login-award.png":77969,"./ssd/boon-login-award.png":85195,"./ssv2/boon-login-award.png":39313};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id=30839},35905:function(t,e,n){var i={"./boon/boon-install-award.png":50863,"./ssd/boon-install-award.png":8157,"./ssv2/boon-install-award.png":78383};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id=35905},38374:function(t,e,n){var i={"./install_reward_0.png":2378,"./install_reward_1.png":41235,"./install_reward_2.png":66744,"./install_reward_3.png":51361};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id=38374},39313:function(t){"use strict";t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2Jvb24tbG9naW4tYXdhcmQuYjM3MzliZWQucG5nIjs="},39567:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return w}});var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale],attrs:{title:t.$t("boon-page-title")}},[e("div",{staticClass:"divider"}),e("div",{staticClass:"tab-wrapper"},t._l(t.tabList,function(n,i){return e("div",{key:i,staticClass:"tab",class:[`tab-${i}`,t.chosenIndex===i?"chosen-active":"","boon-ss-install-title"!==n.title||t.gotInstallReward?"":"dot-active","boon-ss-login-title"!==n.title||t.gotLoginReward?"":"dot-active"],on:{click:function(e){t.chosenIndex=i}}},[e("span",[t._v(t._s(t.$t(n.title)))])])}),0),e("Swiper",{staticClass:"my-swiper-wrapper",attrs:{options:t.swiperOptions}},[e("SwiperSlide",{key:"install"},[e("div",{staticClass:"charge-desc"},[e("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-2-title")))]),e("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),e("div",{staticClass:"gift-image"},[e("img",{attrs:{src:n(35905)(`./${"ss"===t.$gameName?"boon":t.$gameName}/boon-install-award.png`),alt:""}})]),e("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadInstall?[t.gotInstallReward?e("span",{staticClass:"forbidden"}):e("span",{staticClass:"todo click-btn",on:{click:function(e){return t.getReward(t.getPwaReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:[t.calcShowInstall?e("span",{staticClass:"click-btn",on:{click:t.install}},[t._v(t._s(t.$t("boon-task-2-add")))]):e("p",{staticClass:"browser-forbidden"},[t._v(t._s(t.$t("boon-browser-forbidden")))])]:[e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])]],2)]),e("SwiperSlide",{key:"login"},[e("div",{staticClass:"charge-desc"},[e("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-1-title")))]),e("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),e("div",{staticClass:"gift-image"},[e("img",{attrs:{src:n(30839)(`./${"ss"===t.$gameName?"boon":t.$gameName}/boon-login-award.png`),alt:""}})]),e("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadLogin?[t.gotLoginReward?e("span",{staticClass:"forbidden"}):e("span",{staticClass:"todo click-btn",on:{click:function(e){return t.getReward(t.getLoginReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-go-charge-short")))]):e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])],2)])],1)],1)},s=[],o=n(81135),r=n(87367),a=n(24276),l=(n(13561),n(77232)),c=n.n(l),d=n(95353);const{projectId:u,loginAction:h,getLoginReward:p,pwaOpenAction:f,getPwaReward:m}=window.$gcbk("apiParams.boonAme"),g={p0:"web",p1:u};function v(){const t=window.matchMedia("(display-mode: standalone)").matches;return document.referrer.startsWith("android-app://")?"twa":navigator.standalone||t?"standalone":"browser"}var V={name:"BoonPop",components:{Container:o.A,Swiper:a.Swiper,SwiperSlide:a.SwiperSlide},data(){const t=this;return{hadLogin:!1,gotLoginReward:!1,hadInstall:!1,gotInstallReward:!1,deferredPrompt:window.__deferredPrompt||void 0,showMobileSafariGuide:!1,progressPercent:0,chosenIndex:0,swiperInstance:void 0,swiperOptions:{autoplay:!1,on:{slideChangeTransitionStart:function(){t.chosenIndex=this.activeIndex},init:function(){t.swiperInstance=this}}},getLoginReward:p,getPwaReward:m,tabList:[{title:"boon-ss-install-title"},{title:"boon-ss-login-title"}]}},methods:{onClose(){this.$root.$emit("closePop")},showInstallPart(){window.addEventListener("beforeinstallprompt",t=>{t.preventDefault(),this.deferredPrompt=t})},resetStatus(){const t={p0:"web",p1:u,p2:`${h},${p},${f},${m}`};this.$loading.show(),(0,r.OL)(t).then(t=>{const{data:e,code:n}=t;if(0===n){const t={};for(const n of Object.values(e))t[n.task_id]=n;this.hadLogin=h in t,this.gotLoginReward=p in t,this.hadInstall=f in t,this.gotInstallReward=m in t}}).finally(()=>this.$loading.hide())},getReward(t){const e={p2:t};this.$loading.show(),(0,r.Pp)({...e,...g}).then(e=>{const{code:n,data:i=[]}=e;0===n&&i.length&&(t===m&&(this.gotInstallReward=!0),t===p&&(this.gotLoginReward=!0))}).finally(()=>this.$loading.hide())},focusInput(){this.$root.$emit("closePop"),this.$root.$emit("ClickPayButNotLogin")},install(){this.$root.$emit("closePop"),this.deferredPrompt?(this.deferredPrompt.prompt(),this.deferredPrompt.userChoice.then(t=>{if("accepted"===t.outcome){console.log("User accepted the A2HS prompt");const t=setInterval(()=>{"standalone"===v()&&(clearInterval(t),this.$root.$emit("installSuccessful"))},1e3)}else console.log("User dismissed the A2HS prompt");this.deferredPrompt=void 0})):setTimeout(()=>{this.$root.$emit("mobileSafariGuide")},500)}},computed:{...(0,d.aH)("userinfo",["isLogin"]),...(0,d.aH)(["userinfo"]),calcShowInstall(){const t=new(c())(navigator.userAgent),e=t.getResult(),{browser:n}=e,i="Mobile Safari"===n.name||(n.name||"").includes("Chrome")&&this.deferredPrompt;return!this.isLogin||(!!this.hadInstall||i)}},created(){this.$root.$on("loginSuccess",()=>{setTimeout(()=>this.resetStatus(),2e3)}),this.isLogin&&this.resetStatus(),this.$watch("chosenIndex",t=>{this.swiperInstance.slideTo(t,200,!1)}),this.showInstallPart()}},b=V,U=n(81656),N=(0,U.A)(b,i,s,!1,null,"18058d16",null),w=N.exports},45777:function(t,e,n){"use strict";n.d(e,{$:function(){return d},$t:function(){return z},AH:function(){return W},AU:function(){return U},BC:function(){return M},CF:function(){return m},DK:function(){return R},Gq:function(){return k},HW:function(){return J},Hs:function(){return Y},I6:function(){return X},K$:function(){return g},K2:function(){return Q},M8:function(){return G},Pe:function(){return x},Qq:function(){return E},R7:function(){return Z},TF:function(){return H},XQ:function(){return w},YL:function(){return B},Y_:function(){return D},__:function(){return F},cY:function(){return y},eq:function(){return S},hZ:function(){return N},iQ:function(){return u},is:function(){return C},kY:function(){return V},kp:function(){return O},nB:function(){return f},on:function(){return b},p1:function(){return p},pb:function(){return T},pd:function(){return v},qy:function(){return _},vy:function(){return h},zy:function(){return L}});var i=n(85429);function s(t){const e=t.__proto__;Object.defineProperty(t,"__proto__",{get(){return e},set(t){e.__proto__=t}})}class o extends Array{constructor(t){"number"===typeof t?super(t):(super(...t||[]),s(this))}}function r(t=[]){const e=[];return t.forEach(t=>{Array.isArray(t)?e.push(...r(t)):e.push(t)}),e}function a(t,e){return Array.prototype.filter.call(t,e)}function l(t){const e=[];for(let n=0;n<t.length;n+=1)-1===e.indexOf(t[n])&&e.push(t[n]);return e}function c(t,e){if("string"!==typeof t)return[t];const n=[],i=e.querySelectorAll(t);for(let s=0;s<i.length;s+=1)n.push(i[s]);return n}function d(t,e){const n=(0,i.zk)(),s=(0,i.YE)();let r=[];if(!e&&t instanceof o)return t;if(!t)return new o(r);if("string"===typeof t){const n=t.trim();if(n.indexOf("<")>=0&&n.indexOf(">")>=0){let t="div";0===n.indexOf("<li")&&(t="ul"),0===n.indexOf("<tr")&&(t="tbody"),0!==n.indexOf("<td")&&0!==n.indexOf("<th")||(t="tr"),0===n.indexOf("<tbody")&&(t="table"),0===n.indexOf("<option")&&(t="select");const e=s.createElement(t);e.innerHTML=n;for(let n=0;n<e.childNodes.length;n+=1)r.push(e.childNodes[n])}else r=c(t.trim(),e||s)}else if(t.nodeType||t===n||t===s)r.push(t);else if(Array.isArray(t)){if(t instanceof o)return t;r=t}return new o(l(r))}function u(...t){const e=r(t.map(t=>t.split(" ")));return this.forEach(t=>{t.classList.add(...e)}),this}function h(...t){const e=r(t.map(t=>t.split(" ")));return this.forEach(t=>{t.classList.remove(...e)}),this}function p(...t){const e=r(t.map(t=>t.split(" ")));this.forEach(t=>{e.forEach(e=>{t.classList.toggle(e)})})}function f(...t){const e=r(t.map(t=>t.split(" ")));return a(this,t=>e.filter(e=>t.classList.contains(e)).length>0).length>0}function m(t,e){if(1===arguments.length&&"string"===typeof t)return this[0]?this[0].getAttribute(t):void 0;for(let n=0;n<this.length;n+=1)if(2===arguments.length)this[n].setAttribute(t,e);else for(const e in t)this[n][e]=t[e],this[n].setAttribute(e,t[e]);return this}function g(t){for(let e=0;e<this.length;e+=1)this[e].removeAttribute(t);return this}function v(t){for(let e=0;e<this.length;e+=1)this[e].style.transform=t;return this}function V(t){for(let e=0;e<this.length;e+=1)this[e].style.transitionDuration="string"!==typeof t?`${t}ms`:t;return this}function b(...t){let[e,n,i,s]=t;function o(t){const e=t.target;if(!e)return;const s=t.target.dom7EventData||[];if(s.indexOf(t)<0&&s.unshift(t),d(e).is(n))i.apply(e,s);else{const t=d(e).parents();for(let e=0;e<t.length;e+=1)d(t[e]).is(n)&&i.apply(t[e],s)}}function r(t){const e=t&&t.target&&t.target.dom7EventData||[];e.indexOf(t)<0&&e.unshift(t),i.apply(this,e)}"function"===typeof t[1]&&([e,i,s]=t,n=void 0),s||(s=!1);const a=e.split(" ");let l;for(let c=0;c<this.length;c+=1){const t=this[c];if(n)for(l=0;l<a.length;l+=1){const e=a[l];t.dom7LiveListeners||(t.dom7LiveListeners={}),t.dom7LiveListeners[e]||(t.dom7LiveListeners[e]=[]),t.dom7LiveListeners[e].push({listener:i,proxyListener:o}),t.addEventListener(e,o,s)}else for(l=0;l<a.length;l+=1){const e=a[l];t.dom7Listeners||(t.dom7Listeners={}),t.dom7Listeners[e]||(t.dom7Listeners[e]=[]),t.dom7Listeners[e].push({listener:i,proxyListener:r}),t.addEventListener(e,r,s)}}return this}function U(...t){let[e,n,i,s]=t;"function"===typeof t[1]&&([e,i,s]=t,n=void 0),s||(s=!1);const o=e.split(" ");for(let r=0;r<o.length;r+=1){const t=o[r];for(let e=0;e<this.length;e+=1){const o=this[e];let r;if(!n&&o.dom7Listeners?r=o.dom7Listeners[t]:n&&o.dom7LiveListeners&&(r=o.dom7LiveListeners[t]),r&&r.length)for(let e=r.length-1;e>=0;e-=1){const n=r[e];i&&n.listener===i||i&&n.listener&&n.listener.dom7proxy&&n.listener.dom7proxy===i?(o.removeEventListener(t,n.proxyListener,s),r.splice(e,1)):i||(o.removeEventListener(t,n.proxyListener,s),r.splice(e,1))}}}return this}function N(...t){const e=(0,i.zk)(),n=t[0].split(" "),s=t[1];for(let i=0;i<n.length;i+=1){const o=n[i];for(let n=0;n<this.length;n+=1){const i=this[n];if(e.CustomEvent){const n=new e.CustomEvent(o,{detail:s,bubbles:!0,cancelable:!0});i.dom7EventData=t.filter((t,e)=>e>0),i.dispatchEvent(n),i.dom7EventData=[],delete i.dom7EventData}}}return this}function w(t){const e=this;function n(i){i.target===this&&(t.call(this,i),e.off("transitionend",n))}return t&&e.on("transitionend",n),this}function k(t){if(this.length>0){if(t){const t=this.styles();return this[0].offsetWidth+parseFloat(t.getPropertyValue("margin-right"))+parseFloat(t.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null}function R(t){if(this.length>0){if(t){const t=this.styles();return this[0].offsetHeight+parseFloat(t.getPropertyValue("margin-top"))+parseFloat(t.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null}function y(){if(this.length>0){const t=(0,i.zk)(),e=(0,i.YE)(),n=this[0],s=n.getBoundingClientRect(),o=e.body,r=n.clientTop||o.clientTop||0,a=n.clientLeft||o.clientLeft||0,l=n===t?t.scrollY:n.scrollTop,c=n===t?t.scrollX:n.scrollLeft;return{top:s.top+l-r,left:s.left+c-a}}return null}function Z(){const t=(0,i.zk)();return this[0]?t.getComputedStyle(this[0],null):{}}function W(t,e){const n=(0,i.zk)();let s;if(1===arguments.length){if("string"!==typeof t){for(s=0;s<this.length;s+=1)for(const e in t)this[s].style[e]=t[e];return this}if(this[0])return n.getComputedStyle(this[0],null).getPropertyValue(t)}if(2===arguments.length&&"string"===typeof t){for(s=0;s<this.length;s+=1)this[s].style[t]=e;return this}return this}function F(t){return t?(this.forEach((e,n)=>{t.apply(e,[e,n])}),this):this}function T(t){const e=a(this,t);return d(e)}function _(t){if("undefined"===typeof t)return this[0]?this[0].innerHTML:null;for(let e=0;e<this.length;e+=1)this[e].innerHTML=t;return this}function E(t){if("undefined"===typeof t)return this[0]?this[0].textContent.trim():null;for(let e=0;e<this.length;e+=1)this[e].textContent=t;return this}function C(t){const e=(0,i.zk)(),n=(0,i.YE)(),s=this[0];let r,a;if(!s||"undefined"===typeof t)return!1;if("string"===typeof t){if(s.matches)return s.matches(t);if(s.webkitMatchesSelector)return s.webkitMatchesSelector(t);if(s.msMatchesSelector)return s.msMatchesSelector(t);for(r=d(t),a=0;a<r.length;a+=1)if(r[a]===s)return!0;return!1}if(t===n)return s===n;if(t===e)return s===e;if(t.nodeType||t instanceof o){for(r=t.nodeType?[t]:t,a=0;a<r.length;a+=1)if(r[a]===s)return!0;return!1}return!1}function x(){let t,e=this[0];if(e){t=0;while(null!==(e=e.previousSibling))1===e.nodeType&&(t+=1);return t}}function S(t){if("undefined"===typeof t)return this;const e=this.length;if(t>e-1)return d([]);if(t<0){const n=e+t;return d(n<0?[]:[this[n]])}return d([this[t]])}function M(...t){let e;const n=(0,i.YE)();for(let i=0;i<t.length;i+=1){e=t[i];for(let t=0;t<this.length;t+=1)if("string"===typeof e){const i=n.createElement("div");i.innerHTML=e;while(i.firstChild)this[t].appendChild(i.firstChild)}else if(e instanceof o)for(let n=0;n<e.length;n+=1)this[t].appendChild(e[n]);else this[t].appendChild(e)}return this}function Y(t){const e=(0,i.YE)();let n,s;for(n=0;n<this.length;n+=1)if("string"===typeof t){const i=e.createElement("div");for(i.innerHTML=t,s=i.childNodes.length-1;s>=0;s-=1)this[n].insertBefore(i.childNodes[s],this[n].childNodes[0])}else if(t instanceof o)for(s=0;s<t.length;s+=1)this[n].insertBefore(t[s],this[n].childNodes[0]);else this[n].insertBefore(t,this[n].childNodes[0]);return this}function Q(t){return this.length>0?t?this[0].nextElementSibling&&d(this[0].nextElementSibling).is(t)?d([this[0].nextElementSibling]):d([]):this[0].nextElementSibling?d([this[0].nextElementSibling]):d([]):d([])}function J(t){const e=[];let n=this[0];if(!n)return d([]);while(n.nextElementSibling){const i=n.nextElementSibling;t?d(i).is(t)&&e.push(i):e.push(i),n=i}return d(e)}function B(t){if(this.length>0){const e=this[0];return t?e.previousElementSibling&&d(e.previousElementSibling).is(t)?d([e.previousElementSibling]):d([]):e.previousElementSibling?d([e.previousElementSibling]):d([])}return d([])}function L(t){const e=[];let n=this[0];if(!n)return d([]);while(n.previousElementSibling){const i=n.previousElementSibling;t?d(i).is(t)&&e.push(i):e.push(i),n=i}return d(e)}function z(t){const e=[];for(let n=0;n<this.length;n+=1)null!==this[n].parentNode&&(t?d(this[n].parentNode).is(t)&&e.push(this[n].parentNode):e.push(this[n].parentNode));return d(e)}function G(t){const e=[];for(let n=0;n<this.length;n+=1){let i=this[n].parentNode;while(i)t?d(i).is(t)&&e.push(i):e.push(i),i=i.parentNode}return d(e)}function O(t){let e=this;return"undefined"===typeof t?d([]):(e.is(t)||(e=e.parents(t).eq(0)),e)}function X(t){const e=[];for(let n=0;n<this.length;n+=1){const i=this[n].querySelectorAll(t);for(let t=0;t<i.length;t+=1)e.push(i[t])}return d(e)}function D(t){const e=[];for(let n=0;n<this.length;n+=1){const i=this[n].children;for(let n=0;n<i.length;n+=1)t&&!d(i[n]).is(t)||e.push(i[n])}return d(e)}function H(){for(let t=0;t<this.length;t+=1)this[t].parentNode&&this[t].parentNode.removeChild(this[t]);return this}d.fn=o.prototype;const j="resize scroll".split(" ");function $(t){function e(...e){if("undefined"===typeof e[0]){for(let e=0;e<this.length;e+=1)j.indexOf(t)<0&&(t in this[e]?this[e][t]():d(this[e]).trigger(t));return this}return this.on(t,...e)}return e}$("click"),$("blur"),$("focus"),$("focusin"),$("focusout"),$("keyup"),$("keydown"),$("keypress"),$("submit"),$("change"),$("mousedown"),$("mousemove"),$("mouseup"),$("mouseenter"),$("mouseleave"),$("mouseout"),$("mouseover"),$("touchstart"),$("touchend"),$("touchmove"),$("resize"),$("scroll")},50863:function(t){"use strict";t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2Jvb24taW5zdGFsbC1hd2FyZC43YjUwZGY3Yi5wbmciOw=="},50971:function(t,e,n){var i={"./aof/boon/login_reward_0.png":81499,"./aof/boon/login_reward_1.png":16146,"./aof/boon/login_reward_2.png":81289,"./aof/boon/login_reward_3.png":26464};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id=50971},56641:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return d}});var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,t.$gameName],attrs:{title:t.$t("text_tips"),"hide-close":!0,id:"risk-policy-wrapper"},scopedSlots:t._u([{key:"footerBtn",fn:function(){return[e("div",{staticClass:"custom-btn btn-ok",on:{click:t.close}},[t._v(t._s(t.$t("confirm-btn")))])]},proxy:!0}])},[e("div",{staticClass:"desc"},["always_banned"===t.key?e("div",{domProps:{innerHTML:t._s(t.$t("risk_policy_forbidden_forever"))}}):t._e(),["banned_adyen","banned_pingpong","banned_uid"].includes(t.key)?e("div",{domProps:{innerHTML:t._s(t.$t("risk_policy_forbidden_some",{0:`<span>${t.leaveDate}</span>`}))}}):t._e(),["access_warn","use_adyen","use_pingpong","use_wxpay","use_alipay","use_paypal","access_warn_black_room"].includes(t.key)?e("div",{domProps:{innerHTML:t._s(t.$t("risk_policy_pay_tip"))}}):t._e()])])},s=[],o=n(81135),r={name:"RiskControlPolicy",props:{option:{type:Object,default:()=>({})}},data(){const{key:t,value:e,cb:n}=this.option;return{key:t,value:e,cb:n,interval:"",leaveDate:""}},components:{Container:o.A},methods:{close(){this.$root.$emit("closePop"),this.interval&&clearInterval(this.interval),this.cb&&this.cb()},calcLeaveTime(){const t=t=>t<10?`0${Math.floor(t)}`:Math.floor(t),e=e=>`${Math.floor(e/3600/24)}d ${t(e/3600%24)} : ${t(e/60%60)} : ${t(e%60)}`;this.leaveDate=e(this.value),setInterval(()=>{if(0===this.value)return this.close(),void clearInterval(this.interval);this.leaveDate=e(--this.value)},1e3)}},created(){["banned_uid","banned_adyen","banned_pingpong"].includes(this.key)&&this.calcLeaveTime()},beforeDestroy(){this.interval&&clearInterval(this.interval)}},a=r,l=n(81656),c=(0,l.A)(a,i,s,!1,null,"2353863d",null),d=c.exports},59488:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("container",{class:["custom-diamond-wrapper",t.$gameName]},[t.showHowToCatPage?e("div",{staticClass:"cat-page"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("login-validation-main-how-cat")))]),e("div",{staticClass:"back-btn",on:{click:function(e){t.showHowToCatPage=!1}}}),e("img",{attrs:{src:t.$imageLoader("loganFindValidationCode"),alt:""}}),e("div",{staticClass:"time-tips",domProps:{innerHTML:t._s(t.$t("login-validation-date-construction").replace("30",`<span>${t.loginValidationExpireCount}</span>`))}})]):e("div",{staticClass:"main-page"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("login-validation-main-title")))]),e("div",{staticClass:"send-info"},[e("div",{staticClass:"tips",domProps:{innerHTML:t._s(t.$t("login-validation-main-send-over",{0:t.htmlUsername}))}}),t.canResend?e("div",{staticClass:"send-btn",on:{click:t.resend}},[t._v(t._s(t.$t("login-validation-main-resend")))]):t._e(),t.leaveCount>0?e("div",{staticClass:"leave-count"},[t._v("（"+t._s(t.leaveCount)+"s）")]):t._e()]),e("div",{staticClass:"input-wrapper"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.inputCode,expression:"inputCode"}],class:t.$i18n.locale,attrs:{type:"number",placeholder:t.$t("login-validation-main-placeholder"),autofocus:""},domProps:{value:t.inputCode},on:{input:[function(e){e.target.composing||(t.inputCode=e.target.value)},t.fixInput]}})]),e("div",{staticClass:"safe-tips",domProps:{innerHTML:t._s(t.$t("login-validation-safety-construction"))}}),e("div",{class:["confirm-btn",{"confirm-btn_validated":t.rawValidate}],on:{click:t.checkCode}},[t._v(t._s(t.$t("login-validation-main-confirm")))]),e("div",{staticClass:"cat-code-tips",on:{click:function(e){t.showHowToCatPage=!0}}},[t._v(t._s(t.$t("login-validation-main-how-cat")))]),e("div",{staticClass:"close",on:{click:t.close}})])])},s=[],o=n(34870),r=n(87367),a=n(52112);let l="";(0,a.MH)(t=>{l=t});var c={name:"ChannelKlarnaPopup",components:{Container:o.A},props:["option"],computed:{htmlUsername(){return`<span>${this.option.username}</span>`},rawValidate(){const t=this.inputCode;return 6===String(t).length},canResend(){return 0===this.leaveCount&&this.leaveTimes>0}},data(){return{inputCode:"",showHowToCatPage:!1,leaveCount:59,leaveTimes:this.option.remaining_verification_attempts||0,countInterval:"",loginValidationExpireCount:this.$gcbk("ids.loginValidationExpireCount",30)}},methods:{countTime(t){this.leaveCount=t||59,this.countInterval=setInterval(()=>{this.leaveCount--,0===this.leaveCount&&(clearInterval(this.countInterval),this.countInterval=null)},1e3)},resend(){this.$loading.show(),(0,r.UA)({fp_device_id:l,openid:this.option.openid}).then(t=>{const{code:e}=t;switch(e){case 0:this.leaveTimes=t.data.remaining_verification_attempts,this.countTime();break;default:this.$toast.err(this.$t("login-validation-error-code"))}}).finally(()=>this.$loading.hide())},async checkCode(){if(!this.rawValidate)return null;this.$loading.show(),(0,r.q7)({code:+this.inputCode,fp_device_id:l,openid:this.option.openid}).then(t=>{const{code:e}=t;switch(this.inputCode="",e){case 0:case 5011:{const t=this.option.successCb;t&&t(),this.$root.$emit("closePop");break}case 5009:this.$toast.err(this.$t("login-validation-error-expire"));break;case 5010:this.$toast.err(this.$t("login-validation-error-code"));break;default:this.$toast.err(this.$t("login-validation-error-text"))}}).finally(()=>this.$loading.hide())},close(){this.$root.$emit("closePop");const t=this.option.failCb;t&&t()},fixInput(t){const{data:e}=t,n=/^\d$/.test(e);"e"!==e&&"."!==e&&"E"!==e&&n||(t.target.value="");const i=this.inputCode.length;i>6&&(this.inputCode=this.inputCode.slice(0,6))}},created(){const t=this.option&&this.option.send_code_cd||59;this.countTime(t)},beforeDestroy(){this.countInterval&&(this.countInterval=null,clearInterval(this.countInterval))}},d=c,u=n(81656),h=(0,u.A)(d,i,s,!1,null,"6724528a",null),p=h.exports},77969:function(t){"use strict";t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2Jvb24tbG9naW4tYXdhcmQuNWU0YmYwMDYucG5nIjs="},78383:function(t){"use strict";t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2Jvb24taW5zdGFsbC1hd2FyZC5jZjUzNzZjMS5wbmciOw=="},79574:function(t){"use strict";t.exports="data:image/png;base64,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"},85195:function(t){"use strict";t.exports="data:image/png;base64,ZXhwb3J0IGRlZmF1bHQgX193ZWJwYWNrX3B1YmxpY19wYXRoX18gKyAic3RhdGljLzE3NTEyOTkyMDAvaW1nL2Jvb24tbG9naW4tYXdhcmQuOGVkZDU0ZjAucG5nIjs="},85429:function(t,e,n){"use strict";function i(t){return null!==t&&"object"===typeof t&&"constructor"in t&&t.constructor===Object}function s(t={},e={}){Object.keys(e).forEach(n=>{"undefined"===typeof t[n]?t[n]=e[n]:i(e[n])&&i(t[n])&&Object.keys(e[n]).length>0&&s(t[n],e[n])})}n.d(e,{YE:function(){return r},zk:function(){return l}});const o={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function r(){const t="undefined"!==typeof document?document:{};return s(t,o),t}const a={document:o,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return"undefined"===typeof setTimeout?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){"undefined"!==typeof setTimeout&&clearTimeout(t)}};function l(){const t="undefined"!==typeof window?window:{};return s(t,a),t}},98363:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return c}});var i=function(){var t=this;t._self._c;return t._m(0)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"copyright"},[e("p",[t._v(" ©Puzala Games Limited, All Rights Reserved "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://www.puzala.com/privacy-policy/",target:"_blank"}},[t._v("Privacy Policy")]),t._v(" , "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://www.puzala.com/terms-of-service/",target:"_blank"}},[t._v("Terms and Conditions")]),t._v(". ")])])}],o={name:"CommonFooterPuzala"},r=o,a=n(81656),l=(0,a.A)(r,i,s,!1,null,"12bb518e",null),c=l.exports}}]);