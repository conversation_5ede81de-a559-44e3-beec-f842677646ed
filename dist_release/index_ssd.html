<!doctype html><html lang=""><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><link rel="icon" href="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/favicon.ico"><title>State of Survival Top-up Centre</title><meta name="description" content="5% off for a limited time"><meta name="theme-color" content="white"><meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"/><meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" id="viewport" name="viewport"><script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js" defer="defer"></script><script src="https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/js/fake.min.20240805.js"></script><script>window.__ROUTERPATH = '/tilessurvive/'</script><script>window.__GAMENAME = 'ssd'
        window.fixStorage && window.fixStorage(window)
        sessionStorage.setItem('__GAMENAME', 'ssd')

        window.addEventListener('beforeinstallprompt', (e) => {
          // 防止 Chrome 67 及更早版本自动显示安装提示
          e.preventDefault()
          // 稍后再触发此事件
          window.__deferredPrompt = e
        })
        // if ('serviceWorker' in navigator) {
        //   navigator.serviceWorker.register('/service-worker.js', {
        //     scope: '/'
        //   }).then(function (registration) {
        //     console.log('ServiceWorker registration successful with scope: ', registration.scope);
        //   });
        // }</script><link rel="manifest" href="config/ssd.manifest.webmanifest" crossorigin="use-credentials"><script defer="defer" src="/res/js/chunk-koa-4c120c8f.3dd47e10.js"></script><script defer="defer" src="/res/js/chunk-tools-ef39e8ad.23d8c554.js"></script><script defer="defer" src="/res/js/chunk-tools-20043ecd.bba4d6de.js"></script><script defer="defer" src="/res/js/chunk-tools-a9a8c684.9c7f7e3c.js"></script><script defer="defer" src="/res/js/chunk-tools-ecbca18b.d6d89bd1.js"></script><script defer="defer" src="/res/js/chunk-vue-637fdb70.d06cce23.js"></script><script defer="defer" src="/res/js/chunk-vue-1656f0b4.c2a480dd.js"></script><script defer="defer" src="/res/js/chunk-vue-3bad0650.24482581.js"></script><script defer="defer" src="/res/js/chunk-vue-9f88573d.3a8ca3cb.js"></script><script defer="defer" src="/res/js/chunk-vendors-c0d76f48.3a5f4dc1.js"></script><script defer="defer" src="/res/js/chunk-vendors-b9fa02b6.df0f1b6a.js"></script><script defer="defer" src="/res/js/chunk-common-c3373795.845edadf.js"></script><script defer="defer" src="/res/js/chunk-common-42f9d7e6.46c4076d.js"></script><script defer="defer" src="/res/js/chunk-common-5c551db8.a3f11150.js"></script><script defer="defer" src="/res/js/ssd.02e0512f.js"></script><link href="/res/static/1751299200/css/chunk-koa-4c120c8f.5e621772.css" rel="stylesheet"><link href="/res/static/1751299200/css/chunk-koa-cdbd86b1.6c4f3fd7.css" rel="stylesheet"><link href="/res/static/1751299200/css/chunk-common-42f9d7e6.6f59e5fa.css" rel="stylesheet"></head><body><noscript><strong>We're sorry but State of Survival Top-up Centre doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div><script>(function () {
          window._smReadyFuncs = [];
          window.SMSdk = {
            onBoxDataReady: function (boxData) {},
            ready: function (fn) {
              fn && _smReadyFuncs.push(fn);
            }
          };
          window._smConf = {
            organization: 'xNxMh079HgFDeRkJE1qN',
            appId: 'web_sdk',
            publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtbekoieD6v30htpLAEPVp3w2nr9DRC8ElQu+qQfT+MPIU3K3Qc1FiF3gL0GDIKoTOXGuCXz/pVe7snZqcBh/8fsxpsQQMv/pSpzd6rLOthwwbvxLbWW06CU6SGXHBUDCYY+V/Y4GtsgwzhAf3Z0VZ/t0DXX8Yh/SaPXJuKJFn0wIDAQAB',
            apiHost: 'devproxy-web.kingsgroupgames.com'
            // apiHost: 'fp-devproxy-dev.nenglianghe.cn'
          };
      })();</script><script src="https://static.portal101.cn/dist/web/v3.0.0/fp.min.js"></script></body></html>