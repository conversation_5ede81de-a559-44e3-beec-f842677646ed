{"name": "web-pay-unique_common", "version": "0.2.0", "private": true, "scripts": {"serve:release": "NODE_ENV=development vue-cli-service serve --mode release", "serve:master": "NODE_ENV=development vue-cli-service serve --mode master", "build:release": "vue-cli-service build --mode release", "build:master": "vue-cli-service build --mode master", "build:online": "vue-cli-service build --mode online", "build:all": "npm run build:online & npm run build:release", "lint": "vue-cli-service lint", "analyze": "vue-cli-service build --report", "test:performance": "node scripts/performance-test.js"}, "dependencies": {"@adyen/adyen-web": "^5.59.0", "@airwallex/components-sdk": "^1.19.0", "@checkout.com/checkout-web-components": "^0.1.0-beta", "axios": "^1.6.2", "core-js": "^3.34.0", "crypto-js": "^4.2.0", "current-device": "^0.10.2", "swiper": "^8.4.7", "ua-parser-js": "^1.0.37", "vue": "^2.7.14", "vue-awesome-swiper": "^4.1.1", "vue-gtag": "^1.16.1", "vue-i18n": "^8.28.2", "vue-infinite-scroll": "^2.0.2", "vue-lazyload": "^1.3.5", "vue-router": "^3.6.5", "vuejs-paginate": "^2.1.0", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-standard": "^6.1.0", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^8.7.1", "image-webpack-loader": "^8.1.0", "node-xlsx": "^0.21.0", "postcss-pxtorem": "^6.0.0", "sass": "^1.69.5", "sass-loader": "^13.3.2", "vue-template-compiler": "^2.7.14", "webpack-bundle-analyzer": "^4.10.1", "webp-loader": "^0.6.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-console": "off", "no-debugger": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}