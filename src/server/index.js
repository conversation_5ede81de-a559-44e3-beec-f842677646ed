import { post, get } from '@/server/http'

export const getAmeDo = params => get('/ame/do', params)
export const getSSProductList = params => post('/api/sdk/coin_products', params)
export const getCurrencyByIp = params => get('/token/getIpCurrency', params)
export const changeFpidToOpenid = params => post('/account/fpid2openid', params)
export const fetchUidList = params => post('/account/uid_list', params)

/* 代币 */
export const getUserInfoForToken = params => post('/account/store/u', params)
export const getActivityListForToken = params => post('/token/act/init', params)

export const ameDoByGet = params => get('/ame/do', params)
export const ameHoldByGet = params => get('/ame/hold', params)
export const ameDoByGetCommon = params => get('/ameCommon/do', params)

export const getCommonInfo = params => post('/token/common/info', params)
export const sendCode = params => post('/account/store/send_code', params)
export const checkCode = params => post('/account/store/check_code', params)
export const toggleCouponType = params => post('/token/act/coupon/change/type', params)
export const toggleCouponSingle = params => post('/token/act/coupon/change/only_type', params)

export const getWxOpenid = wxCode => get('/ame/do', { p0: 'web', p1: 20, p2: 1113, p3: 'api', wx_code: wxCode })

const BasicRequestPath = {
  productList: '/token/products',
  channelList: '/token/channels',
  placeOrder: '/token/place_order',
  cardPlaceOrder: 'token/point_card_place_order',
  orderDetail: '/token/order',
  redirectProduct: '/token/pay_sdk/products',
  lastChosenChannel: '/token/user_pay_info'
}
const finalPath = Object.create(BasicRequestPath)

switch (window.__GAMENAME) {
  case 'koaCn': {
    BasicRequestPath.productList = '/token/cn/products'
    BasicRequestPath.channelList = '/token/cn/channels'
    break
  }
  case 'ssv': case 'ssv2': {
    BasicRequestPath.productList = '/token/store/coin_products'
    BasicRequestPath.channelList = '/token/store/channels'
    BasicRequestPath.placeOrder = '/token/store/coin_place_order'
    BasicRequestPath.cardPlaceOrder = '/token/store/point_card_place_order'
    BasicRequestPath.orderDetail = '/token/sdk/order'
    BasicRequestPath.lastChosenChannel = '/token/sdk/user_pay_info'
    break
  }
  case 'foundation': case 'ssRP': case 'ssCP': case 'stCP': case 'mcCP': case 'gogCP': case 'romCP': case 'stRP': case 'ssdRP': case 'moRP': case 'koaRP': {
    BasicRequestPath.redirectProduct = '/token/product'
  }
}

export const getTokenList = params => post(finalPath.productList, params)
export const getRedirectProductList = params => post(finalPath.redirectProduct, params)
export const getTokenChannelList = params => post(finalPath.channelList, params)
export const placeOrderToken = params => post(finalPath.placeOrder, params)
export const placeOrderCard = params => post(finalPath.cardPlaceOrder, params)
export const getTokenOrderDetails = params => post(finalPath.orderDetail, params)
export const getLastChosenChannel = params => get(finalPath.lastChosenChannel, params)
