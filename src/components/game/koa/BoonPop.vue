<template>
  <container :title="$t('boon-page-title') " class="arrears-reminder-wrapper" :class="[$i18n.locale, $gameName]">
    <div class="divider"></div>
    <div class="tab-wrapper" :class="{ 'tab-small': ['de', 'sv'].includes($i18n.locale) }">
      <div v-for="(tabItem, tabIndex) in tabList"
           :key="tabItem.dataKey"
           :class="['tab', {'dot-active': !($data[tabItem.dataKey] || $store.state.formdata[tabItem.dataKey]), 'chosen-active': chosenIndex === tabIndex}]"
           @click="chosenIndex = tabIndex">
        <span>{{ $t(tabItem.langKey) }}</span>
      </div>
    </div>
    <Swiper class="my-swiper-wrapper" :options="swiperOptions">
      <!--快捷-->
      <SwiperSlide key="install">
        <div class="charge-desc">
          <div class="row-1">{{ $t('boon-task-2-title') }}</div>
          <div class="row-2">{{ $t('boon-p1-title') }}</div>
        </div>
        <div class="gift-image">
          <template v-if="$gameName === 'koa'">
            <img v-for="i in [0,1,2,3]" :key="i" :src="require(`@/assets/koa/boon/install_reward_${i}.png`)" alt="">
          </template>
          <template v-else>
            <img v-for="i in [0,1,2,3]" :key="i" :src="require(`@/assets/koa/${$gameName === 'rom' ? 'aof' : $gameName}/boon/install_reward_${i}.png`)" alt="">
          </template>
        </div>
        <div class="login-reward-btn action-btn">
          <template v-if="!isLogin">
            <span class="click-btn" @click="focusInput">{{ $t('boon-login') }}</span>
          </template>
          <template v-else-if="!hadInstall">
            <p class="browser-forbidden" v-if="!calcShowInstall">{{ $t('boon-browser-forbidden') }}</p>
            <span v-else class="click-btn" @click="install">{{ $t('boon-task-2-add') }}</span>
          </template>
          <template v-else>
            <span class="forbidden" v-if="gotInstallReward"></span>
            <span class="todo click-btn" v-else @click="getReward(getPwaReward)">{{ $t('boon-gain') }}</span>
          </template>
        </div>
      </SwiperSlide>
      <!--登录-->
      <SwiperSlide key="login">
        <div class="charge-desc">
          <div class="row-1">{{ $t('boon-task-1-title') }}</div>
          <div class="row-2">{{ $t('boon-p1-title') }}</div>
        </div>
        <div class="gift-image">
          <template v-if="$gameName === 'koa'">
            <img v-for="i in [0,1,2,3]" :key="i" :src="require(`@/assets/koa/boon/login_reward_${i}.png`)" alt="">
          </template>
          <template v-else>
            <img v-for="i in [0,1,2,3]" :key="i" :src="require(`@/assets/koa/${$gameName === 'rom' ? 'aof' : $gameName}/boon/login_reward_${i}.png`)" alt="">
          </template>
        </div>
        <div class="login-reward-btn action-btn">
          <span v-if="!isLogin" class="click-btn" @click="focusInput">{{ $t('boon-login') }}</span>
          <span v-else-if="!hadLogin" class="click-btn" @click="focusInput">{{ $t('boon-go-charge-short') }}</span>
          <template v-else>
            <span class="forbidden" v-if="gotLoginReward"></span>
            <span class="todo click-btn" v-else @click="getReward(getLoginReward)">{{ $t('boon-gain') }}</span>
          </template>
        </div>
      </SwiperSlide>
      <!--每日登录-->
      <SwiperSlide key="daily-reward">
        <div class="charge-desc">
          <div class="row-1">{{ $t('text-login-topup') }}</div>
          <div class="row-2">{{ $t('text-claim-daily-chest') }}</div>
        </div>
        <div class="gift-image gift-image__daily-reward">
          <img :src="require('@/assets/koa/boon/daily-reward-image.png')" alt="">
        </div>
        <div class="login-reward-btn action-btn login-reward-btn__daily-reward">
          <span v-if="!isLogin" class="click-btn" @click="focusInput">{{ $t('boon-login') }}</span>
          <template v-else>
            <span :class="['forbidden', 'forbidden__daily-reward']" v-if="gotDailyReward"></span>
            <span class="todo click-btn" v-else @click="getDailyReward">{{ $t('btn-open-now') }}</span>
          </template>
        </div>

        <div v-if="!gotDailyReward" class="tips">*{{ $t('subtitle-daily-rewards-boon') }}</div>
      </SwiperSlide>
    </Swiper>
  </container>
</template>

<script>
import Container from '@/components/pop/container.vue'
import { getAmeDo, ameHoldByGet, ameDoByGet } from '@/server'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.min.css'

import UAParser from 'ua-parser-js'
import { mapState } from 'vuex'
import { numberFormat } from '@/utils/utils'
const { projectId, loginAction, getLoginReward, pwaOpenAction, getPwaReward } = window.$gcbk('apiParams.boonAme', {})

const ameParams = { p0: 'web', p1: projectId }
const numsMap = {
  500: 50,
  2000: 200,
  5000: 400,
  10000: 750,
  20000: 1200,
  50000: 2000
}
function displayMode () {
  const isStandalone = window.matchMedia('(display-mode: standalone)').matches
  if (document.referrer.startsWith('android-app://')) {
    return 'twa'
  } else if (navigator.standalone || isStandalone) {
    return 'standalone'
  }
  return 'browser'
}

const tabList = [
  // {
  //   dataKey: 'gotSeasonBenefitsReward',
  //   langKey: 'season-benefits-tab-txt'
  // },
  // {
  //   dataKey: 'gotTopupReward',
  //   langKey: 'koa-topup-short-title'
  // },
  // {
  //   dataKey: 'goTurntable',
  //   langKey: 'boon-koa-turntable'
  // },
  {
    dataKey: 'gotInstallReward',
    langKey: 'boon-ss-install-title'
  },
  {
    dataKey: 'gotLoginReward',
    langKey: 'boon-ss-login-title'
  },
  {
    dataKey: 'gotDailyReward',
    langKey: 'btn-daily-rewards'
  }
]

export default {
  name: 'BoonPop',
  components: { Container, Swiper, SwiperSlide },
  data () {
    const instance = this
    return {
      chargeStatus: {},
      recharge: 0,

      hadLogin: false, // 1097
      gotLoginReward: false, // 1098
      hadInstall: false, // 1099
      gotInstallReward: false, // 1100
      goTurntable: false,
      // gotSeasonBenefitsReward: false, // 1153,
      deferredPrompt: window.__deferredPrompt || undefined,
      showMobileSafariGuide: false,

      progressPercent: 0,
      numsMap,

      chosenIndex: 0,
      swiperInstance: undefined,
      swiperOptions: {
        autoplay: false,
        on: {
          slideChangeTransitionStart: function () {
            instance.chosenIndex = this.activeIndex
          },
          init: function () {
            // Swiper初始化了
            instance.swiperInstance = this
          }
        }
      },
      tabList,
      getLoginReward,
      getPwaReward,
      missionList: [],
      serverId: 0,

      gotToggleCoupon: true
    }
  },
  methods: {
    getImgUrl (imageId) {
      // return require(`@/assets/koa/topup/${imageId}.png`)
    },
    numberFormat (num) {
      return numberFormat(num)
    },
    onRecive (id) {
      getAmeDo({
        p0: 'web',
        p1: 71,
        p2: 1626,
        id: id
      }).then(res => {
        const { code } = res
        if (code === 0) {
          const item = this.missionList.filter(list => list.id === id)
          const index = this.missionList.findIndex(list => list.id === id)
          item[0].status = 2
          this.$set(this.missionList, index, item[0])
        }
      })
    },
    showInstallPart () {
      window.addEventListener('beforeinstallprompt', (e) => {
        // 防止 Chrome 67 及更早版本自动显示安装提示
        e.preventDefault()
        // 稍后再触发此事件
        this.deferredPrompt = e
      })
    },
    resetStatus () {
      const params = {
        p0: 'web',
        p1: projectId,
        p2: `${loginAction},${getLoginReward},${pwaOpenAction},${getPwaReward}`
      }
      this.$loading.show()
      ameHoldByGet(params)
        .then(res => {
          const { data, code } = res
          if (code === 0) {
            const result = {}
            for (const value of Object.values(data)) result[value.task_id] = value

            this.hadLogin = loginAction in result
            this.gotLoginReward = getLoginReward in result
            this.hadInstall = pwaOpenAction in result
            this.gotInstallReward = getPwaReward in result
            // this.gotSeasonBenefitsReward = '1153' in result
          }
        })
        .finally(() => this.$loading.hide())
    },
    resetTurntable () {
      const params = {
        p0: 'web',
        p1: 79,
        p2: 1711
      }
      this.$loading.show()
      ameDoByGet(params)
        .then(res => {
          const { data, code } = res
          if (code === 0) {
            const ticket = data.score.filter((item) => item.score_id == '10085')[0]
            this.goTurntable = !(ticket.ticket > 0 || ticket.total == 0)
          }
        })
        .finally(() => this.$loading.hide())
    },
    // 领取奖励
    getReward (taskId) {
      const params = { p2: taskId }
      this.$loading.show()
      getAmeDo({ ...params, ...ameParams })
        .then(res => {
          const { code, data = [] } = res
          if (code === 0 && data.length) {
            if (taskId === getPwaReward) this.gotInstallReward = true
            if (taskId === getLoginReward) this.gotLoginReward = true
            // if (taskId === 1153) this.gotSeasonBenefitsReward = true
          }
        })
        .finally(() => this.$loading.hide())
    },
    // 领取奖励
    getDailyReward () {
      this.$loading.show()
      getAmeDo({ p0: 'web', p1: 11, p2: 1422 })
        .then(res => {
          const { code, data = [] } = res
          if (code === 0 && data.length) {
            this.$store.commit('formdata/setDailyRewardStatus', true)
            this.$root.$emit('closePop')

            setTimeout(() => {
              this.$root.$emit('showPop', 'DailyReward', { reward: data[0] })
            }, 0)
          } else {
            this.$toast.err(this.$t('network_err'))
          }
        })
        .catch(() => this.$toast.err(this.$t('network_err')))
        .finally(() => this.$loading.hide())
    },
    focusInput () {
      this.$root.$emit('closePop')
      this.$root.$emit('ClickPayButNotLogin')
      // const input = document.querySelector('#uidInput')
      // setTimeout(() => {
      //   input.focus()
      // }, 100)
    },
    install () {
      this.$root.$emit('closePop')
      // 如果是谷歌浏览器
      if (this.deferredPrompt) {
        this.deferredPrompt.prompt()
        // 等待用户反馈
        this.deferredPrompt.userChoice
          .then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
              console.log('User accepted the A2HS prompt')

              const displayModeCheck = setInterval(() => {
                if (displayMode() === 'standalone') {
                  clearInterval(displayModeCheck)
                  this.$root.$emit('installSuccessful')
                }
              }, 1000)
            } else {
              console.log('User dismissed the A2HS prompt')
            }
            this.deferredPrompt = undefined
          })
      } else {
        setTimeout(() => {
          this.$root.$emit('mobileSafariGuide')
        }, 500)
      }
    },
    // goTurntablePage() {
    //   let finalUrl = `${process.env.VUE_APP_URL_KOA_TURNTABLE}?l=${this.$i18n.locale}`
    //   if(localStorage.getItem('openid')) {
    //     finalUrl += `&openid=${encodeURIComponent(localStorage.getItem('openid'))}`
    //   }
    //   window.open(finalUrl, '_blank')
    // }
    goToggleActivityPage () {
      let finalUrl = `${process.env.VUE_APP_URL_KOA_TOGGLE_COUPON}?l=${this.$i18n.locale}`
      if (localStorage.getItem('openid')) {
        finalUrl += `&openid=${encodeURIComponent(localStorage.getItem('openid'))}`
      }
      window.open(finalUrl, '_blank')
    }
  },
  computed: {
    ...mapState('userinfo', ['isLogin']),
    ...mapState(['userinfo']),
    ...mapState('formdata', ['gotDailyReward', 'koaTopupEnable']),
    calcShowInstall () {
      const parser = new UAParser(navigator.userAgent)
      const result = parser.getResult()
      const { browser } = result

      const whiteBrowser = browser.name === 'Mobile Safari' || ((browser.name || '').includes('Chrome') && this.deferredPrompt)
      if (!this.isLogin) return true
      if (!this.hadInstall) return whiteBrowser
      return true
    }
  },
  created () {
    this.$root.$on('loginSuccess', () => {
      setTimeout(() => this.resetStatus(), 2000)
    })
    if (this.isLogin) this.resetStatus()
    this.$watch('chosenIndex', (index) => {
      this.swiperInstance.slideTo(index, 200, false)
    })

    this.showInstallPart()
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.arrears-reminder-wrapper {
  background-color: #383838;
  padding-top: 18px!important;
  background-image: url("~@/assets/koa/boon/boon-pop-bg_m.png");
  background-position: bottom center;
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .divider{
    width: 100%;
    height: 2px;
    background: #D8D8D8;
    opacity: 0.2;
    margin: 18px auto 0;
  }

  .tab-wrapper{
    display: flex;
    align-items: center;
    justify-content: space-around;

    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #AEAEAE;
    padding-top: 16px;
    line-height: 1.1;

    &.tab-small {
      font-size: 18px;
    }

    .tab{
      min-height: 33px;
      padding: 3px 10px;
      align-items: center;
      justify-content: center;
      display: inline-block;
      cursor: pointer;
    }

    .dot-active{
      span{
        display: inline-block;
        position: relative;

        &:after{
          display: inline-block;
          content: ' ';
          width: 10px;
          height: 10px;
          background: #D20202;
          border-radius: 50%;
          position: absolute;
          top: 0;
          right: 0;
          transform: translate(100%, -100%);
        }
      }
    }

    .chosen-active{
      position: relative;
      span {
        color: #ffffff;
      }

      &:before{
        display: inline-block;
        content: ' ';
        width: 70px;
        height: 4px;
        background: rgba(235, 170, 36, 1);
        border-radius: 2px;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 100%);
      }
    }
  }

  .my-swiper-wrapper{
    //.turntable {
    //  position: relative;
    //  display: flex;
    //  height: 390PX;
    //  align-items: center;
    //  justify-content: center;
    //  flex-direction: column;
    //  .turntable-banner {
    //    position: absolute;
    //    top: -20px;
    //    @include bgCenter('koa/turntable/banner.png',330PX, 390PX);
    //  }
    //  .turntable-button {
    //    position: absolute;
    //    bottom: 40px;
    //    display: flex;
    //    align-items: center;
    //    justify-content: center;
    //    font-weight: bold;
    //    // font-size: 20PX;
    //    padding-bottom: 2px;
    //    transition: all 0.2s;
    //    color: #410B00;
    //    cursor: pointer;
    //    @include bgCenter('koa/turntable/btn.png',200PX, 38PX);
    //
    //    &:hover {
    //      transform: scale(0.95);
    //    }
    //  }
    //}
    .charge-desc{
      font-size: 40px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #D4B880;
      line-height: 56px;
      padding-top: 48px;

      .row-1{
        color: rgba(235, 170, 36, 1);
      }
      .row-2{
        color: white;
      }
    }

    .gift-image{
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      width: 380px;
      margin: 50px auto 0;
      img{
        display: inline-block;
        width: 188px;
        height: auto;
      }
    }

    .gift-image__daily-reward {
      margin-top: 110px;
      img{
        width: 570px;
      }

      & + .login-reward-btn{
        .click-btn{
          margin-top: 120px;
        }
      }
    }
    .topup-title {
      color:#fff;
      padding: 20px
    }
    //.mission-item{
    //  margin: 0 auto;
    //  width: 632px;
    //  height: 124px;
    //  margin-bottom: 16px;
    //  background-image: url("~@/assets/koa/mission-item-bg.png");
    //  background-position: center center;
    //  background-repeat: no-repeat;
    //  background-size: 100% 100%;
    //  .mission-title{
    //    color: #fff;
    //    width: 100%;
    //    height: 42px;
    //    padding: 10px 0px 0px 20px;
    //    line-height: 42px;
    //    font-size: 20px;
    //    line-height: 22px;
    //    text-align: left;
    //    display: flex;
    //    align-items: center;
    //    ::v-deep {
    //      em {
    //        margin-left: 5px;
    //        display: inline-block;
    //        width: 21px;
    //        height: 18px;
    //        background-image: url("~@/assets/koa/boon-diamond.png");
    //        background-position: center center;
    //        background-repeat: no-repeat;
    //        background-size: 100% 100%;
    //      }
    //    }
    //  }
    //  .mission-content{
    //    width: 100%;
    //    color: #fff;
    //    padding: 10px 20px;
    //    display: flex;
    //    align-items: center;
    //    justify-content: space-between;
    //  }
    //  .awards {
    //    display: flex;
    //    align-items: center;
    //    justify-content: flex-start;
    //  }
    //  .award{
    //    position: relative;
    //    // @include flex-center($justify-content: flex-start);
    //    .award-view{
    //      width: 60px;
    //      height: 60px;
    //      margin-right: 10px;
    //      img {
    //        width: 60px;
    //        height: 60px;
    //      }
    //      // @include bg('icon-counter.png');
    //    }
    //    .award-quantity{
    //      font-size: 10px;
    //      text-align: right;
    //      position: absolute;
    //      height: 16px;
    //      line-height: 14px;
    //      padding-right: 2px;
    //      left: 2px;
    //      bottom: 0px;
    //      width: 56px;
    //      background: rgba(0,0,0,0.4);
    //    }
    //  }
    //  .btn-group{
    //    .btn{
    //      // width: 198px;
    //      // height: 58px;
    //      width: 140px;
    //      height: 40px;
    //      padding: 0 4px;
    //      font-size: 16px;
    //      font-weight: 500;
    //      color: #FFFFFF;
    //      display: flex;
    //      align-items: center;
    //      justify-content: center;
    //      line-height: 16px;
    //      // @include flex-center();
    //      &.disable{
    //        pointer-events: none;
    //        filter: grayscale(1);
    //      }
    //    }
    //    .btn-receive{
    //      color: #582D05;
    //      background-image: url("~@/assets/koa/btn-topup-receive.png");
    //      background-position: center center;
    //      background-repeat: no-repeat;
    //      background-size: 100% 100%;
    //    }
    //    .btn-received{
    //      background-image: url("~@/assets/koa/btn-topup-received.png");
    //      background-position: center center;
    //      background-repeat: no-repeat;
    //      background-size: 100% 100%;
    //    }
    //    .btn-unfinished{
    //      cursor: pointer;
    //      background-image: url("~@/assets/koa/btn-topup-unfinished.png");
    //      background-position: center center;
    //      background-repeat: no-repeat;
    //      background-size: 100% 100%;
    //    }
    //
    //  }
    //
    //}

    //.toggle-poster{
    //  @include bgCenter('koa/boon-pop-toggle-coupon-m.png', 603px, 358px);
    //  margin: 100px auto 50px;
    //}
  }

  .action-btn{
    position: relative;
    min-height: 110px;
    span{
      margin: 45px auto 0;
      padding: 0 20px;
      background-color: #C58833;
      @include bgCenter('koa/boon/boon_btn.png',312px, 70px);
      min-width: 300px;

      font-size: 30px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #633B00;
      line-height: 70px;
      display: inline-block;

      &.forbidden{
        background-color: transparent;
        @include bgCenter('koa/boon/boon-award-get.png',349px, 263px);
        margin-top: 0;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: -160px;
      }
    }

    .browser-forbidden{
      font-size: 22px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #EED16D;
      line-height: 30px;
      margin-top: 56px;
      padding: 0 43px;
    }
  }

  .login-reward-btn__daily-reward{
    height: 220px;

    .forbidden__daily-reward{
      top: -60px!important;
    }
  }

  .tips{
    font-size: 20px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #AEAEAE;
    line-height: 28px;
    margin-top: 20px;
    margin-bottom: 30px;
  }

  ::v-deep{
    padding-bottom: 14px;
    .content{
      margin-top: 0;
      padding-left: 20px;
      padding-right: 20px;
      max-height: none;
    }
    .footer-wrapper{
      display: none;
    }

    .swiper-slide{
      max-height: 870px;
      overflow-y: scroll;
    }
  }
}
@include setPcContent{
  .arrears-reminder-wrapper {
    padding-top: 27PX !important;
    background-image: url("~@/assets/koa/boon/boon-pop-bg_pc.png");
    background-position: bottom center;

    .divider {
      margin-top: 21PX;
      height: 2PX;
    }

    .tab-wrapper {
      font-size: 18PX;
      line-height: 25PX;
      padding-top: 14PX;

      .tab{
        min-height: 25PX;
        padding: 3PX 10PX;
        margin: 0 0;
      }

      .dot-active{
        span{
          &:after{
            width: 8PX;
            height: 8PX;
          }
        }
      }

      .chosen-active{
        color: white;
        &:before{
          width: 50PX;
          height: 4PX;
          border-radius: 2PX;
        }
      }
    }

    .my-swiper-wrapper{
      margin-top: 6PX;
      //.turntable {
      //  position: relative;
      //  display: flex;
      //  height: 446PX;
      //  align-items: center;
      //  justify-content: center;
      //  flex-direction: column;
      //  .turntable-banner {
      //    position: absolute;
      //    top: 0px;
      //    @include bgCenter('koa/turntable/banner.png',400PX, 446PX);
      //  }
      //  .turntable-button {
      //    position: absolute;
      //    bottom: 10px;
      //    display: flex;
      //    align-items: center;
      //    justify-content: center;
      //    font-weight: bold;
      //    // font-size: 20PX;
      //    padding-bottom: 2px;
      //    transition: all 0.2s;
      //    color: #410B00;
      //    cursor: pointer;
      //    @include bgCenter('koa/turntable/btn.png',250PX, 47PX);
      //
      //    &:hover {
      //      transform: scale(0.95);
      //    }
      //  }
      //}
      .charge-desc{
        font-size: 26PX;
        line-height: 32PX;
        padding-top: 36PX;
      }
      .gift-image{
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 60PX auto 0;
        flex-wrap: nowrap;
        img {
          width: 126PX;
        }
      }

      .gift-image__daily-reward {
        margin-top: 50px;
        img{
          width: 480px;
        }

        & + .login-reward-btn{
          .click-btn{
            margin-top: 60px;
          }
        }
      }
      .mission-item {
        width: 610px;
      }

      //.toggle-poster{
      //  @include bgCenter('koa/boon-pop-toggle-coupon-m.png', 545px, 327px);
      //  margin: 20px auto 0;
      //
      //  & + .action-btn .click-btn{
      //    margin-top: 30px;
      //  }
      //}
    }

    .action-btn{
      span{
        margin-top: 60PX;
        padding: 0 20PX;
        min-width: 200PX;
        line-height: 50PX;
        font-size: 20PX;
        @include bgCenter('koa/boon/boon_btn.png',220PX, 50PX);
        cursor: pointer;

        &.forbidden{
          @include bgCenter('koa/boon/boon-award-get.png',243PX, 184PX);
          top: -120PX;
        }
      }
    }

    .login-reward-btn__daily-reward{
      height: 110px;

      .forbidden__daily-reward{
        top: -80px!important;
      }
    }

    .browser-forbidden{
      font-size: 16PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FF5E0F;
      line-height: 22PX;
      margin-top: 37PX;
      padding: 0 43PX;
    }

    ::v-deep{
      padding-bottom: 14px;
      .content{
        margin-top: 0;
        padding-left: 20PX;
        padding-right: 20PX;
      }
      .footer-wrapper{
        display: none;
      }

      .swiper-slide{
        max-height: 500PX;
        overflow-y: scroll;
        height: 100%;
      }

      .title{
        font-size: 24PX;
        line-height: 33PX;
        i{
          top:29PX;
          right: 16PX;
          height: 26PX;
          width: 26PX;
        }
      }
    }
  }
}

.arrears-reminder-wrapper.aof{
  background-image: url("~@/assets/koa/boon/boon-pop-bg_m.png");
  .action-btn{
    span{
      @include bgCenter('koa/boon/boon_btn.png',312px, 70px);

      &.forbidden{
        @include bgCenter('koa/boon/boon-award-get.png',349px, 263px);
      }
    }
  }
}
@include setPcContent{
  .arrears-reminder-wrapper.aof{
    background-image: url("~@/assets/koa/boon/boon-pop-bg_pc.png");

    .action-btn{
      span{
        @include bgCenter('koa/boon/boon_btn.png',220PX, 50PX);

        &.forbidden{
          @include bgCenter('koa/boon/boon-award-get.png',243PX, 184PX);
        }
      }
    }
  }
}

.arrears-reminder-wrapper.rom{
  @extend .aof;
}
</style>
