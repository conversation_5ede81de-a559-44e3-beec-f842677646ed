<template>
  <Swiper class="my-swiper-wrapper" :class="[$gameName]" :options="swiperOptions">
    <template v-if="webCashierBanner.length">
      <SwiperSlide v-for="banner in webCashierBanner" :key="banner.imageUrl">
        <img :src="banner.imageUrl" alt="">
        <a v-if="banner.jumpUrl" :href="banner.jumpUrl" target="_blank"></a>
      </SwiperSlide>

      <div class="swiper-pagination" slot="pagination">
        <div :class="['pagination-dot', {'pagination-dot_active': swiperIndex === +index}]" :key="index" v-for="index in Object.keys(webCashierBanner)"></div>
      </div>
    </template>
  </Swiper>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.min.css'
export default {
  name: 'PosterSwiper',
  data () {
    const instance = this
    return {
      swiperOptions: {
        autoplay: {
          disableOnInteraction: false
        },
        on: {
          slideChangeTransitionStart: function () {
            instance.swiperIndex = this.activeIndex
          }
        }
      },
      swiperIndex: 0,
      webCashierBanner: this.$imageLoader('whatsDiamond', [])
    }
  },
  components: {
    Swiper,
    SwiperSlide
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.my-swiper-wrapper {
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  padding-bottom: 18px;

  ::v-deep {
    .swiper-slide {
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        display: inline-block;
      }

      a {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        z-index: 10;
        display: inline-block;
      }
    }
  }

  .swiper-pagination {
    position: absolute;
    z-index: 10;
    display: flex;
    align-items: center;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);

    .pagination-dot {
      width: 8px;
      height: 8px;
      background: #6F6F6F;
      border-radius: 50%;
      margin: 0 3px;
    }

    .pagination-dot_active {
      background: #C0C0C0;;
    }
  }

  @include setPcContent{
    padding-bottom: 17PX;
    border-radius: 4PX;

    .swiper-pagination{

      .pagination-dot{
        height: 7PX;
        width: 7PX;
      }
    }
  }
}

.my-swiper-wrapper.dc{
  border-radius: 0;

  .swiper-pagination {
    .pagination-dot_active {
      background: #525280;;
    }
  }
}
</style>
